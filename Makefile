# 项目Makefile - 支持C++编译和自定义输出路径

# 项目根目录
PROJECT_ROOT := $(shell pwd)

# 输出目录
BIN_DIR := $(PROJECT_ROOT)/bin
TEST_BIN_DIR := $(BIN_DIR)/Tests
LIB_DIR := $(PROJECT_ROOT)/lib
BUILD_DIR := $(PROJECT_ROOT)/build

# 编译器和工具
CC := gcc
CXX := g++
AR := ar
RM := rm -rf
MKDIR := mkdir -p

# 编译选项
CFLAGS := -Wall -O2 -fPIC -I$(PROJECT_ROOT)/src/modules
CXXFLAGS := $(CFLAGS) -std=c++11  # C++11支持
LDFLAGS := -L$(LIB_DIR)

# 第三方库路径
EXTERNAL_DIR := $(PROJECT_ROOT)/external
OPENCV_DIR := $(EXTERNAL_DIR)/opencv
WIRINGPI_DIR := $(EXTERNAL_DIR)/wiringPi
PAHO_DIR := $(EXTERNAL_DIR)/paho.mqtt.c

# 检查并输出库版本
define CHECK_LIB_VERSION
    @echo "检查 $1 库版本..."
    @if pkg-config --exists $1 2>/dev/null; then \
        echo "  $1 版本: $$(pkg-config --modversion $1)"; \
    elif [ -f "$2/$1.pc" ]; then \
        echo "  $1 版本: $$(pkg-config --modversion $1 2>/dev/null || echo '未找到')"; \
    elif [ -f "$2/lib$1.so" ]; then \
        echo "  $1 版本: $$(readelf -d $2/lib$1.so 2>/dev/null | grep SONAME | awk '{print $$5}' || echo '未知')"; \
    elif ldconfig -p 2>/dev/null | grep -q "lib$1.so"; then \
        echo "  $1 版本: 系统已安装"; \
    else \
        echo "  $1 库未找到"; \
    fi
endef

# 输出所有依赖库版本
check_deps:
	$(call CHECK_LIB_VERSION,opencv4,$(OPENCV_DIR)/lib)
	$(call CHECK_LIB_VERSION,wiringPi,$(WIRINGPI_DIR)/lib)
	$(call CHECK_LIB_VERSION,paho-mqtt3c,$(PAHO_DIR)/lib)

# 基础链接库
LDLIBS := -lpthread -lm

# 自动检测模块列表
MODULES := $(notdir $(wildcard $(PROJECT_ROOT)/src/modules/*))

# 为每个模块生成源文件、目标文件和依赖文件列表
define MODULE_template
  SRCS_$(1) := $$(wildcard src/modules/$(1)/src/*.c)
  OBJS_$(1) := $$(patsubst src/modules/$(1)/src/%.c,$$(BUILD_DIR)/$(1)/%.o,$$(SRCS_$(1)))
  DEPS_$(1) := $$(patsubst %.o,%.d,$$(OBJS_$(1)))
  LIB_$(1) := $$(LIB_DIR)/lib$(1).so

  $$(LIB_$(1)): $$(OBJS_$(1)) | $$(LIB_DIR)
	@echo "正在链接模块库: $(1)"
	$$(CC) $$(CFLAGS) -shared -o $$@ $$^ $$(LDFLAGS) $$(LDLIBS)
	@echo "模块库 $(1) 构建完成: $$@"

  $$(BUILD_DIR)/$(1)/%.o: src/modules/$(1)/src/%.c | $$(BUILD_DIR)/$(1)
	@echo "正在编译: $$<"
	$$(CC) $$(CFLAGS) -I$$(PROJECT_ROOT)/src/modules/$(1)/include -MMD -MP -c -o $$@ $$<

  $$(BUILD_DIR)/$(1):
	$$(MKDIR) $$@
endef

# 为每个模块应用模板
$(foreach mod,$(MODULES),$(eval $(call MODULE_template,$(mod))))

# 主程序源文件
MAIN_SRC := $(wildcard src/*.cpp)
MAIN_OBJS := $(patsubst src/%.cpp,$(BUILD_DIR)/main/%.o,$(MAIN_SRC))
MAIN_DEPS := $(patsubst %.o,%.d,$(MAIN_OBJS))

# 测试源文件 - 自动检测tests目录下的所有C/C++文件
TEST_SRCS_C := $(wildcard tests/*.c)
TEST_SRCS_CPP := $(wildcard tests/*.cpp)
TEST_SRCS := $(TEST_SRCS_C) $(TEST_SRCS_CPP)

# 生成对应的目标文件
TEST_OBJS := $(patsubst tests/%.c,$(BUILD_DIR)/tests/%.o,$(TEST_SRCS_C)) \
             $(patsubst tests/%.cpp,$(BUILD_DIR)/tests/%.o,$(TEST_SRCS_CPP))

# 生成依赖文件
TEST_DEPS := $(patsubst %.o,%.d,$(TEST_OBJS))

# 生成可执行文件名（去掉扩展名）
TEST_EXES := $(addprefix $(TEST_BIN_DIR)/, $(notdir $(patsubst %.c,%,$(TEST_SRCS_C)))) \
             $(addprefix $(TEST_BIN_DIR)/, $(notdir $(patsubst %.cpp,%,$(TEST_SRCS_CPP))))

# 主可执行文件名称
MAIN_TARGET := $(BIN_DIR)/GreenLand

# 默认目标
all: $(MAIN_TARGET) $(TEST_EXES)
	@echo "==== 构建完成 ===="
	@echo "主程序: $(MAIN_TARGET)"
	@echo "生成的库文件:"
	@ls -l $(LIB_DIR)/*.so 2>/dev/null || echo "无库文件"
	@echo "生成的测试程序:"
	@ls -l $(TEST_BIN_DIR)/* 2>/dev/null || echo "无测试程序"
	@echo "所有中间文件位于: $(BUILD_DIR)"

# 创建必要的目录
$(BIN_DIR) $(TEST_BIN_DIR) $(LIB_DIR) $(BUILD_DIR)/main $(BUILD_DIR)/tests:
	$(MKDIR) $@

# 编译主程序
$(MAIN_TARGET): $(MAIN_OBJS) $(addprefix $(LIB_DIR)/lib,$(addsuffix .so,$(MODULES))) | $(BIN_DIR)
	@echo "正在链接主程序..."
	$(CXX) $(CXXFLAGS) -o $@ $(MAIN_OBJS) $(LDFLAGS) -Wl,-rpath,$(PROJECT_ROOT)/lib $(addprefix -l,$(MODULES)) $(LDLIBS)
	@echo "主程序构建完成: $@"

# 自动生成所有模块的头文件路径
MODULE_INCLUDES := $(addprefix -I$(PROJECT_ROOT)/src/modules/,$(addsuffix /include,$(MODULES)))

# 编译C++主程序源文件
$(BUILD_DIR)/main/%.o: src/%.cpp | $(BUILD_DIR)/main
	@echo "正在编译C++文件: $<"
	$(CXX) $(CXXFLAGS) $(MODULE_INCLUDES) -MMD -MP -c -o $@ $<

# 定义链接库的函数 - 自动检测模块
define get_test_libs
$(if $(findstring wiringpi,$1),-lwiringPi -lpthread -lm,\
$(if $(findstring opencv,$1),`pkg-config --libs opencv4` -lpthread,\
$(if $(findstring mqtt,$1),-lpaho-mqtt3c -lpthread,\
$(if $(strip $(foreach mod,$(MODULES),$(findstring $(mod),$1))),$(LDFLAGS) $(addprefix -l,$(MODULES)) $(LDLIBS),\
-lpthread))))
endef

# 定义编译器选择函数
define get_test_compiler
$(if $(findstring .cpp,$(suffix $(patsubst $(TEST_BIN_DIR)/%,%,$1))),$(CXX) $(CXXFLAGS),$(CC) $(CFLAGS))
endef

# 传感器测试程序链接规则 (需要模块库)
$(TEST_BIN_DIR)/%_test: $(BUILD_DIR)/tests/%_test.o $(addprefix $(LIB_DIR)/lib,$(addsuffix .so,$(MODULES))) | $(TEST_BIN_DIR)
	@echo "正在链接传感器测试程序: $@"
	$(CC) $(CFLAGS) -o $@ $< $(LDFLAGS) -Wl,-rpath,$(PROJECT_ROOT)/lib $(addprefix -l,$(MODULES)) $(LDLIBS)
	@echo "测试程序构建完成: $@"

$(TEST_BIN_DIR)/%_simple_test: $(BUILD_DIR)/tests/%_simple_test.o $(addprefix $(LIB_DIR)/lib,$(addsuffix .so,$(MODULES))) | $(TEST_BIN_DIR)
	@echo "正在链接传感器简单测试程序: $@"
	$(CC) $(CFLAGS) -o $@ $< $(LDFLAGS) -Wl,-rpath,$(PROJECT_ROOT)/lib $(addprefix -l,$(MODULES)) $(LDLIBS)
	@echo "测试程序构建完成: $@"

# 单元测试程序链接规则 (需要测试框架和模块库)
$(TEST_BIN_DIR)/%_unit_test: $(BUILD_DIR)/tests/%_unit_test.o $(BUILD_DIR)/tests/test_framework.o $(addprefix $(LIB_DIR)/lib,$(addsuffix .so,$(MODULES))) | $(TEST_BIN_DIR)
	@echo "正在链接单元测试程序: $@"
	$(CC) $(CFLAGS) -o $@ $< $(BUILD_DIR)/tests/test_framework.o $(LDFLAGS) -Wl,-rpath,$(PROJECT_ROOT)/lib $(addprefix -l,$(MODULES)) $(LDLIBS)
	@echo "测试程序构建完成: $@"

# 集成测试和性能测试程序链接规则
$(TEST_BIN_DIR)/integration_test: $(BUILD_DIR)/tests/integration_test.o $(BUILD_DIR)/tests/test_framework.o $(addprefix $(LIB_DIR)/lib,$(addsuffix .so,$(MODULES))) | $(TEST_BIN_DIR)
	@echo "正在链接集成测试程序: $@"
	$(CC) $(CFLAGS) -o $@ $< $(BUILD_DIR)/tests/test_framework.o $(LDFLAGS) -Wl,-rpath,$(PROJECT_ROOT)/lib $(addprefix -l,$(MODULES)) $(LDLIBS) -lpthread
	@echo "测试程序构建完成: $@"

$(TEST_BIN_DIR)/performance_test: $(BUILD_DIR)/tests/performance_test.o $(BUILD_DIR)/tests/test_framework.o $(addprefix $(LIB_DIR)/lib,$(addsuffix .so,$(MODULES))) | $(TEST_BIN_DIR)
	@echo "正在链接性能测试程序: $@"
	$(CC) $(CFLAGS) -o $@ $< $(BUILD_DIR)/tests/test_framework.o $(LDFLAGS) -Wl,-rpath,$(PROJECT_ROOT)/lib $(addprefix -l,$(MODULES)) $(LDLIBS) -lpthread
	@echo "测试程序构建完成: $@"

# 水位测试程序链接规则
$(TEST_BIN_DIR)/water_level_test: $(BUILD_DIR)/tests/water_level_test.o $(addprefix $(LIB_DIR)/lib,$(addsuffix .so,$(MODULES))) | $(TEST_BIN_DIR)
	@echo "正在链接水位测试程序: $@"
	$(CC) $(CFLAGS) -o $@ $< $(LDFLAGS) -Wl,-rpath,$(PROJECT_ROOT)/lib -lhcsr04 $(LDLIBS)
	@echo "测试程序构建完成: $@"

# 通用测试程序链接规则 - 其他测试 (排除test_framework)
$(TEST_BIN_DIR)/%: $(BUILD_DIR)/tests/%.o | $(TEST_BIN_DIR)
	@echo "正在链接测试程序: $@"
	@if [ "$*" = "test_framework" ]; then \
		echo "跳过test_framework，它是库文件不是可执行文件"; \
	elif [ -f "tests/$*.c" ]; then \
		if echo "$*" | grep -q "wiringpi"; then \
			$(CC) $(CFLAGS) -o $@ $< $(LDFLAGS) -lwiringPi -lpthread -lm; \
		elif echo "$*" | grep -q "mqtt"; then \
			$(CC) $(CFLAGS) -o $@ $< $(LDFLAGS) -lpaho-mqtt3c -lpthread; \
		else \
			$(CC) $(CFLAGS) -o $@ $< $(LDFLAGS) -lpthread; \
		fi; \
	elif [ -f "tests/$*.cpp" ]; then \
		if echo "$*" | grep -q "opencv"; then \
			$(CXX) $(CXXFLAGS) -o $@ $< $(LDFLAGS) `pkg-config --libs opencv4` -lpthread; \
		else \
			$(CXX) $(CXXFLAGS) -o $@ $< $(LDFLAGS) -lpthread; \
		fi; \
	fi
	@echo "测试程序构建完成: $@"

# 编译测试源文件 - C文件 (自动检测模块)
$(BUILD_DIR)/tests/%.o: tests/%.c | $(BUILD_DIR)/tests
	@echo "正在编译测试文件: $<"
	@mkdir -p $(dir $@)
	@MODULE_FOUND=""; \
	for mod in $(MODULES); do \
		if echo "$*" | grep -q "$$mod"; then \
			MODULE_FOUND="$$mod"; \
			break; \
		fi; \
	done; \
	if echo "$*" | grep -q "water_level"; then \
		$(CC) $(CFLAGS) -I$(PROJECT_ROOT)/src/modules/hcsr04/include -I$(PROJECT_ROOT)/tests -MMD -MP -c -o $@ $<; \
	elif [ -n "$$MODULE_FOUND" ]; then \
		$(CC) $(CFLAGS) -I$(PROJECT_ROOT)/src/modules/$$MODULE_FOUND/include -I$(PROJECT_ROOT)/tests -MMD -MP -c -o $@ $<; \
	elif echo "$*" | grep -q -E "(integration|performance|unit)"; then \
		$(CC) $(CFLAGS) $(MODULE_INCLUDES) -I$(PROJECT_ROOT)/tests -MMD -MP -c -o $@ $<; \
	elif echo "$*" | grep -q "test_framework"; then \
		$(CC) $(CFLAGS) -I$(PROJECT_ROOT)/tests -MMD -MP -c -o $@ $<; \
	else \
		$(CC) $(CFLAGS) -I$(PROJECT_ROOT)/tests -MMD -MP -c -o $@ $<; \
	fi

# 编译测试源文件 - C++文件 (自动检测模块)
$(BUILD_DIR)/tests/%.o: tests/%.cpp | $(BUILD_DIR)/tests
	@echo "正在编译C++测试文件: $<"
	@mkdir -p $(dir $@)
	@if echo "$*" | grep -q "opencv"; then \
		$(CXX) $(CXXFLAGS) `pkg-config --cflags opencv4` -MMD -MP -c -o $@ $<; \
	else \
		MODULE_FOUND=""; \
		for mod in $(MODULES); do \
			if echo "$*" | grep -q "$$mod"; then \
				MODULE_FOUND="$$mod"; \
				break; \
			fi; \
		done; \
		if [ -n "$$MODULE_FOUND" ]; then \
			$(CXX) $(CXXFLAGS) -I$(PROJECT_ROOT)/src/modules/$$MODULE_FOUND/include -MMD -MP -c -o $@ $<; \
		else \
			$(CXX) $(CXXFLAGS) -MMD -MP -c -o $@ $<; \
		fi; \
	fi

# 清理目标
clean:
	$(RM) $(BUILD_DIR) $(BIN_DIR) $(LIB_DIR)
	@echo "项目清理完成"

# 安装第三方库
install_deps:
	@echo "请手动安装OpenCV、wiringPi和Eclipse Paho C库"

# 帮助目标
help:
	@echo "可用的Makefile目标:"
	@echo "  all         - 编译主程序和测试程序"
	@echo "  tests       - 只编译测试程序"
	@echo "  debug-tests - 显示检测到的测试文件"
	@echo "  clean       - 清理所有编译生成的文件"
	@echo "  install_deps - 安装第三方依赖库"
	@echo "  help        - 显示此帮助信息"
	@echo "  check_deps  - 检查并显示依赖库版本"
	@echo ""
	@echo "测试文件自动检测规则:"
	@echo "  - 自动编译tests/目录下的所有.c和.cpp文件"
	@echo "  - 生成对应的可执行文件到bin/Tests/目录"
	@echo "  - 支持特殊库链接(opencv, wiringPi, mqtt, aht20, bh1750)"

# 包含依赖文件
-include $(foreach mod,$(MODULES),$(DEPS_$(mod)))
-include $(MAIN_DEPS)
# -include $(TEST_DEPS)

# 只编译测试程序
tests: $(TEST_EXES)
	@echo "测试程序编译完成"

# 调试目标 - 显示检测到的测试文件
debug-tests:
	@echo "自动检测到的模块:"
	@echo "  $(MODULES)"
	@echo "模块头文件路径:"
	@echo "  $(MODULE_INCLUDES)"
	@echo "检测到的C测试文件:"
	@echo "  $(TEST_SRCS_C)"
	@echo "检测到的C++测试文件:"
	@echo "  $(TEST_SRCS_CPP)"
	@echo "生成的目标文件:"
	@echo "  $(TEST_OBJS)"
	@echo "生成的可执行文件:"
	@echo "  $(TEST_EXES)"

.PHONY: all clean install_deps help check_deps tests debug-tests water_level_test

# 水位测试目标
water_level_test: $(TEST_BIN_DIR)/water_level_test