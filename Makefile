# GreenLand 智能农业监控系统 Makefile
# 作者: aubuty
# 版本: 2.0.0

# 颜色定义
RED     = \033[0;31m
GREEN   = \033[0;32m
YELLOW  = \033[1;33m
BLUE    = \033[0;34m
PURPLE  = \033[0;35m
CYAN    = \033[0;36m
NC      = \033[0m

# 项目配置
PROJECT_NAME = GreenLand
VERSION = 1.1.0
AUTHOR = Alex

# 编译器配置
CXX = g++
CC = gcc
CXXFLAGS = -Wall -Wextra -Wno-unused-function -std=c++17 -O2 -fPIC -D_DEFAULT_SOURCE
CFLAGS = -Wall -Wextra -Wno-unused-function -std=c99 -O2 -fPIC -D_DEFAULT_SOURCE

# 目录配置
SRC_DIR = src
BUILD_DIR = build
BIN_DIR = bin
OBJ_DIR = $(BUILD_DIR)/obj

# 动态检测所有模块目录
MODULE_DIRS = $(wildcard $(SRC_DIR)/modules/*)
MODULE_NAMES = $(notdir $(MODULE_DIRS))

# 核心目录
CORE_DIR = $(SRC_DIR)/core

# 为每个模块生成包含路径
INCLUDES = $(foreach module,$(MODULE_NAMES),-I$(SRC_DIR)/modules/$(module)/include) \
           -I$(CORE_DIR)

# 基础库
LIBS = -lwiringPi -lpthread

# 主程序源文件
MAIN_SRC = $(SRC_DIR)/main.cpp

# 动态生成模块源文件列表 (排除摄像头模块)
BASE_MODULE_NAMES = $(filter-out camera,$(MODULE_NAMES))
BASE_MODULE_SOURCES = $(foreach module,$(BASE_MODULE_NAMES),$(wildcard $(SRC_DIR)/modules/$(module)/src/*.c))

# 基础源文件列表
BASE_SOURCES = $(MAIN_SRC) $(BASE_MODULE_SOURCES)

# 动态生成对象文件列表
MAIN_OBJ = $(OBJ_DIR)/main.o
BASE_MODULE_OBJECTS = $(foreach src,$(BASE_MODULE_SOURCES),$(OBJ_DIR)/$(notdir $(basename $(src))).o)

BASE_OBJECTS = $(MAIN_OBJ) $(BASE_MODULE_OBJECTS)

# 检查OpenCV
OPENCV_EXISTS := $(shell pkg-config --exists opencv4 2>/dev/null && echo "opencv4" || \
                         (pkg-config --exists opencv 2>/dev/null && echo "opencv" || echo "none"))

# 根据OpenCV配置摄像头支持
ifeq ($(OPENCV_EXISTS),opencv4)
    CAMERA_ENABLED = 1
    OPENCV_CFLAGS = $(shell pkg-config --cflags opencv4 2>/dev/null || echo "-I/usr/include/opencv4")
    OPENCV_LIBS = -lopencv_core -lopencv_imgproc -lopencv_imgcodecs -lopencv_videoio -lopencv_highgui
    CAMERA_SRC = $(CAMERA_DIR)/src/camera.cpp
    CAMERA_OBJ = $(OBJ_DIR)/camera.o
    INCLUDES += -I$(CAMERA_DIR)/include $(OPENCV_CFLAGS)
    LIBS += $(OPENCV_LIBS)
    CXXFLAGS += -DENABLE_CAMERA
    TARGET = $(BIN_DIR)/$(PROJECT_NAME)
    ALL_SOURCES = $(BASE_SOURCES) $(CAMERA_SRC)
    ALL_OBJECTS = $(BASE_OBJECTS) $(CAMERA_OBJ)
else ifeq ($(OPENCV_EXISTS),opencv)
    CAMERA_ENABLED = 1
    OPENCV_CFLAGS = $(shell pkg-config --cflags opencv 2>/dev/null || echo "-I/usr/include/opencv")
    OPENCV_LIBS = -lopencv_core -lopencv_imgproc -lopencv_imgcodecs -lopencv_videoio -lopencv_highgui
    CAMERA_SRC = $(CAMERA_DIR)/src/camera.cpp
    CAMERA_OBJ = $(OBJ_DIR)/camera.o
    INCLUDES += -I$(CAMERA_DIR)/include $(OPENCV_CFLAGS)
    LIBS += $(OPENCV_LIBS)
    CXXFLAGS += -DENABLE_CAMERA
    TARGET = $(BIN_DIR)/$(PROJECT_NAME)
    ALL_SOURCES = $(BASE_SOURCES) $(CAMERA_SRC)
    ALL_OBJECTS = $(BASE_OBJECTS) $(CAMERA_OBJ)
else
    CAMERA_ENABLED = 0
    TARGET = $(BIN_DIR)/$(PROJECT_NAME)_no_camera
    ALL_SOURCES = $(BASE_SOURCES)
    ALL_OBJECTS = $(BASE_OBJECTS)
endif

# 编译选项
COMPILE_TESTS ?= no

# 默认目标
.PHONY: all clean help info tests with-tests

all: info $(TARGET)

# 编译主程序和测试程序
with-tests: info $(TARGET) tests

# 显示项目信息
info:
	@echo "$(CYAN)╔══════════════════════════════════════════════════════════════╗$(NC)"
	@echo "$(CYAN)║                    $(GREEN)GreenLand 编译系统$(CYAN)                      ║$(NC)"
	@echo "$(CYAN)╠══════════════════════════════════════════════════════════════╣$(NC)"
	@echo "$(CYAN)║ 项目名称: $(YELLOW)$(PROJECT_NAME)$(CYAN)                                        ║$(NC)"
	@echo "$(CYAN)║ 版本号:   $(YELLOW)$(VERSION)$(CYAN)                                           ║$(NC)"
	@echo "$(CYAN)║ 作者:     $(YELLOW)$(AUTHOR)$(CYAN)                                            ║$(NC)"
	@echo "$(CYAN)║ 编译器:   $(YELLOW)$(CXX) / $(CC)$(CYAN)                                      ║$(NC)"
ifeq ($(CAMERA_ENABLED),1)
	@echo "$(CYAN)║ OpenCV:   $(GREEN)✅ 已启用 ($(OPENCV_EXISTS))$(CYAN)                           ║$(NC)"
	@echo "$(CYAN)║ 摄像头:   $(GREEN)✅ 支持 (500万像素)$(CYAN)                           ║$(NC)"
else
	@echo "$(CYAN)║ OpenCV:   $(RED)❌ 未找到$(CYAN)                                         ║$(NC)"
	@echo "$(CYAN)║ 摄像头:   $(RED)❌ 禁用$(CYAN)                                           ║$(NC)"
endif
	@echo "$(CYAN)║ 目标文件: $(YELLOW)$(TARGET)$(CYAN)                                    ║$(NC)"
	@echo "$(CYAN)╚══════════════════════════════════════════════════════════════╝$(NC)"
	@echo ""

# 创建目录
$(OBJ_DIR):
	@echo "$(BLUE)📁 创建构建目录...$(NC)"
	@mkdir -p $(OBJ_DIR)
	@mkdir -p $(BIN_DIR)

# 编译主程序
$(MAIN_OBJ): $(MAIN_SRC) | $(OBJ_DIR)
	@echo "$(PURPLE)[1/$(words $(ALL_OBJECTS))]$(NC) $(BLUE)🔧 编译主程序...$(NC) $(YELLOW)$(notdir $<)$(NC)"
	@$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# 编译日志模块
$(LOGGER_OBJ): $(LOGGER_SRC) | $(OBJ_DIR)
	@echo "$(PURPLE)[2/$(words $(ALL_OBJECTS))]$(NC) $(BLUE)🔧 编译日志模块...$(NC) $(YELLOW)$(notdir $<)$(NC)"
	@$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 编译水位传感器模块
$(HCSR04_OBJ): $(HCSR04_SRC) | $(OBJ_DIR)
	@echo "$(PURPLE)[3/$(words $(ALL_OBJECTS))]$(NC) $(BLUE)🔧 编译水位传感器...$(NC) $(YELLOW)$(notdir $<)$(NC)"
	@$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 编译温湿度传感器模块
$(AHT20_OBJ): $(AHT20_SRC) | $(OBJ_DIR)
	@echo "$(PURPLE)[4/$(words $(ALL_OBJECTS))]$(NC) $(BLUE)🔧 编译温湿度传感器...$(NC) $(YELLOW)$(notdir $<)$(NC)"
	@$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 编译光照传感器模块
$(BH1750_OBJ): $(BH1750_SRC) | $(OBJ_DIR)
	@echo "$(PURPLE)[5/$(words $(ALL_OBJECTS))]$(NC) $(BLUE)🔧 编译光照传感器...$(NC) $(YELLOW)$(notdir $<)$(NC)"
	@$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 编译摄像头模块 (如果启用)
ifeq ($(CAMERA_ENABLED),1)
$(CAMERA_OBJ): $(CAMERA_SRC) | $(OBJ_DIR)
	@echo "$(PURPLE)[6/$(words $(ALL_OBJECTS))]$(NC) $(BLUE)🔧 编译摄像头模块...$(NC) $(YELLOW)$(notdir $<)$(NC)"
	@$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@
endif

# 链接最终程序
$(TARGET): $(ALL_OBJECTS)
	@echo ""
	@echo "$(GREEN)🔗 链接最终程序...$(NC)"
	@echo "$(CYAN)   目标: $(YELLOW)$(TARGET)$(NC)"
	@echo "$(CYAN)   库文件: $(YELLOW)$(LIBS)$(NC)"
	@$(CXX) $(ALL_OBJECTS) $(LIBS) -o $@
	@echo ""
	@echo "$(GREEN)✅ 编译完成！$(NC)"
	@echo "$(CYAN)📁 可执行文件: $(YELLOW)$(TARGET)$(NC)"
	@ls -lh $(TARGET) | awk '{print "$(CYAN)📊 文件大小: $(YELLOW)" $$5 "$(NC)"}'
	@echo "$(CYAN)🚀 运行命令: $(YELLOW)./$(TARGET)$(NC)"
	@echo ""

# 编译测试程序
tests:
	@echo ""
	@echo "$(BLUE)🧪 编译测试程序...$(NC)"
	@echo "$(CYAN)使用tests目录的专用Makefile$(NC)"
	@$(MAKE) -C tests
	@echo ""

# 清理
clean:
	@echo "$(BLUE)🧹 清理构建文件...$(NC)"
	@rm -rf $(BUILD_DIR)
	@rm -f $(BIN_DIR)/$(PROJECT_NAME)*
	@if [ -f "tests/Makefile" ]; then $(MAKE) -C tests clean; fi
	@echo "$(GREEN)✅ 清理完成$(NC)"

# 帮助信息
help:
	@echo "$(CYAN)GreenLand 编译系统帮助$(NC)"
	@echo "$(CYAN)========================$(NC)"
	@echo "$(YELLOW)make$(NC)           - 编译主程序"
	@echo "$(YELLOW)make with-tests$(NC) - 编译主程序和测试程序"
	@echo "$(YELLOW)make tests$(NC)      - 只编译测试程序"
	@echo "$(YELLOW)make clean$(NC)      - 清理所有构建文件"
	@echo "$(YELLOW)make help$(NC)       - 显示帮助信息"
	@echo "$(YELLOW)make info$(NC)       - 显示项目信息"
	@echo ""
	@echo "$(CYAN)测试系统:$(NC)"
	@echo "$(YELLOW)cd tests && make$(NC)      - 进入tests目录编译"
	@echo "$(YELLOW)cd tests && make list$(NC)  - 列出所有测试文件"
	@echo "$(YELLOW)cd tests && make help$(NC)  - 查看测试系统帮助"
	@echo ""
	@echo "$(CYAN)特性:$(NC)"
	@echo "$(GREEN)✅ 自动检测OpenCV并配置500万像素摄像头支持$(NC)"
	@echo "$(GREEN)✅ 彩色编译进度显示$(NC)"
	@echo "$(GREEN)✅ 智能依赖管理$(NC)"
	@echo "$(GREEN)✅ 零编译警告$(NC)"
	@echo "$(GREEN)✅ 可选择性编译测试程序$(NC)"

