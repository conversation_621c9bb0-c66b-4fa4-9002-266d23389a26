/**
 * @file camera_basic_usage.c
 * @brief 摄像头模块基本使用示例
 * <AUTHOR>
 * @date 2024
 *
 * 演示摄像头模块的基本使用方法:
 * - 设备扫描和初始化
 * - 拍照功能
 * - 录像功能
 * - 参数配置
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include "camera.h"
#include "logger.h"

static volatile int running = 1;
static camera_t camera = NULL;

// 信号处理函数
void signal_handler(int sig) {
    printf("\n正在退出...\n");
    running = 0;

    if (camera) {
        camera_stop_recording(camera);
    }
}

int main() {
    printf("📷 摄像头模块基本使用示例\n");
    printf("==========================\n\n");

    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 1. 初始化日志系统
    printf("📝 初始化日志系统...\n");
    log_config_t log_config;
    logger_get_default_config(&log_config, "camera_example");
    logger_t logger = logger_create(&log_config);

    if (logger) {
        camera_set_logger(logger);
        printf("✅ 日志系统初始化完成\n\n");
    }

    // 2. 扫描摄像头设备
    printf("🔍 扫描摄像头设备...\n");
    int devices[8];
    int device_count = camera_scan_devices(devices, 8);

    if (device_count == 0) {
        printf("❌ 未找到摄像头设备\n");
        printf("请确保USB摄像头已正确连接\n");
        goto cleanup;
    }

    printf("✅ 找到 %d 个摄像头设备:\n", device_count);
    for (int i = 0; i < device_count; i++) {
        printf("  设备 %d: /dev/video%d\n", i, devices[i]);
    }
    printf("\n");

    // 3. 配置摄像头
    printf("⚙️  配置摄像头...\n");
    camera_config_t config;
    camera_get_default_config(&config);

    // 使用第一个设备
    config.device_id = devices[0];

    // 设置为HD分辨率
    config.resolution = CAMERA_RES_HD;

    // 设置保存路径
    strcpy(config.save_path, "CameraOutput");

    // 启用时间戳和水印
    config.enable_timestamp = true;
    config.enable_watermark = true;

    // 设置JPEG质量
    config.jpeg_quality = 90;

    printf("  设备ID: %d\n", config.device_id);
    printf("  分辨率: %s\n", camera_resolution_to_string(config.resolution));
    printf("  帧率: %d fps\n", config.fps);
    printf("  保存路径: %s\n", config.save_path);
    printf("  JPEG质量: %d\n", config.jpeg_quality);
    printf("\n");

    // 4. 创建和初始化摄像头
    printf("🔧 创建摄像头实例...\n");
    camera = camera_create(&config);
    if (!camera) {
        printf("❌ 创建摄像头实例失败\n");
        goto cleanup;
    }

    printf("🔧 初始化摄像头...\n");
    int result = camera_init(camera);
    if (result != CAMERA_OK) {
        printf("❌ 摄像头初始化失败: %s\n", camera_error_to_string(result));
        goto cleanup;
    }

    printf("✅ 摄像头初始化成功\n\n");

    // 5. 获取摄像头信息
    camera_info_t info;
    camera_get_info(camera, &info);
    printf("📊 摄像头信息:\n");
    printf("  设备名称: /dev/video%d\n", info.device_id);
    printf("  实际分辨率: %dx%d\n", info.width, info.height);
    printf("  实际帧率: %d fps\n", info.fps);
    printf("  连接状态: %s\n", info.is_connected ? "已连接" : "未连接");
    printf("\n");

    // 6. 拍照示例
    printf("📸 拍照示例...\n");

    // 快速拍照
    printf("  快速拍照...\n");
    result = camera_quick_photo(camera, "example_quick");
    if (result == CAMERA_OK) {
        printf("  ✅ 快速拍照成功\n");
    } else {
        printf("  ❌ 快速拍照失败: %s\n", camera_error_to_string(result));
    }

    // 高质量拍照
    printf("  高质量拍照...\n");
    camera_photo_params_t photo_params = {
        .filename = "example_hq",
        .format = CAMERA_FORMAT_JPEG,
        .quality = 100,
        .add_timestamp = true,
        .add_watermark = true,
        .width = 0,  // 使用默认分辨率
        .height = 0
    };

    result = camera_take_photo(camera, &photo_params);
    if (result == CAMERA_OK) {
        printf("  ✅ 高质量拍照成功\n");
    } else {
        printf("  ❌ 高质量拍照失败: %s\n", camera_error_to_string(result));
    }

    // PNG格式拍照
    printf("  PNG格式拍照...\n");
    photo_params.format = CAMERA_FORMAT_PNG;
    strcpy(photo_params.filename, "example_png");

    result = camera_take_photo(camera, &photo_params);
    if (result == CAMERA_OK) {
        printf("  ✅ PNG格式拍照成功\n");
    } else {
        printf("  ❌ PNG格式拍照失败: %s\n", camera_error_to_string(result));
    }

    printf("\n");

    // 7. 录像示例
    printf("🎥 录像示例...\n");

    // 短时录像
    printf("  开始5秒录像...\n");
    camera_video_params_t video_params = {
        .filename = "example_video",
        .format = CAMERA_VIDEO_MP4,
        .duration_seconds = 5,
        .fps = 0,  // 使用默认帧率
        .width = 0,  // 使用默认分辨率
        .height = 0,
        .add_timestamp = true,
        .add_watermark = true
    };

    result = camera_start_recording(camera, &video_params);
    if (result == CAMERA_OK) {
        printf("  ✅ 录像开始\n");

        // 显示倒计时
        for (int i = 5; i > 0 && running; i--) {
            printf("  录像中... %d 秒\r", i);
            fflush(stdout);
            sleep(1);
        }
        printf("\n");

        // 等待录像完成
        while (camera_get_state(camera) == CAMERA_STATE_RECORDING && running) {
            usleep(100000);  // 100ms
        }

        printf("  ✅ 录像完成\n");
    } else {
        printf("  ❌ 录像失败: %s\n", camera_error_to_string(result));
    }

    printf("\n");

    // 8. 参数调整示例
    printf("⚙️  参数调整示例...\n");

    // 调整分辨率
    printf("  设置分辨率为 640x480...\n");
    result = camera_set_resolution(camera, 640, 480);
    if (result == CAMERA_OK) {
        printf("  ✅ 分辨率设置成功\n");
    }

    // 调整图像参数
    printf("  调整图像参数...\n");
    result = camera_set_image_params(camera, 10, 20, 0, 0);  // 亮度+10, 对比度+20
    if (result == CAMERA_OK) {
        printf("  ✅ 图像参数调整成功\n");
    }

    // 拍一张调整后的照片
    printf("  拍摄调整后的照片...\n");
    result = camera_quick_photo(camera, "example_adjusted");
    if (result == CAMERA_OK) {
        printf("  ✅ 调整后拍照成功\n");
    }

    printf("\n");

    // 9. 显示统计信息
    printf("📊 最终统计信息:\n");
    char stats[1024];
    result = camera_get_stats(camera, stats, sizeof(stats));
    if (result == CAMERA_OK) {
        printf("%s\n", stats);
    }

    printf("\n✅ 示例程序完成\n");
    printf("\n💡 生成的文件:\n");
    printf("  - 查看 CameraOutput/ 目录中的照片和视频\n");
    printf("  - 查看 Log/ 目录中的日志文件\n");

cleanup:
    // 清理资源
    if (camera) {
        printf("\n🧹 清理摄像头资源...\n");
        camera_destroy(camera);
    }

    if (logger) {
        logger_destroy(logger);
    }

    printf("👋 示例程序退出\n");
    return 0;
}
