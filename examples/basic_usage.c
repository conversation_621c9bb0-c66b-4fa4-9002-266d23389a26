/**
 * @file basic_usage.c
 * @brief GreenLand基本使用示例
 * <AUTHOR>
 * @date 2024
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include "../src/modules/logger/include/logger.h"
#include "../src/modules/hcsr04/include/hcsr04.h"

static volatile int running = 1;

void signal_handler(int sig) {
    printf("\n正在退出...\n");
    running = 0;
}

int main() {
    printf("🌱 GreenLand 基本使用示例\n");
    printf("========================\n\n");

    signal(SIGINT, signal_handler);

    // 1. 初始化日志系统
    printf("📝 初始化日志系统...\n");
    log_config_t log_config;
    logger_get_default_config(&log_config, "basic_example");
    logger_t logger = logger_create(&log_config);

    if (!logger) {
        printf("❌ 日志系统初始化失败\n");
        return 1;
    }

    logger_info(logger, "GreenLand 基本示例启动");
    printf("✅ 日志系统初始化完成\n\n");

    // 2. 初始化水位传感器
    printf("🌊 初始化水位传感器...\n");

    if (hcsr04_init() != HCSR04_OK) {
        printf("⚠️ 水位传感器初始化失败（可能无硬件连接）\n");
        printf("💡 这是正常的，如果没有连接HC-SR04传感器\n\n");
    } else {
        printf("✅ 水位传感器初始化完成\n\n");
    }

    // 3. 主监控循环
    printf("🔄 开始监控循环（按Ctrl+C退出）...\n");
    int loop_count = 0;

    while (running) {
        loop_count++;

        // 读取水位数据
        float distance, water_level;
        hcsr04_read_distance(&distance);
        hcsr04_read_water_level(&water_level);

        if (distance > 0) {
            printf("📊 循环 %d: 距离=%.2fcm, 水位=%.2fcm (%.1f%%)\n",
                   loop_count, distance, water_level,
                   (water_level / 35.0) * 100);

            logger_info(logger, "水位数据: 距离=%.2fcm, 水位=%.2fcm",
                       distance, water_level);

            // 水位报警
            if (water_level < 5.0) {
                printf("🔴 警告: 水位过低！\n");
                logger_warn(logger, "水位过低: %.2fcm", water_level);
            } else if (water_level > 30.0) {
                printf("🔵 提示: 水位充足\n");
            }
        } else {
            printf("📊 循环 %d: 传感器读取失败\n", loop_count);
            logger_warn(logger, "传感器读取失败");
        }

        // 每10次循环显示一次系统状态
        if (loop_count % 10 == 0) {
            printf("💚 系统运行正常 - 已运行 %d 个周期\n", loop_count);
            logger_info(logger, "系统状态检查 - 运行周期: %d", loop_count);
        }

        sleep(2);  // 2秒间隔
    }

    // 4. 清理资源
    printf("\n🧹 清理系统资源...\n");
    logger_info(logger, "系统正常退出");

    hcsr04_deinit();
    logger_destroy(logger);

    printf("✅ 示例程序完成\n");
    printf("💡 查看 Log/ 目录中的日志文件\n");

    return 0;
}
