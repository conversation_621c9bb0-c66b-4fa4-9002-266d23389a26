/**
 * @file basic_usage.c
 * @brief AHT20温湿度传感器基础使用示例
 * <AUTHOR> Team
 * @date 2025-05-23
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include "aht20.h"

int main() {
    printf("=== AHT20温湿度传感器基础示例 ===\n");
    
    // 1. 初始化传感器
    printf("正在初始化AHT20传感器...\n");
    if (aht20_init() != AHT20_OK) {
        printf("❌ AHT20初始化失败\n");
        printf("请检查:\n");
        printf("  - 传感器是否连接到I2C-2总线\n");
        printf("  - I2C地址是否为0x38\n");
        printf("  - 是否以sudo权限运行\n");
        return 1;
    }
    printf("✅ AHT20初始化成功\n\n");
    
    // 2. 读取单次数据
    printf("--- 单次读取示例 ---\n");
    aht20_data_t data;
    if (aht20_read_data(&data) == AHT20_OK) {
        printf("温度: %.2f°C\n", data.temperature);
        printf("湿度: %.2f%%\n", data.humidity);
    } else {
        printf("❌ 数据读取失败\n");
    }
    
    printf("\n--- 连续读取示例 ---\n");
    printf("连续读取5次，每次间隔2秒:\n");
    
    // 3. 连续读取数据
    for (int i = 0; i < 5; i++) {
        if (aht20_read_data(&data) == AHT20_OK) {
            printf("第%d次: 温度=%.2f°C, 湿度=%.2f%%\n", 
                   i + 1, data.temperature, data.humidity);
            
            // 简单的数据验证
            if (data.temperature < -40 || data.temperature > 85) {
                printf("  ⚠️  温度值异常\n");
            }
            if (data.humidity < 0 || data.humidity > 100) {
                printf("  ⚠️  湿度值异常\n");
            }
        } else {
            printf("第%d次: 读取失败\n", i + 1);
        }
        
        if (i < 4) sleep(2); // 最后一次不等待
    }
    
    // 4. 清理资源
    aht20_deinit();
    printf("\n✅ 示例完成\n");
    
    return 0;
}
