/**
 * @file advanced_usage.c
 * @brief AHT20温湿度传感器高级使用示例
 * <AUTHOR> Team
 * @date 2025-05-23
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <time.h>
#include <math.h>
#include "aht20.h"

static volatile int running = 1;

// 信号处理函数
void signal_handler(int sig) {
    printf("\n收到退出信号，正在停止...\n");
    running = 0;
}

// 计算露点温度
float calculate_dew_point(float temperature, float humidity) {
    float a = 17.27;
    float b = 237.7;
    float alpha = ((a * temperature) / (b + temperature)) + log(humidity / 100.0);
    return (b * alpha) / (a - alpha);
}

// 计算体感温度 (Heat Index)
float calculate_heat_index(float temperature, float humidity) {
    if (temperature < 27.0) return temperature; // 低温时体感温度约等于实际温度
    
    float t = temperature;
    float h = humidity;
    
    // Heat Index 公式 (简化版)
    float hi = -8.78469475556 + 1.61139411 * t + 2.33854883889 * h
               - 0.14611605 * t * h - 0.012308094 * t * t
               - 0.0164248277778 * h * h + 0.002211732 * t * t * h
               + 0.00072546 * t * h * h - 0.000003582 * t * t * h * h;
    
    return hi;
}

// 环境舒适度评估
const char* assess_comfort(float temperature, float humidity) {
    if (temperature < 18) return "偏冷";
    if (temperature > 28) return "偏热";
    if (humidity < 30) return "偏干";
    if (humidity > 70) return "偏湿";
    if (temperature >= 20 && temperature <= 26 && humidity >= 40 && humidity <= 60) {
        return "舒适";
    }
    return "一般";
}

int main() {
    printf("=== AHT20温湿度传感器高级示例 ===\n");
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 初始化传感器
    if (aht20_init() != AHT20_OK) {
        printf("❌ AHT20初始化失败\n");
        return 1;
    }
    printf("✅ AHT20初始化成功\n\n");
    
    printf("开始环境监控 (按Ctrl+C退出):\n");
    printf("时间\t\t温度\t湿度\t露点\t体感\t舒适度\n");
    printf("------------------------------------------------------------\n");
    
    int count = 0;
    float temp_sum = 0, humidity_sum = 0;
    float temp_min = 100, temp_max = -100;
    float humidity_min = 100, humidity_max = 0;
    
    while (running) {
        aht20_data_t data;
        
        if (aht20_read_data(&data) == AHT20_OK) {
            count++;
            
            // 统计数据
            temp_sum += data.temperature;
            humidity_sum += data.humidity;
            
            if (data.temperature < temp_min) temp_min = data.temperature;
            if (data.temperature > temp_max) temp_max = data.temperature;
            if (data.humidity < humidity_min) humidity_min = data.humidity;
            if (data.humidity > humidity_max) humidity_max = data.humidity;
            
            // 计算衍生数据
            float dew_point = calculate_dew_point(data.temperature, data.humidity);
            float heat_index = calculate_heat_index(data.temperature, data.humidity);
            const char* comfort = assess_comfort(data.temperature, data.humidity);
            
            // 获取当前时间
            time_t now = time(NULL);
            struct tm* tm_info = localtime(&now);
            char time_str[20];
            strftime(time_str, sizeof(time_str), "%H:%M:%S", tm_info);
            
            // 输出数据
            printf("%s\t%.1f°C\t%.1f%%\t%.1f°C\t%.1f°C\t%s\n",
                   time_str, data.temperature, data.humidity, 
                   dew_point, heat_index, comfort);
            
            // 每10次输出统计信息
            if (count % 10 == 0) {
                printf("\n--- 统计信息 (最近%d次测量) ---\n", count);
                printf("温度: 平均=%.1f°C, 最低=%.1f°C, 最高=%.1f°C\n",
                       temp_sum / count, temp_min, temp_max);
                printf("湿度: 平均=%.1f%%, 最低=%.1f%%, 最高=%.1f%%\n",
                       humidity_sum / count, humidity_min, humidity_max);
                printf("------------------------------------------------------------\n");
            }
            
        } else {
            printf("数据读取失败\n");
        }
        
        sleep(5); // 每5秒读取一次
    }
    
    // 输出最终统计
    if (count > 0) {
        printf("\n=== 最终统计 ===\n");
        printf("总测量次数: %d\n", count);
        printf("温度: 平均=%.1f°C, 范围=%.1f°C~%.1f°C\n",
               temp_sum / count, temp_min, temp_max);
        printf("湿度: 平均=%.1f%%, 范围=%.1f%%~%.1f%%\n",
               humidity_sum / count, humidity_min, humidity_max);
    }
    
    aht20_deinit();
    printf("✅ 监控结束\n");
    
    return 0;
}
