/**
 * @file advanced_usage.c
 * @brief HC-SR04 超声波传感器高级使用示例
 * <AUTHOR> Team
 * @date 2024
 *
 * 本示例演示 HC-SR04 的高级功能:
 * - 自定义配置
 * - 多次采样平均
 * - 传感器校准
 * - 错误处理和重试
 * - 数据记录
 *
 * 编译命令:
 * gcc -o hcsr04_advanced advanced_usage.c -I../../src/modules/hcsr04/include -L../../lib -lhcsr04 -lm
 *
 * 运行命令:
 * sudo ./hcsr04_advanced
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <time.h>
#include <string.h>
#include "hcsr04.h"

// 全局变量
static volatile int running = 1;
static FILE* log_file = NULL;

/**
 * @brief 信号处理函数
 */
void signal_handler(int sig) {
    printf("\n收到信号 %d，正在退出...\n", sig);
    running = 0;
}

/**
 * @brief 记录数据到文件
 */
void log_data(const hcsr04_data_t* data) {
    if (!log_file || !data) return;

    time_t now = time(NULL);
    struct tm* tm_info = localtime(&now);
    char time_str[64];
    strftime(time_str, sizeof(time_str), "%Y-%m-%d %H:%M:%S", tm_info);

    fprintf(log_file, "%s,%.2f,%.2f,%.1f,%u,%s\n",
            time_str,
            data->distance_cm,
            data->water_level_cm,
            data->water_level_percent,
            data->echo_time_us,
            data->valid ? "valid" : "invalid");

    fflush(log_file);
}

/**
 * @brief 传感器校准功能
 */
void calibrate_sensor(void) {
    printf("\n=== 传感器校准 ===\n");
    printf("请将一个已知距离的物体放在传感器前方\n");
    printf("输入已知距离 (cm，输入0跳过): ");

    float known_distance;
    if (scanf("%f", &known_distance) != 1 || known_distance <= 0) {
        printf("跳过校准\n");
        return;
    }

    printf("正在测量距离...\n");

    float measured_distance;
    int result = hcsr04_calibrate(known_distance, &measured_distance);

    if (result == HCSR04_OK) {
        printf("校准完成！\n");
    } else {
        printf("校准失败: %s\n", hcsr04_error_to_string(result));
    }

    printf("按回车键继续...");
    getchar(); // 清除缓冲区
    getchar(); // 等待用户输入
}

/**
 * @brief 配置水箱参数
 */
void configure_tank(void) {
    printf("\n=== 水箱配置 ===\n");

    float tank_height, sensor_offset;

    printf("当前水箱高度: %.1f cm\n", hcsr04_get_tank_height());
    printf("输入新的水箱高度 (cm，输入0保持不变): ");
    if (scanf("%f", &tank_height) == 1 && tank_height > 0) {
        if (hcsr04_set_tank_height(tank_height) == HCSR04_OK) {
            printf("水箱高度已设置为: %.1f cm\n", tank_height);
        } else {
            printf("设置失败，参数无效\n");
        }
    }

    printf("当前传感器偏移: %.1f cm\n", hcsr04_get_sensor_offset());
    printf("输入新的传感器偏移 (cm，输入0保持不变): ");
    if (scanf("%f", &sensor_offset) == 1 && sensor_offset != 0) {
        if (hcsr04_set_sensor_offset(sensor_offset) == HCSR04_OK) {
            printf("传感器偏移已设置为: %.1f cm\n", sensor_offset);
        } else {
            printf("设置失败，参数无效\n");
        }
    }

    printf("按回车键继续...");
    getchar(); // 清除缓冲区
    getchar(); // 等待用户输入
}

/**
 * @brief 高精度测量 (多次采样)
 */
void high_precision_measurement(void) {
    printf("\n=== 高精度测量 ===\n");
    printf("进行5次采样的高精度测量...\n");

    hcsr04_data_t data;
    int result = hcsr04_read_averaged(&data, 5);

    if (result == HCSR04_OK && data.valid) {
        printf("高精度测量结果:\n");
        printf("  距离: %.3f cm\n", data.distance_cm);
        printf("  水位: %.3f cm (%.2f%%)\n", data.water_level_cm, data.water_level_percent);
        printf("  平均回响时间: %u μs\n", data.echo_time_us);
    } else {
        printf("高精度测量失败: %s\n", hcsr04_error_to_string(result));
    }

    printf("按回车键继续...");
    getchar(); // 等待用户输入
}

/**
 * @brief 主函数
 */
int main(void) {
    printf("=== HC-SR04 超声波传感器高级使用示例 ===\n");

    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 打开日志文件
    log_file = fopen("hcsr04_data.csv", "w");
    if (log_file) {
        fprintf(log_file, "时间,距离(cm),水位(cm),水位(%%),回响时间(μs),状态\n");
        printf("数据将记录到 hcsr04_data.csv 文件\n");
    }

    // 自定义配置
    hcsr04_config_t config;
    hcsr04_get_default_config(&config);

    // 可以修改配置参数 (用户实际水箱)
    config.tank_height_cm = 35.0f;   // 35cm 水箱 (用户实际尺寸)
    config.sensor_offset_cm = 3.0f;  // 3cm 偏移
    config.timeout_us = 35000;       // 35ms 超时
    config.sample_count = 3;         // 3次采样

    // 使用自定义配置初始化
    printf("正在使用自定义配置初始化 HC-SR04 传感器...\n");
    int result = hcsr04_init_with_config(&config);
    if (result != HCSR04_OK) {
        printf("错误: HC-SR04 初始化失败: %s\n", hcsr04_error_to_string(result));
        if (log_file) fclose(log_file);
        return -1;
    }

    printf("HC-SR04 传感器初始化成功！\n");

    // 显示菜单
    printf("\n选择操作:\n");
    printf("1. 配置水箱参数\n");
    printf("2. 传感器校准\n");
    printf("3. 高精度测量\n");
    printf("4. 开始连续监测\n");
    printf("请选择 (1-4): ");

    int choice;
    if (scanf("%d", &choice) == 1) {
        switch (choice) {
            case 1:
                configure_tank();
                break;
            case 2:
                calibrate_sensor();
                break;
            case 3:
                high_precision_measurement();
                break;
            case 4:
                break;
            default:
                printf("无效选择，直接开始监测\n");
                break;
        }
    }

    // 连续监测
    printf("\n开始连续水位监测...\n");
    printf("按 Ctrl+C 退出程序\n");
    printf("时间 | 距离(cm) | 水位(cm) | 水位(%%) | 状态\n");
    printf("------------------------------------------------\n");

    int read_count = 0;
    int error_count = 0;

    while (running) {
        hcsr04_data_t data;

        // 使用高精度读取 (3次采样平均)
        result = hcsr04_read_averaged(&data, 3);

        time_t now = time(NULL);
        struct tm* tm_info = localtime(&now);
        char time_str[16];
        strftime(time_str, sizeof(time_str), "%H:%M:%S", tm_info);

        if (result == HCSR04_OK && data.valid) {
            printf("%s | %7.2f | %7.2f | %6.1f | 正常\n",
                   time_str,
                   data.distance_cm,
                   data.water_level_cm,
                   data.water_level_percent);

            error_count = 0; // 重置错误计数

            // 记录数据
            log_data(&data);

            // 水位报警
            if (data.water_level_percent < 5.0f) {
                printf("🚨 严重警告: 水位极低 (%.1f%%)\n", data.water_level_percent);
            } else if (data.water_level_percent < 15.0f) {
                printf("⚠️  警告: 水位过低 (%.1f%%)\n", data.water_level_percent);
            } else if (data.water_level_percent > 95.0f) {
                printf("🚨 严重警告: 水位极高 (%.1f%%)\n", data.water_level_percent);
            } else if (data.water_level_percent > 85.0f) {
                printf("⚠️  警告: 水位过高 (%.1f%%)\n", data.water_level_percent);
            }
        } else {
            printf("%s | 读取失败: %s\n", time_str, hcsr04_error_to_string(result));
            error_count++;

            // 连续错误处理
            if (error_count >= 5) {
                printf("⚠️  连续读取失败，请检查传感器连接\n");
                error_count = 0;
            }
        }

        read_count++;

        // 每100次读取显示统计信息
        if (read_count % 100 == 0) {
            printf("--- 已完成 %d 次测量 ---\n", read_count);
        }

        // 等待2秒
        sleep(2);
    }

    // 清理资源
    printf("\n正在清理资源...\n");
    hcsr04_deinit();

    if (log_file) {
        fclose(log_file);
        printf("数据已保存到 hcsr04_data.csv\n");
    }

    printf("程序已退出。\n");
    return 0;
}
