/**
 * @file basic_usage.c
 * @brief HC-SR04 超声波传感器基础使用示例
 * <AUTHOR> Team
 * @date 2024
 * 
 * 本示例演示如何使用 HC-SR04 超声波传感器进行水位检测
 * 
 * 硬件连接:
 * - VCC: 5V
 * - GND: GND  
 * - Trig: GPIO 22 (物理引脚 15)
 * - Echo: GPIO 23 (物理引脚 16)
 * 
 * 编译命令:
 * gcc -o hcsr04_basic basic_usage.c -I../../src/modules/hcsr04/include -L../../lib -lhcsr04 -lm
 * 
 * 运行命令:
 * sudo ./hcsr04_basic
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include "hcsr04.h"

// 全局变量
static volatile int running = 1;

/**
 * @brief 信号处理函数
 */
void signal_handler(int sig) {
    printf("\n收到信号 %d，正在退出...\n", sig);
    running = 0;
}

/**
 * @brief 主函数
 */
int main(void) {
    printf("=== HC-SR04 超声波传感器基础使用示例 ===\n");
    printf("按 Ctrl+C 退出程序\n\n");
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 初始化传感器
    printf("正在初始化 HC-SR04 传感器...\n");
    int result = hcsr04_init();
    if (result != HCSR04_OK) {
        printf("错误: HC-SR04 初始化失败: %s\n", hcsr04_error_to_string(result));
        return -1;
    }
    
    printf("HC-SR04 传感器初始化成功！\n\n");
    
    // 显示传感器状态
    char status_info[512];
    hcsr04_get_status_info(status_info, sizeof(status_info));
    printf("%s\n", status_info);
    
    // 主循环 - 连续读取数据
    printf("开始水位监测...\n");
    printf("格式: 时间 | 距离(cm) | 水位(cm) | 水位(%%) | 回响时间(μs)\n");
    printf("------------------------------------------------------------\n");
    
    int read_count = 0;
    while (running) {
        hcsr04_data_t data;
        
        // 读取传感器数据
        result = hcsr04_read_data(&data);
        
        if (result == HCSR04_OK && data.valid) {
            printf("%02d | %7.2f | %7.2f | %6.1f | %8u\n",
                   ++read_count,
                   data.distance_cm,
                   data.water_level_cm,
                   data.water_level_percent,
                   data.echo_time_us);
            
            // 水位报警
            if (data.water_level_percent < 10.0f) {
                printf("⚠️  警告: 水位过低 (%.1f%%)\n", data.water_level_percent);
            } else if (data.water_level_percent > 90.0f) {
                printf("⚠️  警告: 水位过高 (%.1f%%)\n", data.water_level_percent);
            }
        } else {
            printf("%02d | 读取失败: %s\n", ++read_count, hcsr04_error_to_string(result));
        }
        
        // 等待1秒
        sleep(1);
    }
    
    // 清理资源
    printf("\n正在清理资源...\n");
    hcsr04_deinit();
    
    printf("程序已退出。\n");
    return 0;
}
