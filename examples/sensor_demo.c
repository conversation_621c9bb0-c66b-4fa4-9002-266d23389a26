/**
 * @file sensor_demo.c
 * @brief 传感器演示程序
 * <AUTHOR>
 * @date 2024
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include "../src/modules/logger/include/logger.h"
#include "../src/modules/hcsr04/include/hcsr04.h"

static volatile int running = 1;
static logger_t demo_logger = NULL;

void signal_handler(int sig) {
    printf("\n正在停止演示...\n");
    running = 0;
}

void demo_water_level_sensor() {
    printf("🌊 水位传感器演示\n");
    printf("================\n");

    printf("📋 传感器配置:\n");
    printf("  触发引脚: GPIO 268 (wPi 22, 物理引脚33)\n");
    printf("  回响引脚: GPIO 258 (wPi 23, 物理引脚35)\n");
    printf("  最大水位: %.1f cm\n", hcsr04_get_tank_height());
    printf("  传感器偏移: %.1f cm\n", hcsr04_get_sensor_offset());
    printf("\n");

    if (hcsr04_init() != HCSR04_OK) {
        printf("⚠️ 传感器初始化失败\n");
        printf("💡 请检查硬件连接:\n");
        printf("   - HC-SR04 VCC -> 5V\n");
        printf("   - HC-SR04 GND -> GND\n");
        printf("   - HC-SR04 Trig -> GPIO 268 (wPi 22, 物理引脚33)\n");
        printf("   - HC-SR04 Echo -> GPIO 258 (wPi 23, 物理引脚35)\n");
        return;
    }

    printf("✅ 传感器初始化成功\n");
    printf("🔄 开始测量（按Ctrl+C停止）...\n\n");

    int measurement_count = 0;
    float total_distance = 0;
    float min_distance = 999.0;
    float max_distance = 0;

    while (running) {
        float distance, water_level;
        hcsr04_read_distance(&distance);
        hcsr04_read_water_level(&water_level);

        if (distance > 0) {
            measurement_count++;
            total_distance += distance;

            if (distance < min_distance) min_distance = distance;
            if (distance > max_distance) max_distance = distance;

            printf("📏 测量 %d: 距离=%.2fcm, 水位=%.2fcm",
                   measurement_count, distance, water_level);

            // 水位状态指示
            if (water_level < 0) {
                printf(" [空]");
            } else if (water_level < 10) {
                printf(" [低]");
            } else if (water_level < 25) {
                printf(" [中]");
            } else {
                printf(" [高]");
            }

            // 水位百分比
            if (water_level >= 0) {
                float percentage = (water_level / hcsr04_get_tank_height()) * 100;
                printf(" (%.1f%%)", percentage);
            }

            printf("\n");

            logger_info(demo_logger, "水位测量: 距离=%.2fcm, 水位=%.2fcm",
                       distance, water_level);
        } else {
            printf("❌ 测量失败\n");
            logger_warn(demo_logger, "水位测量失败");
        }

        // 每10次测量显示统计
        if (measurement_count > 0 && measurement_count % 10 == 0) {
            float avg_distance = total_distance / measurement_count;
            printf("\n📊 统计信息 (最近%d次测量):\n", measurement_count);
            printf("   平均距离: %.2f cm\n", avg_distance);
            printf("   最小距离: %.2f cm\n", min_distance);
            printf("   最大距离: %.2f cm\n", max_distance);
            printf("   测量精度: %.2f cm\n", max_distance - min_distance);
            printf("\n");
        }

        sleep(1);
    }

    // 最终统计
    if (measurement_count > 0) {
        printf("\n📈 最终统计:\n");
        printf("   总测量次数: %d\n", measurement_count);
        printf("   平均距离: %.2f cm\n", total_distance / measurement_count);
        printf("   距离范围: %.2f - %.2f cm\n", min_distance, max_distance);
    }

    hcsr04_deinit();
    printf("✅ 水位传感器演示完成\n");
}

int main() {
    printf("🎯 GreenLand 传感器演示程序\n");
    printf("============================\n\n");

    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 初始化日志
    log_config_t log_config;
    logger_get_default_config(&log_config, "sensor_demo");
    demo_logger = logger_create(&log_config);

    if (demo_logger) {
        logger_info(demo_logger, "传感器演示程序启动");
    }

    // 运行演示
    demo_water_level_sensor();

    // 清理
    if (demo_logger) {
        logger_info(demo_logger, "传感器演示程序结束");
        logger_destroy(demo_logger);
    }

    printf("\n👋 演示程序结束\n");
    return 0;
}
