/**
 * @file advanced_usage.c
 * @brief BH1750光照传感器高级使用示例 - 多模式测试
 * <AUTHOR> Team
 * @date 2025-05-23
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <time.h>
#include "bh1750.h"

static volatile int running = 1;

// 信号处理函数
void signal_handler(int sig) {
    printf("\n收到退出信号，正在停止...\n");
    running = 0;
}

// 模式名称映射
const char* get_mode_name(bh1750_mode_t mode) {
    switch (mode) {
        case BH1750_MODE_CONT_H_RES:  return "连续高分辨率";
        case BH1750_MODE_CONT_H_RES2: return "连续高分辨率2";
        case BH1750_MODE_CONT_L_RES:  return "连续低分辨率";
        case BH1750_MODE_ONE_H_RES:   return "单次高分辨率";
        case BH1750_MODE_ONE_H_RES2:  return "单次高分辨率2";
        case BH1750_MODE_ONE_L_RES:   return "单次低分辨率";
        default: return "未知模式";
    }
}

// 光照应用场景判断
const char* get_application_scene(float lux) {
    if (lux < 0.1) return "完全黑暗 - 需要照明";
    if (lux < 1) return "月光 - 夜间模式";
    if (lux < 10) return "昏暗 - 需要补光";
    if (lux < 50) return "室内弱光 - 阅读困难";
    if (lux < 200) return "室内正常照明";
    if (lux < 500) return "办公室照明";
    if (lux < 1000) return "明亮室内";
    if (lux < 10000) return "阴天户外";
    if (lux < 50000) return "晴天阴影";
    return "直射阳光";
}

// 测试不同模式
void test_different_modes() {
    printf("\n=== 不同模式对比测试 ===\n");
    
    bh1750_mode_t modes[] = {
        BH1750_MODE_CONT_H_RES,
        BH1750_MODE_CONT_H_RES2,
        BH1750_MODE_CONT_L_RES
    };
    
    int num_modes = sizeof(modes) / sizeof(modes[0]);
    
    for (int i = 0; i < num_modes; i++) {
        printf("\n测试模式: %s\n", get_mode_name(modes[i]));
        
        if (bh1750_set_mode(modes[i]) != BH1750_OK) {
            printf("❌ 设置模式失败\n");
            continue;
        }
        
        sleep(1); // 等待模式切换
        
        // 连续读取3次求平均值
        float total_lux = 0;
        int valid_reads = 0;
        
        for (int j = 0; j < 3; j++) {
            bh1750_data_t data;
            if (bh1750_read_data(&data) == BH1750_OK) {
                total_lux += data.lux;
                valid_reads++;
                printf("  第%d次: %.2f lx (原始: %d)\n", j + 1, data.lux, data.raw_data);
            }
            usleep(500000); // 500ms
        }
        
        if (valid_reads > 0) {
            float avg_lux = total_lux / valid_reads;
            printf("  平均值: %.2f lx\n", avg_lux);
        }
    }
}

// 光照变化监控
void monitor_light_changes() {
    printf("\n=== 光照变化监控 ===\n");
    printf("监控光照变化 (按Ctrl+C退出)\n");
    printf("时间\t\t光照强度\t变化\t\t应用场景\n");
    printf("----------------------------------------------------------------\n");
    
    float last_lux = -1;
    int count = 0;
    
    while (running && count < 20) { // 最多监控20次
        bh1750_data_t data;
        
        if (bh1750_read_data(&data) == BH1750_OK) {
            count++;
            
            // 获取当前时间
            time_t now = time(NULL);
            struct tm* tm_info = localtime(&now);
            char time_str[20];
            strftime(time_str, sizeof(time_str), "%H:%M:%S", tm_info);
            
            // 计算变化
            char change_str[20] = "初始值";
            if (last_lux >= 0) {
                float change = data.lux - last_lux;
                if (change > 5) {
                    snprintf(change_str, sizeof(change_str), "↑+%.1f", change);
                } else if (change < -5) {
                    snprintf(change_str, sizeof(change_str), "↓%.1f", change);
                } else {
                    snprintf(change_str, sizeof(change_str), "→%.1f", change);
                }
            }
            
            printf("%s\t%.2f lx\t\t%s\t\t%s\n",
                   time_str, data.lux, change_str, get_application_scene(data.lux));
            
            last_lux = data.lux;
            
        } else {
            printf("数据读取失败\n");
        }
        
        sleep(3); // 每3秒读取一次
    }
}

int main() {
    printf("=== BH1750光照传感器高级示例 ===\n");
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 初始化传感器
    if (bh1750_init() != BH1750_OK) {
        printf("❌ BH1750初始化失败\n");
        return 1;
    }
    printf("✅ BH1750初始化成功\n");
    
    // 测试不同模式
    test_different_modes();
    
    // 监控光照变化
    monitor_light_changes();
    
    // 清理资源
    bh1750_deinit();
    printf("\n✅ 高级示例完成\n");
    
    return 0;
}
