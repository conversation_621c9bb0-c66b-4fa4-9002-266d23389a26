/**
 * @file basic_usage.c
 * @brief BH1750光照传感器基础使用示例
 * <AUTHOR> Team
 * @date 2025-05-23
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include "bh1750.h"

// 光照等级判断
const char* get_light_level(float lux) {
    if (lux < 1) return "黑暗";
    if (lux < 10) return "很暗";
    if (lux < 50) return "昏暗";
    if (lux < 200) return "室内照明";
    if (lux < 500) return "明亮";
    if (lux < 1000) return "很明亮";
    if (lux < 10000) return "强光";
    return "极强光";
}

int main() {
    printf("=== BH1750光照传感器基础示例 ===\n");
    
    // 1. 初始化传感器
    printf("正在初始化BH1750传感器...\n");
    if (bh1750_init() != BH1750_OK) {
        printf("❌ BH1750初始化失败\n");
        printf("请检查:\n");
        printf("  - 传感器是否连接到I2C-2总线\n");
        printf("  - I2C地址是否为0x23\n");
        printf("  - 是否以sudo权限运行\n");
        return 1;
    }
    printf("✅ BH1750初始化成功\n\n");
    
    // 2. 读取单次数据
    printf("--- 单次读取示例 ---\n");
    bh1750_data_t data;
    if (bh1750_read_data(&data) == BH1750_OK) {
        printf("光照强度: %.2f lx\n", data.lux);
        printf("原始数据: %d\n", data.raw_data);
        printf("光照等级: %s\n", get_light_level(data.lux));
    } else {
        printf("❌ 数据读取失败\n");
    }
    
    printf("\n--- 连续读取示例 ---\n");
    printf("连续读取5次，每次间隔2秒:\n");
    
    // 3. 连续读取数据
    for (int i = 0; i < 5; i++) {
        if (bh1750_read_data(&data) == BH1750_OK) {
            printf("第%d次: %.2f lx (%s)\n", 
                   i + 1, data.lux, get_light_level(data.lux));
            
            // 简单的数据验证
            if (data.lux < 0 || data.lux > 65535) {
                printf("  ⚠️  光照强度值异常\n");
            }
        } else {
            printf("第%d次: 读取失败\n", i + 1);
        }
        
        if (i < 4) sleep(2); // 最后一次不等待
    }
    
    // 4. 使用简化接口
    printf("\n--- 简化接口示例 ---\n");
    float lux;
    if (bh1750_read_lux(&lux) == BH1750_OK) {
        printf("当前光照: %.2f lx\n", lux);
    }
    
    // 5. 清理资源
    bh1750_deinit();
    printf("\n✅ 示例完成\n");
    
    return 0;
}
