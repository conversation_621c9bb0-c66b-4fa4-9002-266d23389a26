/**
 * @file environmental_monitor.c
 * @brief 环境监控示例 - 同时使用AHT20和BH1750
 * <AUTHOR> Team
 * @date 2025-05-23
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <time.h>
#include <math.h>

extern "C" {
    #include "aht20.h"
    #include "bh1750.h"
}

static volatile int running = 1;

// 信号处理函数
void signal_handler(int sig) {
    printf("\n收到退出信号，正在停止环境监控...\n");
    running = 0;
}

// 环境数据结构
typedef struct {
    float temperature;
    float humidity;
    float lux;
    time_t timestamp;
} environmental_data_t;

// 环境评估
void assess_environment(const environmental_data_t* env) {
    printf("\n=== 环境评估 ===\n");
    
    // 温度评估
    if (env->temperature < 16) {
        printf("🥶 温度偏低，建议增加供暖\n");
    } else if (env->temperature > 30) {
        printf("🥵 温度偏高，建议降温\n");
    } else if (env->temperature >= 20 && env->temperature <= 26) {
        printf("🌡️  温度适宜\n");
    } else {
        printf("🌡️  温度一般\n");
    }
    
    // 湿度评估
    if (env->humidity < 30) {
        printf("🏜️  湿度偏低，空气干燥\n");
    } else if (env->humidity > 70) {
        printf("💧 湿度偏高，空气潮湿\n");
    } else if (env->humidity >= 40 && env->humidity <= 60) {
        printf("💨 湿度适宜\n");
    } else {
        printf("💨 湿度一般\n");
    }
    
    // 光照评估
    if (env->lux < 50) {
        printf("🌙 光线不足，建议开灯\n");
    } else if (env->lux > 1000) {
        printf("☀️  光线充足\n");
    } else if (env->lux >= 200 && env->lux <= 500) {
        printf("💡 光线适宜\n");
    } else {
        printf("💡 光线一般\n");
    }
    
    // 综合舒适度
    int comfort_score = 0;
    if (env->temperature >= 20 && env->temperature <= 26) comfort_score += 3;
    else if (env->temperature >= 18 && env->temperature <= 28) comfort_score += 2;
    else comfort_score += 1;
    
    if (env->humidity >= 40 && env->humidity <= 60) comfort_score += 3;
    else if (env->humidity >= 30 && env->humidity <= 70) comfort_score += 2;
    else comfort_score += 1;
    
    if (env->lux >= 200 && env->lux <= 500) comfort_score += 3;
    else if (env->lux >= 50 && env->lux <= 1000) comfort_score += 2;
    else comfort_score += 1;
    
    printf("🏠 综合舒适度: ");
    if (comfort_score >= 8) printf("优秀 ⭐⭐⭐\n");
    else if (comfort_score >= 6) printf("良好 ⭐⭐\n");
    else if (comfort_score >= 4) printf("一般 ⭐\n");
    else printf("较差\n");
}

// 数据记录到CSV文件
void log_to_csv(const environmental_data_t* env, const char* filename) {
    FILE* file = fopen(filename, "a");
    if (file) {
        // 如果文件为空，写入标题行
        fseek(file, 0, SEEK_END);
        if (ftell(file) == 0) {
            fprintf(file, "时间,温度(°C),湿度(%%),光照(lx)\n");
        }
        
        // 写入数据
        struct tm* tm_info = localtime(&env->timestamp);
        char time_str[64];
        strftime(time_str, sizeof(time_str), "%Y-%m-%d %H:%M:%S", tm_info);
        
        fprintf(file, "%s,%.2f,%.2f,%.2f\n", 
                time_str, env->temperature, env->humidity, env->lux);
        fclose(file);
    }
}

int main() {
    printf("=== 环境监控系统 ===\n");
    printf("同时监控温度、湿度和光照\n\n");
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 初始化传感器
    printf("正在初始化传感器...\n");
    
    if (aht20_init() != AHT20_OK) {
        printf("❌ AHT20初始化失败\n");
        return 1;
    }
    printf("✅ AHT20 (温湿度) 初始化成功\n");
    
    if (bh1750_init() != BH1750_OK) {
        printf("❌ BH1750初始化失败\n");
        aht20_deinit();
        return 1;
    }
    printf("✅ BH1750 (光照) 初始化成功\n\n");
    
    printf("开始环境监控 (按Ctrl+C退出):\n");
    printf("时间\t\t温度\t湿度\t光照\t\t状态\n");
    printf("------------------------------------------------------------\n");
    
    int count = 0;
    const char* csv_filename = "environmental_data.csv";
    
    while (running) {
        environmental_data_t env = {0};
        env.timestamp = time(NULL);
        
        // 读取温湿度数据
        aht20_data_t aht20_data;
        if (aht20_read_data(&aht20_data) == AHT20_OK) {
            env.temperature = aht20_data.temperature;
            env.humidity = aht20_data.humidity;
        } else {
            printf("AHT20读取失败\n");
            continue;
        }
        
        // 读取光照数据
        bh1750_data_t bh1750_data;
        if (bh1750_read_data(&bh1750_data) == BH1750_OK) {
            env.lux = bh1750_data.lux;
        } else {
            printf("BH1750读取失败\n");
            continue;
        }
        
        count++;
        
        // 显示数据
        struct tm* tm_info = localtime(&env.timestamp);
        char time_str[20];
        strftime(time_str, sizeof(time_str), "%H:%M:%S", tm_info);
        
        printf("%s\t%.1f°C\t%.1f%%\t%.1f lx\t\t", 
               time_str, env.temperature, env.humidity, env.lux);
        
        // 简单状态指示
        if (env.temperature >= 20 && env.temperature <= 26 &&
            env.humidity >= 40 && env.humidity <= 60 &&
            env.lux >= 200 && env.lux <= 500) {
            printf("😊 舒适");
        } else {
            printf("😐 一般");
        }
        printf("\n");
        
        // 记录到CSV文件
        log_to_csv(&env, csv_filename);
        
        // 每10次进行详细评估
        if (count % 10 == 0) {
            assess_environment(&env);
            printf("📊 数据已保存到 %s\n", csv_filename);
            printf("------------------------------------------------------------\n");
        }
        
        sleep(5); // 每5秒监控一次
    }
    
    // 清理资源
    aht20_deinit();
    bh1750_deinit();
    
    printf("\n📊 监控结束，共记录 %d 次数据\n", count);
    printf("📁 数据文件: %s\n", csv_filename);
    printf("✅ 环境监控系统已退出\n");
    
    return 0;
}
