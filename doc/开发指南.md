# GreenLand 开发指南

## 📋 概述

本文档为 GreenLand 环境监控系统的开发者提供详细的开发指南，包括项目结构、编码规范、构建系统和扩展方法。

## 🏗️ 项目架构

### 目录结构

```
GreenLandV01/
├── src/                    # 源代码
│   ├── main.cpp           # 主程序 (C++)
│   └── modules/           # 传感器模块 (C)
│       ├── aht20/         # AHT20 温湿度传感器
│       │   ├── include/   # 头文件
│       │   └── src/       # 源文件
│       └── bh1750/        # BH1750 光照传感器
│           ├── include/   # 头文件
│           └── src/       # 源文件
├── tests/                 # 测试代码
│   ├── test_framework.h   # 测试框架
│   ├── *_unit_test.c      # 单元测试
│   ├── integration_test.c # 集成测试
│   └── performance_test.c # 性能测试
├── examples/              # 示例代码
│   ├── aht20/            # AHT20 示例
│   ├── bh1750/           # BH1750 示例
│   └── combined/         # 组合示例
├── scripts/              # 构建和测试脚本
├── doc/                  # 文档
├── bin/                  # 可执行文件 (生成)
├── lib/                  # 库文件 (生成)
├── build/                # 构建中间文件 (生成)
└── Log/                  # 日志文件
```

### 架构设计

```
┌─────────────────────────────────────┐
│           主程序 (main.cpp)          │
│         多线程监控系统               │
└─────────────┬───────────────────────┘
              │
    ┌─────────┴─────────┐
    │                   │
┌───▼────┐         ┌───▼────┐
│ AHT20  │         │ BH1750 │
│ 模块   │         │ 模块   │
│ (C)    │         │ (C)    │
└───┬────┘         └───┬────┘
    │                   │
    └─────────┬─────────┘
              │
         ┌────▼────┐
         │ I2C 总线 │
         │ /dev/i2c-2│
         └─────────┘
```

## 🛠️ 开发环境

### 系统要求

- **操作系统**: Armbian Linux (ARM64)
- **编译器**: GCC 11+ / G++ 11+
- **构建工具**: Make
- **版本控制**: Git
- **硬件**: Orange Pi Zero 2W

### 开发工具

```bash
# 安装基础开发工具
sudo apt update
sudo apt install build-essential git i2c-tools

# 可选工具
sudo apt install gdb valgrind pandoc  # 调试和文档工具
```

### 环境配置

```bash
# 启用 I2C
sudo nano /boot/armbianEnv.txt
# 添加: overlays=i2c2

# 重启后验证
sudo i2cdetect -y 2
```

## 📝 编码规范

### C 语言规范 (传感器模块)

#### 命名约定

```c
// 函数命名: 模块名_动作_对象
int aht20_read_data(aht20_data_t* data);
int bh1750_set_mode(bh1750_mode_t mode);

// 常量命名: 模块名_类型_名称
#define AHT20_I2C_ADDR          0x38
#define BH1750_CMD_POWER_ON     0x01

// 类型命名: 模块名_用途_t
typedef struct aht20_data_t;
typedef enum bh1750_mode_t;

// 变量命名: 小写下划线
static int i2c_fd = -1;
static bool initialized = false;
```

#### 函数设计

```c
/**
 * @brief 函数简要描述
 * @param param1 参数1描述
 * @param param2 参数2描述
 * @return 返回值描述
 *   - 成功: 返回值含义
 *   - 失败: 错误码含义
 */
int module_function(type param1, type param2) {
    // 参数验证
    if (!param1 || !param2) {
        return MODULE_ERROR_PARAM;
    }
    
    // 状态检查
    if (!initialized) {
        return MODULE_ERROR_INIT;
    }
    
    // 主要逻辑
    int result = do_something();
    if (result != 0) {
        return MODULE_ERROR_OPERATION;
    }
    
    return MODULE_OK;
}
```

#### 错误处理

```c
// 统一的错误码定义
typedef enum {
    MODULE_OK = 0,
    MODULE_ERROR_INIT = -1,
    MODULE_ERROR_I2C = -2,
    MODULE_ERROR_TIMEOUT = -3,
    MODULE_ERROR_PARAM = -4
} module_error_t;

// 错误处理模式
int function_with_error_handling(void) {
    int result = risky_operation();
    if (result != 0) {
        // 记录错误
        printf("错误: 操作失败 (错误码: %d)\n", result);
        
        // 清理资源
        cleanup_resources();
        
        // 返回错误码
        return MODULE_ERROR_OPERATION;
    }
    
    return MODULE_OK;
}
```

### C++ 语言规范 (主程序)

#### 类和对象

```cpp
// 类命名: PascalCase
class SensorManager {
private:
    std::atomic<bool> running_;
    std::mutex data_mutex_;
    
public:
    // 构造函数
    SensorManager() : running_(false) {}
    
    // 析构函数
    ~SensorManager() {
        stop();
    }
    
    // 公共接口
    bool start();
    void stop();
    SensorData readData();
};
```

#### 现代 C++ 特性

```cpp
// 使用智能指针
std::unique_ptr<SensorManager> manager = 
    std::make_unique<SensorManager>();

// 使用 auto 关键字
auto data = manager->readData();

// 使用范围 for 循环
for (const auto& reading : sensor_readings) {
    process_reading(reading);
}

// 使用 lambda 表达式
std::thread worker([this]() {
    this->worker_thread();
});
```

### 通用规范

#### 代码格式

```c
// 缩进: 4个空格
if (condition) {
    do_something();
    if (nested_condition) {
        do_nested_thing();
    }
}

// 大括号: K&R 风格
if (condition) {
    // 代码
} else {
    // 代码
}

// 函数定义
int function_name(int param1, 
                  const char* param2) {
    // 代码
}
```

#### 注释规范

```c
/**
 * @file filename.h
 * @brief 文件简要描述
 * <AUTHOR>
 * @date 创建日期
 */

/**
 * @brief 函数简要描述
 * 
 * 详细描述函数的功能、用法和注意事项。
 * 
 * @param param1 参数1的描述
 * @param param2 参数2的描述
 * @return 返回值描述
 * @retval 0 成功
 * @retval -1 失败
 * 
 * @note 特别注意事项
 * @warning 警告信息
 * 
 * @example
 * @code
 * int result = function_name(value1, value2);
 * if (result == 0) {
 *     printf("成功\n");
 * }
 * @endcode
 */
int function_name(int param1, const char* param2);
```

## 🔧 构建系统

### Makefile 结构

```makefile
# 项目配置
PROJECT_ROOT := $(shell pwd)
CC := gcc
CXX := g++
CFLAGS := -Wall -O2 -fPIC
CXXFLAGS := $(CFLAGS) -std=c++11

# 模块定义
MODULES := aht20 bh1750

# 自动生成规则
define MODULE_template
  # 模块特定的构建规则
endef

# 应用模板到所有模块
$(foreach mod,$(MODULES),$(eval $(call MODULE_template,$(mod))))
```

### 构建目标

```bash
# 编译所有组件
make all

# 只编译库
make libs

# 只编译测试
make tests

# 清理构建文件
make clean

# 显示帮助
make help
```

### 添加新模块

1. **创建模块目录**:
   ```bash
   mkdir -p src/modules/new_sensor/{include,src}
   ```

2. **实现标准接口**:
   ```c
   // include/new_sensor.h
   int new_sensor_init(void);
   void new_sensor_deinit(void);
   int new_sensor_read_data(new_sensor_data_t* data);
   ```

3. **更新 Makefile**:
   ```makefile
   MODULES := aht20 bh1750 new_sensor
   ```

4. **添加测试**:
   ```bash
   cp tests/aht20_unit_test.c tests/new_sensor_unit_test.c
   # 修改测试内容
   ```

## 🧪 测试开发

### 测试文件命名

- `*_unit_test.c`: 单元测试
- `*_integration_test.c`: 集成测试
- `*_performance_test.c`: 性能测试
- `*_simple_test.c`: 简单功能测试

### 测试模板

```c
#include "test_framework.h"
#include "your_module.h"

void test_basic_functionality(void) {
    TEST_CASE_START("基础功能测试");
    
    // 初始化
    int result = your_module_init();
    TEST_ASSERT(result == YOUR_MODULE_OK, "初始化应该成功");
    
    // 测试功能
    your_data_t data;
    result = your_module_read(&data);
    TEST_ASSERT(result == YOUR_MODULE_OK, "读取应该成功");
    
    // 验证数据
    TEST_ASSERT(data.value >= MIN_VALUE && data.value <= MAX_VALUE,
                "数据应该在有效范围内");
    
    // 清理
    your_module_deinit();
    
    TEST_CASE_END();
}

int main(void) {
    test_framework_init();
    
    TEST_SUITE_START("您的模块测试");
    test_basic_functionality();
    TEST_SUITE_END();
    
    test_framework_summary();
    return test_framework_get_exit_code();
}
```

## 📊 性能优化

### 性能指标

- **响应时间**: < 100ms
- **内存使用**: < 50MB
- **CPU 使用**: < 5%
- **I2C 频率**: 100kHz

### 优化技巧

1. **减少 I2C 通信**:
   ```c
   // 批量读取而不是单独读取
   uint8_t buffer[6];
   i2c_read(buffer, sizeof(buffer));
   ```

2. **缓存机制**:
   ```c
   static sensor_data_t cached_data;
   static time_t last_read_time = 0;
   
   if (time(NULL) - last_read_time < CACHE_TIMEOUT) {
       *data = cached_data;
       return SENSOR_OK;
   }
   ```

3. **异步处理**:
   ```cpp
   std::future<sensor_data_t> future_data = 
       std::async(std::launch::async, read_sensor_data);
   ```

## 🐛 调试技巧

### 日志系统

```c
#define DEBUG_LEVEL 3

#define LOG_ERROR(fmt, ...) \
    if (DEBUG_LEVEL >= 1) printf("[ERROR] " fmt "\n", ##__VA_ARGS__)

#define LOG_WARN(fmt, ...) \
    if (DEBUG_LEVEL >= 2) printf("[WARN] " fmt "\n", ##__VA_ARGS__)

#define LOG_INFO(fmt, ...) \
    if (DEBUG_LEVEL >= 3) printf("[INFO] " fmt "\n", ##__VA_ARGS__)

#define LOG_DEBUG(fmt, ...) \
    if (DEBUG_LEVEL >= 4) printf("[DEBUG] " fmt "\n", ##__VA_ARGS__)
```

### GDB 调试

```bash
# 编译调试版本
make CFLAGS="-g -O0" all

# 启动 GDB
sudo gdb ./bin/Tests/your_test

# GDB 命令
(gdb) break main
(gdb) run
(gdb) step
(gdb) print variable_name
(gdb) backtrace
```

### Valgrind 内存检查

```bash
# 安装 Valgrind
sudo apt install valgrind

# 检查内存泄漏
sudo valgrind --leak-check=full ./bin/Tests/your_test

# 检查内存错误
sudo valgrind --tool=memcheck ./bin/Tests/your_test
```

## 📚 文档编写

### 文档类型

1. **API 文档**: 函数接口说明
2. **用户指南**: 使用方法和示例
3. **开发指南**: 开发和扩展说明
4. **测试文档**: 测试方法和结果

### Markdown 规范

```markdown
# 一级标题

## 二级标题

### 代码示例

```c
int example_function(void) {
    return 0;
}
```

### 表格

| 参数 | 类型 | 描述 |
|------|------|------|
| data | ptr  | 数据指针 |

### 列表

- 项目1
- 项目2
  - 子项目
```

## 🚀 发布流程

### 版本管理

```bash
# 创建发布分支
git checkout -b release/v1.1.0

# 更新版本号
echo "1.1.0" > VERSION

# 运行完整测试
make clean && make all
sudo ./scripts/generate_test_report.sh

# 提交更改
git add .
git commit -m "Release v1.1.0"

# 创建标签
git tag -a v1.1.0 -m "Release version 1.1.0"
```

### 发布检查清单

- [ ] 所有测试通过
- [ ] 文档更新完整
- [ ] 性能指标达标
- [ ] 代码审查完成
- [ ] 版本号更新
- [ ] 变更日志更新

---

更多信息请参考：
- [API 参考](API_Reference.md)
- [测试指南](Testing_Guide.md)
- [故障排除](Troubleshooting.md)
