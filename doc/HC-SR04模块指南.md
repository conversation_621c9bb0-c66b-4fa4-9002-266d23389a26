# HC-SR04 超声波传感器模块使用指南

## 📋 概述

HC-SR04 是一款高精度、低功耗的超声波测距传感器，在 GreenLand 系统中用于水位检测。本模块提供了完整的驱动程序和 API 接口。

## 🔧 硬件规格

### 传感器参数
- **型号**: HC-SR04 超声波测距模块
- **工作电压**: 5V DC
- **工作电流**: 15mA
- **工作频率**: 40kHz
- **测量范围**: 2cm - 400cm
- **测量精度**: 3mm
- **测量角度**: 15°
- **触发信号**: 10μs TTL脉冲
- **回响信号**: 输出TTL电平信号

### 引脚定义
| 引脚 | 功能 | 连接 |
|------|------|------|
| VCC | 电源正极 | 5V |
| GND | 电源负极 | GND |
| Trig | 触发信号 | GPIO 22 (物理引脚 15) |
| Echo | 回响信号 | GPIO 23 (物理引脚 16) |

## 🔌 硬件连接

### Orange Pi Zero 2W 连接图

```
HC-SR04          Orange Pi Zero 2W
┌─────────┐      ┌─────────────────┐
│   VCC   │ ──── │ 5V   (Pin 2/4)  │
│   GND   │ ──── │ GND  (Pin 6/9)  │
│   Trig  │ ──── │ GPIO22 (Pin 15) │
│   Echo  │ ──── │ GPIO23 (Pin 16) │
└─────────┘      └─────────────────┘
```

### 安装建议
1. **传感器位置**: 安装在水箱顶部，垂直向下
2. **安装高度**: 距离水箱底部 5-10cm
3. **避免障碍物**: 确保超声波路径无遮挡
4. **防水处理**: 做好传感器防水保护

## 💻 软件接口

### 头文件包含
```c
#include "hcsr04.h"
```

### 基础使用示例

#### 简单测距
```c
#include "hcsr04.h"

int main() {
    // 初始化传感器
    if (hcsr04_init() != HCSR04_OK) {
        printf("传感器初始化失败\n");
        return -1;
    }

    // 读取距离
    float distance;
    if (hcsr04_read_distance(&distance) == HCSR04_OK) {
        printf("距离: %.2f cm\n", distance);
    }

    // 清理资源
    hcsr04_deinit();
    return 0;
}
```

#### 水位监测
```c
#include "hcsr04.h"

int main() {
    // 初始化传感器
    if (hcsr04_init() != HCSR04_OK) {
        printf("传感器初始化失败\n");
        return -1;
    }

    // 设置水箱参数 (用户实际水箱)
    hcsr04_set_tank_height(35.0f);   // 35cm 水箱 (用户实际尺寸)
    hcsr04_set_sensor_offset(3.0f);  // 3cm 偏移

    // 读取水位数据
    hcsr04_data_t data;
    if (hcsr04_read_data(&data) == HCSR04_OK && data.valid) {
        printf("距离: %.2f cm\n", data.distance_cm);
        printf("水位: %.2f cm (%.1f%%)\n",
               data.water_level_cm, data.water_level_percent);
    }

    hcsr04_deinit();
    return 0;
}
```

#### 高精度测量
```c
#include "hcsr04.h"

int main() {
    if (hcsr04_init() != HCSR04_OK) {
        return -1;
    }

    // 5次采样平均
    hcsr04_data_t data;
    if (hcsr04_read_averaged(&data, 5) == HCSR04_OK && data.valid) {
        printf("高精度距离: %.3f cm\n", data.distance_cm);
        printf("高精度水位: %.3f cm\n", data.water_level_cm);
    }

    hcsr04_deinit();
    return 0;
}
```

### 自定义配置
```c
#include "hcsr04.h"

int main() {
    // 创建自定义配置
    hcsr04_config_t config;
    hcsr04_get_default_config(&config);

    // 修改配置参数 (用户实际水箱)
    config.tank_height_cm = 35.0f;     // 35cm 水箱 (用户实际尺寸)
    config.sensor_offset_cm = 3.0f;    // 3cm 偏移
    config.timeout_us = 35000;         // 35ms 超时
    config.sample_count = 5;           // 5次采样

    // 使用自定义配置初始化
    if (hcsr04_init_with_config(&config) != HCSR04_OK) {
        printf("自定义配置初始化失败\n");
        return -1;
    }

    // 使用传感器...

    hcsr04_deinit();
    return 0;
}
```

## 🔧 API 参考

### 核心函数

#### `hcsr04_init()`
使用默认配置初始化传感器
```c
int hcsr04_init(void);
```

#### `hcsr04_init_with_config()`
使用自定义配置初始化传感器
```c
int hcsr04_init_with_config(const hcsr04_config_t* config);
```

#### `hcsr04_deinit()`
清理传感器资源
```c
void hcsr04_deinit(void);
```

#### `hcsr04_read_data()`
读取完整传感器数据
```c
int hcsr04_read_data(hcsr04_data_t* data);
```

#### `hcsr04_read_distance()`
读取距离值（简化接口）
```c
int hcsr04_read_distance(float* distance_cm);
```

#### `hcsr04_read_water_level()`
读取水位高度（简化接口）
```c
int hcsr04_read_water_level(float* water_level_cm);
```

### 配置函数

#### `hcsr04_set_tank_height()`
设置水箱高度
```c
int hcsr04_set_tank_height(float height_cm);
```

#### `hcsr04_get_tank_height()`
获取水箱高度
```c
float hcsr04_get_tank_height(void);
```

#### `hcsr04_set_sensor_offset()`
设置传感器偏移
```c
int hcsr04_set_sensor_offset(float offset_cm);
```

#### `hcsr04_get_sensor_offset()`
获取传感器偏移
```c
float hcsr04_get_sensor_offset(void);
```

### 高级函数

#### `hcsr04_read_averaged()`
多次采样平均读取
```c
int hcsr04_read_averaged(hcsr04_data_t* data, uint8_t sample_count);
```

#### `hcsr04_calibrate()`
传感器校准
```c
int hcsr04_calibrate(float known_distance_cm, float* measured_distance_cm);
```

## 📊 数据结构

### `hcsr04_data_t`
传感器数据结构
```c
typedef struct {
    float distance_cm;              // 距离 (cm)
    float water_level_cm;           // 水位高度 (cm)
    float water_level_percent;      // 水位百分比 (%)
    uint32_t echo_time_us;          // 回响时间 (μs)
    bool valid;                     // 数据有效性
    uint32_t timestamp;             // 时间戳
} hcsr04_data_t;
```

### `hcsr04_config_t`
配置结构
```c
typedef struct {
    uint8_t trig_pin;               // 触发引脚
    uint8_t echo_pin;               // 回响引脚
    float tank_height_cm;           // 水箱高度 (cm)
    float sensor_offset_cm;         // 传感器偏移 (cm)
    uint32_t timeout_us;            // 超时时间 (μs)
    uint8_t sample_count;           // 采样次数
} hcsr04_config_t;
```

## ⚠️ 注意事项

### 使用限制
1. **权限要求**: 需要 root 权限访问 GPIO
2. **测量频率**: 建议测量间隔 ≥ 60ms
3. **环境因素**: 温度、湿度会影响声速
4. **障碍物**: 避免测量路径有遮挡物

### 错误处理
```c
int result = hcsr04_read_data(&data);
if (result != HCSR04_OK) {
    printf("读取失败: %s\n", hcsr04_error_to_string(result));
    // 处理错误...
}
```

### 性能优化
1. **减少测量频率**: 避免过于频繁的测量
2. **使用平均值**: 多次采样提高精度
3. **错误重试**: 实现适当的重试机制
4. **资源管理**: 及时释放不再使用的资源

## 🔍 故障排除

### 常见问题

#### 1. 初始化失败
**症状**: `hcsr04_init()` 返回错误
**解决方案**:
- 检查是否使用 sudo 运行
- 确认 GPIO 引脚未被占用
- 检查硬件连接

#### 2. 读取超时
**症状**: 频繁出现 `HCSR04_ERROR_TIMEOUT`
**解决方案**:
- 检查传感器电源供应
- 确认 Echo 引脚连接正确
- 增加超时时间设置

#### 3. 数据异常
**症状**: 读取到异常距离值
**解决方案**:
- 检查测量路径是否有障碍物
- 确认传感器安装位置
- 使用多次采样平均

#### 4. 水位计算错误
**症状**: 水位百分比异常
**解决方案**:
- 检查水箱高度设置
- 确认传感器偏移配置
- 重新校准传感器

### 调试技巧
```c
// 启用详细日志
char status_info[512];
hcsr04_get_status_info(status_info, sizeof(status_info));
printf("%s\n", status_info);

// 校准传感器
float measured_distance;
hcsr04_calibrate(20.0f, &measured_distance);  // 20cm 已知距离
```

## 📚 相关文档

- [API 参考文档](API_Reference.md)
- [测试指南](Testing_Guide.md)
- [故障排除指南](Troubleshooting.md)
- [开发指南](Development_Guide.md)
