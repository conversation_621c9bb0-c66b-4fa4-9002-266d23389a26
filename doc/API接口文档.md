# GreenLand API 参考文档

## 📋 概述

本文档提供 GreenLand 环境监控系统的完整 API 参考，包括所有传感器模块的函数、数据结构和常量定义。

## 🌡️ AHT20 温湿度传感器 API

### 头文件

```c
#include "aht20.h"
```

### 数据结构

#### `aht20_data_t`

温湿度数据结构

```c
typedef struct {
    float temperature;  // 温度 (°C)
    float humidity;     // 湿度 (%)
} aht20_data_t;
```

#### `aht20_error_t`

错误码枚举

```c
typedef enum {
    AHT20_OK = 0,                // 操作成功
    AHT20_ERROR_INIT = -1,       // 初始化错误
    AHT20_ERROR_I2C = -2,        // I2C 通信错误
    AHT20_ERROR_TIMEOUT = -3,    // 超时错误
    AHT20_ERROR_CALIBRATION = -4,// 校准错误
    AHT20_ERROR_CRC = -5         // CRC 校验错误
} aht20_error_t;
```

### 常量定义

```c
#define AHT20_I2C_ADDR          0x38        // I2C 地址
#define AHT20_I2C_BUS           "/dev/i2c-2" // I2C 总线
#define AHT20_CMD_INIT          0xBE        // 初始化命令
#define AHT20_CMD_TRIGGER       0xAC        // 触发测量命令
#define AHT20_CMD_SOFT_RESET    0xBA        // 软复位命令
#define AHT20_CMD_STATUS        0x71        // 状态查询命令
```

### 核心函数

#### `aht20_init()`

初始化 AHT20 传感器

```c
int aht20_init(void);
```

**返回值**:
- `AHT20_OK`: 初始化成功
- `AHT20_ERROR_I2C`: I2C 通信失败
- `AHT20_ERROR_CALIBRATION`: 传感器校准失败

**示例**:
```c
if (aht20_init() == AHT20_OK) {
    printf("AHT20 初始化成功\n");
}
```

#### `aht20_deinit()`

清理 AHT20 传感器资源

```c
void aht20_deinit(void);
```

**示例**:
```c
aht20_deinit();
```

#### `aht20_read_data()`

读取温湿度数据

```c
int aht20_read_data(aht20_data_t* data);
```

**参数**:
- `data`: 指向数据结构的指针

**返回值**:
- `AHT20_OK`: 读取成功
- `AHT20_ERROR_INIT`: 传感器未初始化
- `AHT20_ERROR_I2C`: I2C 通信失败
- `AHT20_ERROR_TIMEOUT`: 测量超时

**示例**:
```c
aht20_data_t data;
if (aht20_read_data(&data) == AHT20_OK) {
    printf("温度: %.2f°C, 湿度: %.2f%%\n",
           data.temperature, data.humidity);
}
```

### 辅助函数

#### `aht20_get_status()`

获取传感器状态

```c
int aht20_get_status(uint8_t* status);
```

#### `aht20_soft_reset()`

软复位传感器

```c
int aht20_soft_reset(void);
```

#### `aht20_is_busy()`

检查传感器是否忙碌

```c
int aht20_is_busy(void);
```

#### `aht20_is_calibrated()`

检查传感器是否已校准

```c
int aht20_is_calibrated(void);
```

## 💡 BH1750 光照传感器 API

### 头文件

```c
#include "bh1750.h"
```

### 数据结构

#### `bh1750_data_t`

光照数据结构

```c
typedef struct {
    float lux;              // 光照强度 (lx)
    uint16_t raw_data;      // 原始数据
    bh1750_mode_t mode;     // 当前测量模式
} bh1750_data_t;
```

#### `bh1750_mode_t`

测量模式枚举

```c
typedef enum {
    BH1750_MODE_CONT_H_RES = 0,    // 连续高分辨率模式
    BH1750_MODE_CONT_H_RES2,       // 连续高分辨率模式2
    BH1750_MODE_CONT_L_RES,        // 连续低分辨率模式
    BH1750_MODE_ONE_H_RES,         // 单次高分辨率模式
    BH1750_MODE_ONE_H_RES2,        // 单次高分辨率模式2
    BH1750_MODE_ONE_L_RES          // 单次低分辨率模式
} bh1750_mode_t;
```

#### `bh1750_error_t`

错误码枚举

```c
typedef enum {
    BH1750_OK = 0,           // 操作成功
    BH1750_ERROR_INIT = -1,  // 初始化错误
    BH1750_ERROR_I2C = -2,   // I2C 通信错误
    BH1750_ERROR_TIMEOUT = -3,// 超时错误
    BH1750_ERROR_MODE = -4,  // 模式错误
    BH1750_ERROR_DATA = -5   // 数据错误
} bh1750_error_t;
```

### 常量定义

```c
#define BH1750_I2C_ADDR          0x23    // 默认 I2C 地址
#define BH1750_I2C_ADDR_ALT      0x5C    // 备用 I2C 地址
#define BH1750_I2C_BUS           "/dev/i2c-2" // I2C 总线

// 命令定义
#define BH1750_CMD_POWER_DOWN    0x00    // 断电模式
#define BH1750_CMD_POWER_ON      0x01    // 通电模式
#define BH1750_CMD_RESET         0x07    // 重置命令
#define BH1750_CMD_CONT_H_RES    0x10    // 连续高分辨率
#define BH1750_CMD_CONT_H_RES2   0x11    // 连续高分辨率2
#define BH1750_CMD_CONT_L_RES    0x13    // 连续低分辨率
```

### 核心函数

#### `bh1750_init()`

使用默认地址初始化 BH1750 传感器

```c
int bh1750_init(void);
```

#### `bh1750_init_with_addr()`

使用指定地址初始化 BH1750 传感器

```c
int bh1750_init_with_addr(uint8_t i2c_addr);
```

**参数**:
- `i2c_addr`: I2C 地址 (0x23 或 0x5C)

**示例**:
```c
// 使用默认地址
if (bh1750_init() == BH1750_OK) {
    printf("BH1750 初始化成功\n");
}

// 使用指定地址
if (bh1750_init_with_addr(0x5C) == BH1750_OK) {
    printf("BH1750 (0x5C) 初始化成功\n");
}
```

#### `bh1750_deinit()`

清理 BH1750 传感器资源

```c
void bh1750_deinit(void);
```

#### `bh1750_set_mode()`

设置测量模式

```c
int bh1750_set_mode(bh1750_mode_t mode);
```

**参数**:
- `mode`: 测量模式

**示例**:
```c
// 设置为连续高分辨率模式
bh1750_set_mode(BH1750_MODE_CONT_H_RES);
```

#### `bh1750_read_data()`

读取完整的光照数据

```c
int bh1750_read_data(bh1750_data_t* data);
```

**示例**:
```c
bh1750_data_t data;
if (bh1750_read_data(&data) == BH1750_OK) {
    printf("光照强度: %.2f lx (原始: %d)\n",
           data.lux, data.raw_data);
}
```

#### `bh1750_read_lux()`

读取光照强度值 (简化接口)

```c
int bh1750_read_lux(float* lux);
```

**示例**:
```c
float lux;
if (bh1750_read_lux(&lux) == BH1750_OK) {
    printf("光照强度: %.2f lx\n", lux);
}
```

### 电源管理函数

#### `bh1750_power_down()`

进入断电模式

```c
int bh1750_power_down(void);
```

#### `bh1750_power_on()`

退出断电模式

```c
int bh1750_power_on(void);
```

#### `bh1750_reset()`

重置传感器

```c
int bh1750_reset(void);
```

## 🔧 使用示例

### 基础使用

```c
#include "aht20.h"
#include "bh1750.h"

int main() {
    // 初始化传感器
    if (aht20_init() != AHT20_OK) {
        printf("AHT20 初始化失败\n");
        return -1;
    }

    if (bh1750_init() != BH1750_OK) {
        printf("BH1750 初始化失败\n");
        aht20_deinit();
        return -1;
    }

    // 设置 BH1750 模式
    bh1750_set_mode(BH1750_MODE_CONT_H_RES);

    // 读取数据
    aht20_data_t temp_hum;
    float lux;

    if (aht20_read_data(&temp_hum) == AHT20_OK &&
        bh1750_read_lux(&lux) == BH1750_OK) {

        printf("环境数据:\n");
        printf("  温度: %.2f°C\n", temp_hum.temperature);
        printf("  湿度: %.2f%%\n", temp_hum.humidity);
        printf("  光照: %.2f lx\n", lux);
    }

    // 清理资源
    aht20_deinit();
    bh1750_deinit();

    return 0;
}
```

### 错误处理

```c
int read_sensors_with_retry(aht20_data_t* temp_hum, float* lux) {
    int retry_count = 3;

    while (retry_count-- > 0) {
        int aht20_result = aht20_read_data(temp_hum);
        int bh1750_result = bh1750_read_lux(lux);

        if (aht20_result == AHT20_OK && bh1750_result == BH1750_OK) {
            return 0; // 成功
        }

        // 错误处理
        if (aht20_result != AHT20_OK) {
            printf("AHT20 读取失败: %d\n", aht20_result);
        }

        if (bh1750_result != BH1750_OK) {
            printf("BH1750 读取失败: %d\n", bh1750_result);
        }

        usleep(100000); // 等待 100ms 后重试
    }

    return -1; // 失败
}
```

## 📝 注意事项

### 使用要求

1. **权限**: 需要 root 权限访问 I2C 设备
2. **硬件**: 确保传感器正确连接到 I2C-2 总线
3. **初始化**: 使用前必须先调用初始化函数
4. **清理**: 程序结束前调用清理函数

### 性能建议

1. **读取频率**: AHT20 建议间隔 ≥2秒，BH1750 建议间隔 ≥1秒
2. **模式选择**: 根据精度需求选择合适的 BH1750 模式
3. **错误重试**: 实现适当的错误重试机制
4. **资源管理**: 及时释放不再使用的资源

## 🔗 兼容性

### 旧版本兼容

为了保持向后兼容性，提供了以下兼容接口：

```c
// AHT20 兼容接口
int aht20_read_data_legacy(void* data, size_t size);

// BH1750 兼容接口
int bh1750_read_data_legacy(void* data, size_t size);
```

### 平台支持

- **ARM64**: Orange Pi Zero 2W (主要支持)
- **ARM32**: Orange Pi 系列 (兼容)
- **x86_64**: 开发和测试环境 (模拟模式)

---

更多信息请参考：
- [测试指南](Testing_Guide.md)
- [开发指南](Development_Guide.md)
- [AHT20 模块指南](AHT20_Module_Guide.md)
- [BH1750 模块指南](BH1750_Module_Guide.md)
