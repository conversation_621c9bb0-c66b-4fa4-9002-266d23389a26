# GreenLand 故障排除指南

## 📋 概述

本文档提供 GreenLand 环境监控系统常见问题的诊断和解决方案。

## 🔧 常见问题

### 1. 权限相关问题

#### 问题: "Permission denied" 错误

**症状**:
```
错误: 无法打开I2C设备 /dev/i2c-2: Permission denied
```

**解决方案**:
```bash
# 方案1: 使用sudo运行
sudo ./bin/GreenLand

# 方案2: 添加用户到i2c组
sudo usermod -a -G i2c $USER
# 重新登录后生效

# 方案3: 修改设备权限
sudo chmod 666 /dev/i2c-2
```

#### 问题: 测试程序无法访问I2C

**诊断**:
```bash
# 检查I2C设备权限
ls -l /dev/i2c-*

# 检查用户组
groups $USER

# 检查I2C模块
lsmod | grep i2c
```

### 2. 硬件连接问题

#### 问题: 传感器未检测到

**症状**:
```
❌ AHT20传感器未连接，跳过测试
❌ BH1750传感器未连接，跳过测试
```

**诊断步骤**:
```bash
# 1. 扫描I2C总线
sudo i2cdetect -y 2

# 期望输出:
#      0  1  2  3  4  5  6  7  8  9  a  b  c  d  e  f
# 20: -- -- -- 23 -- -- -- -- -- -- -- -- -- -- -- --
# 30: -- -- -- -- -- -- -- -- 38 -- -- -- -- -- -- --
```

**解决方案**:
1. **检查物理连接**:
   ```
   传感器    →    Orange Pi Zero 2W
   VCC      →    3.3V (Pin 1)
   GND      →    GND (Pin 6)  
   SDA      →    I2C2_SDA (Pin 3)
   SCL      →    I2C2_SCL (Pin 5)
   ```

2. **检查I2C配置**:
   ```bash
   # 编辑配置文件
   sudo nano /boot/armbianEnv.txt
   
   # 确保包含:
   overlays=i2c2
   
   # 重启系统
   sudo reboot
   ```

3. **检查电源供应**:
   - 确认3.3V电源稳定
   - 检查接地连接
   - 测量传感器供电电压

#### 问题: I2C通信错误

**症状**:
```
错误: 无法设置I2C从设备地址 0x38: Device or resource busy
```

**解决方案**:
```bash
# 1. 重置I2C总线
sudo i2cdetect -y 2

# 2. 检查是否有其他程序占用
sudo lsof /dev/i2c-2

# 3. 重启I2C服务
sudo modprobe -r i2c-dev
sudo modprobe i2c-dev
```

### 3. 编译问题

#### 问题: 编译失败

**症状**:
```
gcc: error: unrecognized command line option '-std=c++11'
```

**解决方案**:
```bash
# 1. 检查编译器版本
gcc --version
g++ --version

# 2. 更新编译器
sudo apt update
sudo apt install build-essential

# 3. 清理重新编译
make clean && make all
```

#### 问题: 链接错误

**症状**:
```
undefined reference to 'aht20_init'
```

**解决方案**:
```bash
# 1. 检查库文件
ls -l lib/

# 2. 重新编译库
make clean
make libs

# 3. 检查链接路径
export LD_LIBRARY_PATH=$PWD/lib:$LD_LIBRARY_PATH
```

### 4. 运行时问题

#### 问题: 传感器读取失败

**症状**:
```
❌ AHT20: 读取失败 (错误码: -2), 错误次数: 5
```

**诊断**:
```bash
# 1. 检查传感器状态
sudo ./bin/Tests/aht20_simple_test

# 2. 检查I2C通信
sudo i2cget -y 2 0x38

# 3. 查看详细日志
tail -f Log/aht20_error.log
```

**解决方案**:
1. **重新初始化传感器**
2. **检查电源稳定性**
3. **降低I2C频率**
4. **增加重试机制**

#### 问题: 数据异常

**症状**:
```
⚠️ 温度异常: 125.00°C
⚠️ 湿度异常: 150.00%
```

**可能原因**:
- 传感器故障
- I2C通信干扰
- 电源不稳定
- 温度过高

**解决方案**:
```bash
# 1. 软复位传感器
# 在代码中调用 aht20_soft_reset()

# 2. 检查环境条件
# 确保工作温度在 -40°C 到 85°C 范围内

# 3. 增加滤波
# 实现数据平滑算法
```

### 5. 性能问题

#### 问题: 响应时间过长

**症状**:
```
❌ AHT20平均响应时间应该小于100ms
实际: 150.5ms
```

**优化方案**:
```c
// 1. 减少I2C等待时间
usleep(10000);  // 从20ms减少到10ms

// 2. 使用缓存机制
static time_t last_read = 0;
if (time(NULL) - last_read < 1) {
    return cached_data;
}

// 3. 异步读取
pthread_t read_thread;
pthread_create(&read_thread, NULL, async_read, NULL);
```

#### 问题: 内存使用过高

**诊断**:
```bash
# 1. 检查内存使用
ps aux | grep GreenLand

# 2. 使用valgrind检查
sudo valgrind --leak-check=full ./bin/GreenLand

# 3. 监控内存增长
watch -n 1 'ps -p $(pgrep GreenLand) -o pid,vsz,rss,comm'
```

### 6. 系统问题

#### 问题: 系统不稳定

**症状**:
- 程序随机崩溃
- 数据丢失
- 系统重启

**诊断步骤**:
```bash
# 1. 检查系统日志
sudo journalctl -f

# 2. 检查内核消息
sudo dmesg | tail -20

# 3. 检查硬件状态
sudo vcgencmd measure_temp
sudo vcgencmd get_throttled
```

**解决方案**:
1. **检查电源供应**
2. **降低系统负载**
3. **增加散热**
4. **更新系统**

## 🛠️ 诊断工具

### 系统诊断脚本

创建 `scripts/diagnose.sh`:
```bash
#!/bin/bash

echo "=== GreenLand 系统诊断 ==="

# 1. 系统信息
echo "系统信息:"
uname -a
cat /etc/os-release | grep PRETTY_NAME

# 2. I2C检查
echo -e "\nI2C设备:"
ls -l /dev/i2c-*
sudo i2cdetect -y 2

# 3. 权限检查
echo -e "\n权限检查:"
groups $USER
ls -l /dev/i2c-2

# 4. 编译环境
echo -e "\n编译环境:"
gcc --version | head -1
make --version | head -1

# 5. 库文件检查
echo -e "\n库文件:"
ls -l lib/ 2>/dev/null || echo "库文件不存在"

# 6. 进程检查
echo -e "\n相关进程:"
ps aux | grep -E "(GreenLand|i2c)" | grep -v grep
```

### 性能监控脚本

创建 `scripts/monitor.sh`:
```bash
#!/bin/bash

echo "=== 性能监控 ==="

while true; do
    # CPU和内存使用
    ps -p $(pgrep GreenLand) -o pid,pcpu,pmem,vsz,rss,comm 2>/dev/null
    
    # 系统温度
    echo "CPU温度: $(vcgencmd measure_temp 2>/dev/null || echo '未知')"
    
    # I2C状态
    echo "I2C设备数: $(ls /dev/i2c-* 2>/dev/null | wc -l)"
    
    echo "---"
    sleep 5
done
```

## 📞 获取帮助

### 日志收集

运行诊断并收集日志:
```bash
# 1. 运行诊断
sudo ./scripts/diagnose.sh > diagnosis.log 2>&1

# 2. 收集系统日志
sudo journalctl --since "1 hour ago" > system.log

# 3. 运行测试并记录
sudo ./scripts/generate_test_report.sh

# 4. 打包日志
tar -czf greenland_logs_$(date +%Y%m%d_%H%M%S).tar.gz \
    diagnosis.log system.log test_reports/ Log/
```

### 问题报告模板

```markdown
## 问题描述
简要描述遇到的问题

## 环境信息
- 硬件: Orange Pi Zero 2W
- 操作系统: [输出 uname -a]
- GreenLand版本: [版本号]

## 重现步骤
1. 步骤1
2. 步骤2
3. 步骤3

## 期望结果
描述期望的正常行为

## 实际结果
描述实际发生的情况

## 错误信息
```
[粘贴错误信息]
```

## 诊断信息
[附加 diagnosis.log 内容]

## 已尝试的解决方案
列出已经尝试过的解决方法
```

### 联系方式

- **GitHub Issues**: [项目Issues页面]
- **邮件支持**: <EMAIL>
- **文档中心**: [在线文档链接]

---

更多信息请参考：
- [API 参考](API_Reference.md)
- [开发指南](Development_Guide.md)
- [测试指南](Testing_Guide.md)
