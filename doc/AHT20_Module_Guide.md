# AHT20 温湿度传感器模块使用指南

## 📋 概述

AHT20是一款高精度数字温湿度传感器，采用I2C通信接口。本模块提供了完整的C语言API，支持温度和湿度的精确测量。

## 🔧 硬件配置

### 连接方式
- **I2C总线**: `/dev/i2c-2`
- **I2C地址**: `0x38`
- **供电电压**: 3.3V
- **通信协议**: I2C

### 引脚连接
```
AHT20    →    Orange Pi Zero 2W
VCC      →    3.3V
GND      →    GND
SDA      →    I2C2_SDA (Pin 3)
SCL      →    I2C2_SCL (Pin 5)
```

## 📚 API 参考

### 头文件
```c
#include "aht20.h"
```

### 数据结构

#### aht20_data_t
```c
typedef struct {
    float temperature;  // 温度 (°C)
    float humidity;     // 湿度 (%)
} aht20_data_t;
```

#### 错误码
```c
typedef enum {
    AHT20_OK = 0,              // 成功
    AHT20_ERROR_INIT = -1,     // 初始化错误
    AHT20_ERROR_I2C = -2,      // I2C通信错误
    AHT20_ERROR_TIMEOUT = -3,  // 超时错误
    AHT20_ERROR_CALIBRATION = -4, // 校准错误
    AHT20_ERROR_CRC = -5       // CRC校验错误
} aht20_error_t;
```

### 核心函数

#### 初始化和清理
```c
// 初始化AHT20传感器
int aht20_init(void);

// 释放AHT20资源
void aht20_deinit(void);
```

#### 数据读取
```c
// 读取温湿度数据
int aht20_read_data(aht20_data_t* data);
```

#### 状态查询
```c
// 获取传感器状态
int aht20_get_status(uint8_t* status);

// 检查是否已校准
int aht20_is_calibrated(void);

// 检查是否忙碌
int aht20_is_busy(void);
```

## 🚀 使用示例

### 基础使用
```c
#include <stdio.h>
#include "aht20.h"

int main() {
    // 1. 初始化
    if (aht20_init() != AHT20_OK) {
        printf("初始化失败\n");
        return 1;
    }
    
    // 2. 读取数据
    aht20_data_t data;
    if (aht20_read_data(&data) == AHT20_OK) {
        printf("温度: %.2f°C\n", data.temperature);
        printf("湿度: %.2f%%\n", data.humidity);
    }
    
    // 3. 清理
    aht20_deinit();
    return 0;
}
```

### 连续监控
```c
#include <stdio.h>
#include <unistd.h>
#include "aht20.h"

int main() {
    if (aht20_init() != AHT20_OK) {
        return 1;
    }
    
    for (int i = 0; i < 10; i++) {
        aht20_data_t data;
        if (aht20_read_data(&data) == AHT20_OK) {
            printf("第%d次: %.2f°C, %.2f%%\n", 
                   i+1, data.temperature, data.humidity);
        }
        sleep(2); // 每2秒读取一次
    }
    
    aht20_deinit();
    return 0;
}
```

## 📊 技术规格

### 测量范围
- **温度**: -40°C ~ +85°C
- **湿度**: 0% ~ 100% RH

### 精度
- **温度**: ±0.3°C (典型值)
- **湿度**: ±2% RH (典型值)

### 性能参数
- **分辨率**: 温度 0.01°C, 湿度 0.024% RH
- **响应时间**: < 5秒 (63% of step change)
- **测量时间**: ~80ms
- **工作电流**: 0.25μA (睡眠), 550μA (测量)

## ⚠️ 注意事项

### 使用要求
1. **权限**: 需要sudo权限访问I2C设备
2. **I2C使能**: 确保I2C-2总线已启用
3. **硬件连接**: 检查SDA/SCL连接是否正确

### 最佳实践
1. **初始化检查**: 始终检查初始化返回值
2. **错误处理**: 处理所有可能的错误码
3. **资源清理**: 程序结束前调用deinit函数
4. **读取间隔**: 建议读取间隔不少于1秒

### 故障排除
```bash
# 检查I2C设备
sudo i2cdetect -y 2

# 应该在地址0x38看到设备
#      0  1  2  3  4  5  6  7  8  9  a  b  c  d  e  f
# 30: -- -- -- -- -- -- -- -- 38 -- -- -- -- -- -- --
```

## 📁 示例代码

完整的示例代码位于 `examples/aht20/` 目录：
- `basic_usage.c` - 基础使用示例
- `advanced_usage.c` - 高级功能示例

## 🔗 相关链接

- [AHT20数据手册](https://files.seeedstudio.com/wiki/Grove-AHT20_I2C_Industrial_Grade_Temperature_and_Humidity_Sensor/AHT20-datasheet-2020-4-16.pdf)
- [I2C工具使用](https://www.kernel.org/doc/Documentation/i2c/tools/i2cdetect.rst)
