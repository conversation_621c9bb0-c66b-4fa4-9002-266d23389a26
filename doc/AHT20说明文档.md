# AHT20 温湿度传感器使用指南

## 🌡️ 硬件配置

- **传感器**: AHT20 温湿度传感器
- **I2C总线**: `/dev/i2c-2`
- **I2C地址**: `0x38`
- **连接确认**: 运行 `sudo i2cdetect -y 2` 应该看到地址0x38

## 🚀 快速开始

### 方法1: 使用便捷脚本（推荐）

```bash
# 简单测试（读取10次数据）
./aht20_test

# 完整测试（包含所有功能测试）
./aht20_full_test
```

### 方法2: 直接运行（使用RPATH，无需设置环境变量）

```bash
# 简单测试
sudo ./bin/Tests/aht20_simple_test

# 完整测试
sudo ./bin/Tests/aht20_test
```

### 方法3: 手动设置库路径

```bash
# 如果RPATH不工作，可以手动设置
sudo LD_LIBRARY_PATH=$PWD/lib:$LD_LIBRARY_PATH ./bin/Tests/aht20_simple_test
```

## 📊 测试输出示例

```
=== AHT20 简单测试程序 ===
正在初始化 AHT20 传感器...
AHT20 状态: 0x18
✅ AHT20 初始化成功
✅ AHT20初始化成功

第 1次读取: 温度=31.52°C, 湿度=17.87%
第 2次读取: 温度=31.52°C, 湿度=17.87%
第 3次读取: 温度=31.49°C, 湿度=17.85%
...
✅ 测试完成
```

## 🔧 编译

```bash
# 编译所有测试
make all

# 只编译测试程序
make tests

# 清理并重新编译
make clean && make all
```

## 📝 编程接口

```c
#include "aht20.h"

// 初始化传感器
if (aht20_init() == AHT20_OK) {
    // 读取数据
    aht20_data_t data;
    if (aht20_read_data(&data) == AHT20_OK) {
        printf("温度: %.2f°C, 湿度: %.2f%%\n", 
               data.temperature, data.humidity);
    }
    
    // 清理资源
    aht20_deinit();
}
```

## ❗ 故障排除

### 权限问题
```bash
# 错误: Permission denied
# 解决: 使用sudo运行
sudo ./aht20_test
```

### 库文件找不到
```bash
# 错误: cannot open shared object file: libaht20.so
# 解决1: 使用便捷脚本
./aht20_test

# 解决2: 手动设置库路径
LD_LIBRARY_PATH=$PWD/lib:$LD_LIBRARY_PATH ./bin/Tests/aht20_simple_test
```

### I2C设备不存在
```bash
# 错误: No such file or directory: /dev/i2c-2
# 解决: 检查I2C设备
ls /dev/i2c-*
sudo i2cdetect -l
```

### 传感器未检测到
```bash
# 错误: 无法设置I2C从设备地址
# 解决: 检查硬件连接和I2C地址
sudo i2cdetect -y 2
```

## 📁 文件说明

- `aht20_test` - 简单测试启动脚本
- `aht20_full_test` - 完整测试启动脚本
- `bin/Tests/aht20_simple_test` - 简单测试程序
- `bin/Tests/aht20_test` - 完整测试程序
- `lib/libaht20.so` - AHT20驱动库
- `src/modules/aht20/` - AHT20驱动源码
