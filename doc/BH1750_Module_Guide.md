# BH1750 光照传感器模块使用指南

## 📋 概述

BH1750是一款数字光照强度传感器，采用I2C通信接口。本模块提供了完整的C语言API，支持多种测量模式和高精度光照测量。

## 🔧 硬件配置

### 连接方式
- **I2C总线**: `/dev/i2c-2`
- **I2C地址**: `0x23` (ADDR引脚接地)
- **供电电压**: 3.3V
- **通信协议**: I2C

### 引脚连接
```
BH1750   →    Orange Pi Zero 2W
VCC      →    3.3V
GND      →    GND
SDA      →    I2C2_SDA (Pin 3)
SCL      →    I2C2_SCL (Pin 5)
ADDR     →    GND (设置I2C地址为0x23)
```

## 📚 API 参考

### 头文件
```c
#include "bh1750.h"
```

### 数据结构

#### bh1750_data_t
```c
typedef struct {
    float lux;              // 光照强度 (lx)
    uint16_t raw_data;      // 原始数据
    bh1750_mode_t mode;     // 当前测量模式
} bh1750_data_t;
```

#### 测量模式
```c
typedef enum {
    BH1750_MODE_CONT_H_RES = 0,    // 连续高分辨率模式 (1lx)
    BH1750_MODE_CONT_H_RES2,       // 连续高分辨率模式2 (0.5lx)
    BH1750_MODE_CONT_L_RES,        // 连续低分辨率模式 (4lx)
    BH1750_MODE_ONE_H_RES,         // 单次高分辨率模式 (1lx)
    BH1750_MODE_ONE_H_RES2,        // 单次高分辨率模式2 (0.5lx)
    BH1750_MODE_ONE_L_RES          // 单次低分辨率模式 (4lx)
} bh1750_mode_t;
```

#### 错误码
```c
typedef enum {
    BH1750_OK = 0,
    BH1750_ERROR_INIT = -1,     // 初始化错误
    BH1750_ERROR_I2C = -2,      // I2C通信错误
    BH1750_ERROR_TIMEOUT = -3,  // 超时错误
    BH1750_ERROR_MODE = -4,     // 模式错误
    BH1750_ERROR_DATA = -5      // 数据错误
} bh1750_error_t;
```

### 核心函数

#### 初始化和清理
```c
// 初始化BH1750传感器 (使用默认地址0x23)
int bh1750_init(void);

// 初始化BH1750传感器 (指定I2C地址)
int bh1750_init_with_addr(uint8_t i2c_addr);

// 释放BH1750资源
void bh1750_deinit(void);
```

#### 模式控制
```c
// 设置测量模式
int bh1750_set_mode(bh1750_mode_t mode);

// 电源控制
int bh1750_power_down(void);
int bh1750_power_on(void);

// 重置传感器
int bh1750_reset(void);
```

#### 数据读取
```c
// 读取完整数据
int bh1750_read_data(bh1750_data_t* data);

// 只读取光照强度值
int bh1750_read_lux(float* lux);
```

## 🚀 使用示例

### 基础使用
```c
#include <stdio.h>
#include "bh1750.h"

int main() {
    // 1. 初始化
    if (bh1750_init() != BH1750_OK) {
        printf("初始化失败\n");
        return 1;
    }
    
    // 2. 读取数据
    bh1750_data_t data;
    if (bh1750_read_data(&data) == BH1750_OK) {
        printf("光照强度: %.2f lx\n", data.lux);
        printf("原始数据: %d\n", data.raw_data);
    }
    
    // 3. 清理
    bh1750_deinit();
    return 0;
}
```

### 模式切换示例
```c
#include <stdio.h>
#include <unistd.h>
#include "bh1750.h"

int main() {
    if (bh1750_init() != BH1750_OK) {
        return 1;
    }
    
    // 测试不同分辨率模式
    bh1750_mode_t modes[] = {
        BH1750_MODE_CONT_H_RES,   // 1lx分辨率
        BH1750_MODE_CONT_H_RES2,  // 0.5lx分辨率
        BH1750_MODE_CONT_L_RES    // 4lx分辨率
    };
    
    for (int i = 0; i < 3; i++) {
        bh1750_set_mode(modes[i]);
        sleep(1); // 等待模式切换
        
        float lux;
        if (bh1750_read_lux(&lux) == BH1750_OK) {
            printf("模式%d: %.2f lx\n", i, lux);
        }
    }
    
    bh1750_deinit();
    return 0;
}
```

### 简化接口示例
```c
#include <stdio.h>
#include "bh1750.h"

int main() {
    if (bh1750_init() != BH1750_OK) {
        return 1;
    }
    
    // 使用简化接口只获取光照值
    float lux;
    if (bh1750_read_lux(&lux) == BH1750_OK) {
        printf("当前光照: %.2f lx\n", lux);
        
        // 光照等级判断
        if (lux < 10) {
            printf("光线很暗\n");
        } else if (lux < 200) {
            printf("室内照明\n");
        } else if (lux < 1000) {
            printf("明亮环境\n");
        } else {
            printf("强光环境\n");
        }
    }
    
    bh1750_deinit();
    return 0;
}
```

## 📊 技术规格

### 测量范围
- **光照强度**: 1 ~ 65535 lx

### 分辨率
- **高分辨率模式**: 1 lx
- **高分辨率模式2**: 0.5 lx  
- **低分辨率模式**: 4 lx

### 性能参数
- **测量时间**: 
  - 高分辨率: 120ms
  - 低分辨率: 16ms
- **工作电流**: 120μA (典型值)
- **待机电流**: 0.01μA
- **工作温度**: -40°C ~ +85°C

### 光照等级参考
| 光照强度 (lx) | 环境描述 | 应用场景 |
|---------------|----------|----------|
| < 1 | 黑暗 | 夜间无照明 |
| 1 ~ 10 | 很暗 | 月光、夜灯 |
| 10 ~ 50 | 昏暗 | 昏暗的室内 |
| 50 ~ 200 | 室内照明 | 一般室内照明 |
| 200 ~ 500 | 明亮 | 办公室照明 |
| 500 ~ 1000 | 很明亮 | 商店照明 |
| 1000 ~ 10000 | 强光 | 阴天户外 |
| > 10000 | 极强光 | 晴天户外 |

## ⚠️ 注意事项

### 使用要求
1. **权限**: 需要sudo权限访问I2C设备
2. **I2C使能**: 确保I2C-2总线已启用
3. **硬件连接**: 检查SDA/SCL连接是否正确
4. **地址设置**: ADDR引脚接地时地址为0x23

### 最佳实践
1. **初始化检查**: 始终检查初始化返回值
2. **模式选择**: 根据应用需求选择合适的分辨率模式
3. **测量间隔**: 连续模式下建议间隔至少200ms
4. **电源管理**: 长期不使用时可调用power_down节能

### 故障排除
```bash
# 检查I2C设备
sudo i2cdetect -y 2

# 应该在地址0x23看到设备
#      0  1  2  3  4  5  6  7  8  9  a  b  c  d  e  f
# 20: -- -- -- 23 -- -- -- -- -- -- -- -- -- -- -- --
```

## 📁 示例代码

完整的示例代码位于 `examples/bh1750/` 目录：
- `basic_usage.c` - 基础使用示例
- `advanced_usage.c` - 多模式测试示例

## 🔗 相关链接

- [BH1750数据手册](https://www.mouser.com/datasheet/2/348/bh1750fvi-e-186247.pdf)
- [I2C工具使用](https://www.kernel.org/doc/Documentation/i2c/tools/i2cdetect.rst)
