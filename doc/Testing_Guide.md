# GreenLand 测试指南

## 📋 概述

本文档详细介绍了 GreenLand 环境监控系统的测试框架、测试策略和测试执行方法。

## 🧪 测试框架

### 测试框架特性

GreenLand 使用自定义的轻量级测试框架，具有以下特性：

- **彩色输出**: 使用颜色区分测试结果
- **性能测试**: 内置性能测试工具
- **内存检测**: 简单的内存泄漏检测
- **统计报告**: 详细的测试统计信息
- **信号处理**: 支持优雅的测试中断

### 测试宏

```c
// 基础断言
TEST_ASSERT(condition, message)
TEST_ASSERT_EQUAL(expected, actual, message)
TEST_ASSERT_FLOAT_EQUAL(expected, actual, tolerance, message)
TEST_ASSERT_NOT_NULL(ptr, message)
TEST_ASSERT_NULL(ptr, message)

// 测试套件
TEST_SUITE_START("套件名称")
// ... 测试用例 ...
TEST_SUITE_END()

// 测试用例
TEST_CASE_START("用例名称")
// ... 测试代码 ...
TEST_CASE_END()

// 跳过测试
TEST_SKIP(message)
```

## 🏗️ 测试架构

### 测试层次

```
测试层次结构:
├── 单元测试 (Unit Tests)
│   ├── AHT20 单元测试
│   └── BH1750 单元测试
├── 集成测试 (Integration Tests)
│   ├── 传感器协同测试
│   ├── 多线程测试
│   └── 数据一致性测试
├── 性能测试 (Performance Tests)
│   ├── 响应时间测试
│   ├── 内存使用测试
│   └── 吞吐量测试
└── 系统测试 (System Tests)
    ├── 稳定性测试
    └── 端到端测试
```

### 测试文件结构

```
tests/
├── test_framework.h          # 测试框架头文件
├── test_framework.c          # 测试框架实现
├── aht20_unit_test.c         # AHT20 单元测试
├── bh1750_unit_test.c        # BH1750 单元测试
├── integration_test.c        # 集成测试
├── performance_test.c        # 性能测试
├── aht20_simple_test.c       # AHT20 简单测试
├── aht20_test.c              # AHT20 完整测试
├── bh1750_simple_test.c      # BH1750 简单测试
└── bh1750_test.c             # BH1750 完整测试
```

## 🚀 运行测试

### 编译测试

```bash
# 编译所有测试
make clean && make all

# 只编译测试程序
make tests

# 查看检测到的测试文件
make debug-tests
```

### 运行单个测试

```bash
# 运行 AHT20 单元测试
sudo ./bin/Tests/aht20_unit_test

# 运行 BH1750 单元测试
sudo ./bin/Tests/bh1750_unit_test

# 运行集成测试
sudo ./bin/Tests/integration_test

# 运行性能测试
sudo ./bin/Tests/performance_test
```

### 运行所有测试

```bash
# 使用测试运行器
sudo ./scripts/run_tests.sh

# 运行特定传感器测试
sudo ./scripts/test_sensors.sh -s aht20
sudo ./scripts/test_sensors.sh -s bh1750
sudo ./scripts/test_sensors.sh -s all
```

### 生成测试报告

```bash
# 生成完整测试报告
sudo ./scripts/generate_test_report.sh
```

## 📊 测试覆盖

### 单元测试覆盖

**AHT20 传感器**:
- ✅ 初始化和清理
- ✅ 数据读取功能
- ✅ 状态检查功能
- ✅ 错误处理
- ✅ 性能测试
- ✅ 数据一致性

**BH1750 传感器**:
- ✅ 初始化和清理
- ✅ 模式设置
- ✅ 数据读取功能
- ✅ 所有模式测试
- ✅ 电源管理
- ✅ 性能测试

### 集成测试覆盖

- ✅ 传感器协同工作
- ✅ 多线程环境
- ✅ 数据一致性
- ✅ 系统稳定性
- ✅ 资源管理

### 性能测试覆盖

- ✅ 响应时间测试
- ✅ 内存使用测试
- ✅ 系统吞吐量
- ✅ CPU 使用率

## 🎯 测试策略

### 测试原则

1. **全面性**: 覆盖所有公共 API 和关键路径
2. **可靠性**: 测试结果应该稳定可重复
3. **性能**: 测试执行时间应该合理
4. **可维护性**: 测试代码应该清晰易懂

### 测试数据

- **温度范围**: -40°C 到 85°C
- **湿度范围**: 0% 到 100%
- **光照范围**: 0 到 65535 lx
- **响应时间**: < 100ms (AHT20), < 50ms (BH1750)
- **内存使用**: < 50MB
- **CPU 使用**: < 5%

### 边界条件测试

- 传感器未连接
- I2C 通信错误
- 空指针参数
- 无效参数值
- 系统资源不足

## 🔧 测试配置

### 环境要求

- **硬件**: Orange Pi Zero 2W
- **操作系统**: Armbian Linux
- **权限**: sudo (访问 I2C 设备)
- **传感器**: AHT20 (0x38), BH1750 (0x23)

### 测试参数

```c
// 性能测试配置
#define PERFORMANCE_TEST_COUNT 1000
#define RESPONSE_TIME_SAMPLES 100
#define STABILITY_TEST_DURATION 30  // 秒

// 容差配置
#define TEMPERATURE_TOLERANCE 0.5   // °C
#define HUMIDITY_TOLERANCE 2.0      // %
#define LUX_TOLERANCE 10.0          // lx
```

## 📈 测试报告

### 报告内容

测试报告包含以下信息：

1. **系统信息**: 硬件、操作系统、编译器版本
2. **硬件连接**: I2C 设备扫描结果
3. **测试结果**: 每个测试套件的详细结果
4. **性能指标**: 响应时间、内存使用、吞吐量
5. **错误分析**: 失败测试的详细信息

### 报告格式

- **Markdown**: 详细的文本报告
- **HTML**: 可视化的网页报告
- **日志文件**: 每个测试的详细日志

## 🐛 故障排除

### 常见问题

1. **权限错误**
   ```bash
   sudo ./bin/Tests/test_name
   ```

2. **I2C 设备未找到**
   ```bash
   sudo i2cdetect -y 2
   lsmod | grep i2c
   ```

3. **编译错误**
   ```bash
   make clean && make all
   ```

4. **测试超时**
   - 检查传感器连接
   - 确认 I2C 总线正常
   - 降低测试强度

### 调试技巧

- 使用 `TEST_SKIP()` 跳过有问题的测试
- 查看详细的测试日志
- 使用性能分析工具
- 检查内存泄漏报告

## 📝 编写新测试

### 测试模板

```c
#include "test_framework.h"
#include "your_module.h"

void test_your_function(void) {
    TEST_CASE_START("测试用例名称");
    
    // 测试代码
    int result = your_function();
    TEST_ASSERT(result == EXPECTED_VALUE, "功能应该正常工作");
    
    TEST_CASE_END();
}

int main(void) {
    test_framework_init();
    
    TEST_SUITE_START("您的测试套件");
    test_your_function();
    TEST_SUITE_END();
    
    test_framework_summary();
    return test_framework_get_exit_code();
}
```

### 最佳实践

1. **清晰的测试名称**: 使用描述性的测试名称
2. **独立的测试**: 每个测试应该独立运行
3. **适当的断言**: 使用合适的断言宏
4. **资源清理**: 确保测试后清理资源
5. **错误处理**: 测试各种错误情况

---

更多信息请参考：
- [API 文档](API_Reference.md)
- [开发指南](Development_Guide.md)
- [故障排除](Troubleshooting.md)
