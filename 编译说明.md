# 🔧 GreenLand 编译和使用说明

## 🚀 快速开始

### 1. 一键编译和运行

```bash
# 编译程序
./scripts/build_simple.sh build

# 编译并运行测试
./scripts/build_simple.sh test

# 编译并运行监控
./scripts/build_simple.sh run
```

### 2. 手动编译

```bash
# 创建目录
mkdir -p bin

# 编译（无摄像头支持）
g++ -Wall -Wextra -std=c++11 -O2 \
    -Isrc/modules/logger/include \
    -Isrc/modules/hcsr04/include \
    -Isrc/modules/aht20/include \
    -Isrc/modules/bh1750/include \
    src/main.cpp \
    src/modules/logger/src/logger.c \
    src/modules/hcsr04/src/hcsr04.c \
    src/modules/aht20/src/aht20.c \
    src/modules/bh1750/src/bh1750.c \
    -lwiringPi -lpthread \
    -o bin/GreenLand

# 编译（含摄像头支持）
g++ -Wall -Wextra -std=c++11 -O2 -DENABLE_CAMERA \
    -Isrc/modules/logger/include \
    -Isrc/modules/hcsr04/include \
    -Isrc/modules/aht20/include \
    -Isrc/modules/bh1750/include \
    -Isrc/modules/camera/include \
    $(pkg-config --cflags opencv4) \
    src/main.cpp \
    src/modules/logger/src/logger.c \
    src/modules/hcsr04/src/hcsr04.c \
    src/modules/aht20/src/aht20.c \
    src/modules/bh1750/src/bh1750.c \
    src/modules/camera/src/camera.cpp \
    -lwiringPi -lpthread \
    $(pkg-config --libs opencv4) \
    -o bin/GreenLand
```

## 📦 依赖安装

### 基础依赖

```bash
# 更新系统
sudo apt-get update && sudo apt-get upgrade

# 安装编译工具
sudo apt-get install build-essential cmake git

# 安装wiringPi库
sudo apt-get install wiringpi

# 或者从源码安装wiringPi
git clone https://github.com/WiringPi/WiringPi.git
cd WiringPi
./build
```

### 摄像头支持（可选）

```bash
# 安装OpenCV
sudo apt-get install libopencv-dev

# 或者安装OpenCV4
sudo apt-get install libopencv-contrib-dev

# 检查OpenCV版本
pkg-config --modversion opencv4
```

### 性能测试工具（可选）

```bash
# 安装压力测试工具
sudo apt-get install stress

# 安装网络工具
sudo apt-get install iputils-ping dnsutils
```

## 🏃 运行程序

### 基本使用

```bash
# 显示帮助
./bin/GreenLand -h

# 运行测试模式（推荐首次使用）
./bin/GreenLand -t

# 运行多线程监控模式
./bin/GreenLand

# 显示系统信息
./bin/GreenLand -i
```

### 权限设置

```bash
# 添加用户到GPIO组
sudo usermod -a -G gpio $USER

# 添加用户到I2C组
sudo usermod -a -G i2c $USER

# 重新登录使权限生效
# 或者使用sudo运行
sudo ./bin/GreenLand -t
```

## 🧪 测试和性能

### 功能测试

```bash
# 快速功能测试
./scripts/test_system.sh quick

# 稳定性测试
./scripts/test_system.sh stability

# 全部测试
./scripts/test_system.sh all
```

### 性能测试

```bash
# 系统信息
./scripts/performance_test.sh system

# 性能测试
./scripts/performance_test.sh performance

# 压力测试
./scripts/performance_test.sh stress

# 网络测试
./scripts/performance_test.sh network

# 全部测试
./scripts/performance_test.sh all
```

## 📁 文件结构

### 编译产物

```
bin/
├── GreenLand           # 主程序
└── Tests/              # 测试程序目录
    ├── quick_test          # 快速测试
    ├── performance_test    # 性能测试
    ├── system_stability_test # 稳定性测试
    ├── basic_usage         # 基本使用示例
    └── sensor_demo         # 传感器演示
```

### 媒体文件

```
media/
├── photos/             # 照片存储
│   └── test_20241225_103000.jpg
└── videos/             # 视频存储
    └── test_video_20241225_103000.mp4
```

### 日志文件

```
Log/
├── greenland.log           # 主程序日志
├── water_sensor.log        # 水位传感器日志
├── temperature_sensor.log  # 温湿度传感器日志
├── light_sensor.log        # 光照传感器日志
├── camera.log              # 摄像头日志
└── perf_test.log          # 性能测试日志
```

## 🔧 故障排除

### 编译问题

**问题**: `wiringPi.h: No such file or directory`
```bash
# 解决方案
sudo apt-get install wiringpi
# 或者检查是否安装
ldconfig -p | grep wiringPi
```

**问题**: `opencv2/opencv.hpp: No such file or directory`
```bash
# 解决方案
sudo apt-get install libopencv-dev
# 或者禁用摄像头支持
./scripts/build_simple.sh build  # 会自动检测并禁用
```

**问题**: 编译警告过多
```bash
# 解决方案：所有警告已修复
# 如果仍有警告，请检查编译器版本
gcc --version
g++ --version
```

### 运行问题

**问题**: `Permission denied`
```bash
# 解决方案
chmod +x bin/GreenLand
sudo ./bin/GreenLand -t
```

**问题**: `GPIO setup failed`
```bash
# 解决方案
sudo usermod -a -G gpio $USER
# 重新登录或使用sudo
```

**问题**: 传感器读取失败
```bash
# 检查硬件连接
# HC-SR04:
#   VCC -> 5V (物理引脚2)
#   GND -> GND (物理引脚6)
#   Trig -> GPIO 268 (wPi 22, 物理引脚33)
#   Echo -> GPIO 258 (wPi 23, 物理引脚35)
```

**问题**: 摄像头无法使用
```bash
# 检查设备
ls /dev/video*

# 检查权限
sudo usermod -a -G video $USER

# 测试摄像头
v4l2-ctl --list-devices
```

## 📊 性能优化

### 编译优化

```bash
# 使用-O3优化
gcc -O3 -march=native ...

# 启用链接时优化
gcc -flto ...

# 静态链接（减少依赖）
gcc -static ...
```

### 运行时优化

```bash
# 设置CPU频率
sudo cpufreq-set -g performance

# 调整进程优先级
nice -n -10 ./bin/GreenLand

# 绑定CPU核心
taskset -c 0 ./bin/GreenLand
```

### 内存优化

```bash
# 监控内存使用
top -p $(pgrep GreenLand)

# 检查内存泄漏
valgrind --leak-check=full ./bin/GreenLand -t
```

## 🔄 自动化部署

### 创建systemd服务

```bash
# 创建服务文件
sudo tee /etc/systemd/system/greenland.service << EOF
[Unit]
Description=GreenLand Smart Agriculture Monitor
After=network.target

[Service]
Type=simple
User=orangepi
WorkingDirectory=/home/<USER>/Workspace/GreenLandV01
ExecStart=/home/<USER>/Workspace/GreenLandV01/bin/GreenLand
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 启用服务
sudo systemctl enable GreenLand.service
sudo systemctl start GreenLand.service

# 查看状态
sudo systemctl status GreenLand.service
```

### 定时任务

```bash
# 编辑crontab
crontab -e

# 添加定时任务
# 每小时运行一次测试
0 * * * * cd /home/<USER>/Workspace/GreenLandV01 && ./bin/GreenLand -t >> /tmp/greenland_cron.log 2>&1

# 每天凌晨清理旧日志
0 0 * * * find /home/<USER>/Workspace/GreenLandV01/Log -name "*.log" -mtime +7 -delete
```

## 📞 技术支持

- **作者**: aubuty
- **邮箱**: <EMAIL>
- **项目地址**: https://gitee.com/lvjing_nmg/hardware-fundamentals.git

---

🌱 **愿景**: 让智能农业技术更加普及和易用！
