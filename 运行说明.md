# 🌱 GreenLand 运行说明

## 🚀 快速开始

### 1. 编译程序

```bash
# 简单编译（推荐）
./scripts/build_simple.sh build

# 或者使用完整Makefile
make clean && make
```

### 2. 运行程序

```bash
# 运行测试模式（推荐首次使用）
./bin/greenland -t

# 运行多线程监控模式
./bin/greenland

# 查看帮助
./bin/greenland -h
```

### 3. 多线程监控特性

- **🌊 水位传感器线程**: 独立线程，5秒间隔读取数据
- **📷 摄像头线程**: 独立线程，60秒间隔自动拍照
- **📊 主监控线程**: 30秒间隔显示系统状态
- **🔄 线程安全**: 使用互斥锁保护共享数据

### 3. 一键操作

```bash
# 编译并运行测试
./scripts/build_simple.sh test

# 编译并运行监控
./scripts/build_simple.sh run
```

## 📁 媒体文件存储

程序会自动创建以下目录结构：

```
media/
├── photos/     # 📸 照片存储目录
└── videos/     # 🎥 视频存储目录
```

### 文件命名规则

- **照片**: `test_YYYYMMDD_HHMMSS.jpg`
- **视频**: `test_video_YYYYMMDD_HHMMSS.mp4`

## 🔧 功能说明

### 测试模式 (`-t`)

运行所有传感器和摄像头的功能测试：

1. **水位传感器测试**
   - 初始化HC-SR04传感器
   - 进行5次距离和水位测量
   - 显示测量结果和百分比

2. **摄像头测试**（如果可用）
   - 扫描USB摄像头设备
   - 拍摄测试照片 → `media/photos/`
   - 录制5秒测试视频 → `media/videos/`

### 多线程监控模式（默认）

启动独立的传感器线程：

1. **🌊 水位传感器线程**
   - 5秒间隔读取传感器数据
   - 实时更新水位状态
   - 自动水位报警（低于5cm警告）

2. **📷 摄像头线程**（如果启用）
   - 60秒间隔自动拍照
   - 照片自动保存到 `media/photos/`
   - 文件名格式：`auto_YYYYMMDD_HHMMSS.jpg`

3. **📊 主监控线程**
   - 30秒间隔显示系统状态
   - 显示所有传感器的实时数据
   - 记录运行日志到 `Log/` 目录
   - 按 `Ctrl+C` 优雅退出所有线程

## 📊 日志文件

程序会在 `Log/` 目录下创建日志文件：

```
Log/
├── greenland.log          # 主程序日志
├── greenland_error.log    # 错误日志
└── greenland_warning.log  # 警告日志
```

## 🧪 测试程序

所有测试程序位于 `bin/Tests/` 目录：

```bash
# 运行各种测试程序
./bin/Tests/quick_test              # 快速功能测试
./bin/Tests/system_stability_test   # 系统稳定性测试
./bin/Tests/performance_test        # 性能测试
./bin/Tests/basic_usage             # 基本使用示例
./bin/Tests/sensor_demo             # 传感器演示

# 或使用脚本运行
./scripts/test_system.sh quick      # 快速测试
./scripts/test_system.sh stability  # 稳定性测试
./scripts/performance_test.sh all   # 性能测试
```

## 🔌 硬件连接

### HC-SR04 水位传感器

```
HC-SR04    →    Orange Pi Zero 2W
VCC        →    5V (物理引脚2)
GND        →    GND (物理引脚6)
Trig       →    GPIO 268 (wPi 22, 物理引脚33)
Echo       →    GPIO 258 (wPi 23, 物理引脚35)
```

### USB摄像头

- 插入任意USB接口
- 程序会自动扫描 `/dev/video0` 到 `/dev/video7`

## ⚠️ 注意事项

1. **权限要求**
   ```bash
   # 如果遇到权限问题，使用sudo运行
   sudo ./bin/greenland -t
   ```

2. **硬件检测**
   - 如果没有连接传感器，程序会显示警告但继续运行
   - 摄像头功能需要OpenCV库支持

3. **依赖检查**
   ```bash
   # 检查wiringPi库
   ldconfig -p | grep wiringPi

   # 检查OpenCV库
   pkg-config --exists opencv4 && echo "OpenCV4 可用"
   ```

## 🛠️ 故障排除

### 编译问题

```bash
# 安装基础依赖
sudo apt-get update
sudo apt-get install build-essential cmake

# 安装wiringPi
sudo apt-get install wiringpi

# 安装OpenCV（可选）
sudo apt-get install libopencv-dev
```

### 运行问题

```bash
# GPIO权限问题
sudo usermod -a -G gpio $USER
# 重新登录后生效

# I2C权限问题
sudo usermod -a -G i2c $USER

# 启用I2C（如果需要其他传感器）
sudo orangepi-config
# 选择 System -> Hardware -> i2c1 -> Enable
```

### 传感器问题

1. **HC-SR04无读数**
   - 检查接线是否正确
   - 确认5V供电
   - 检查GPIO引脚配置

2. **摄像头无法使用**
   - 检查USB连接
   - 确认OpenCV库已安装
   - 尝试不同的USB接口

## 📈 性能优化

### 降低CPU使用率

```bash
# 编译优化版本
gcc -O3 -march=native ...

# 调整采样间隔（修改源码）
sleep(5);  // 增加到更长间隔
```

### 存储空间管理

```bash
# 定期清理旧文件
find media/ -name "*.jpg" -mtime +7 -delete  # 删除7天前的照片
find media/ -name "*.mp4" -mtime +3 -delete  # 删除3天前的视频
```

## 🔄 自动启动

### 创建systemd服务

```bash
# 创建服务文件
sudo nano /etc/systemd/system/greenland.service
```

```ini
[Unit]
Description=GreenLand Smart Agriculture Monitor
After=network.target

[Service]
Type=simple
User=orangepi
WorkingDirectory=/home/<USER>/Workspace/GreenLandV01
ExecStart=/home/<USER>/Workspace/GreenLandV01/bin/greenland
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```bash
# 启用服务
sudo systemctl enable greenland.service
sudo systemctl start greenland.service

# 查看状态
sudo systemctl status greenland.service
```

## 📞 技术支持

- **作者**: 刘旭
- **邮箱**: <EMAIL>
- **项目地址**: https://gitee.com/lvjing_nmg/hardware-fundamentals.git

---

🌱 **愿景**: 让智能农业技术更加普及和易用！
