#!/bin/bash

# GreenLand项目编译测试脚本
# 作者: GreenLand Team
# 日期: 2024

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

echo -e "${CYAN}🚀 GreenLand项目编译测试脚本${NC}"
echo -e "${CYAN}================================${NC}"
echo -e "项目目录: ${PROJECT_ROOT}"
echo -e "执行时间: $(date)"
echo ""

# 函数: 打印步骤
print_step() {
    echo -e "${BLUE}📋 步骤 $1: $2${NC}"
}

# 函数: 打印成功
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数: 打印警告
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数: 打印错误
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数: 检查依赖
check_dependencies() {
    print_step "1" "检查编译依赖"

    # 检查编译器
    if ! command -v gcc &> /dev/null; then
        print_error "gcc 编译器未安装"
        exit 1
    fi
    print_success "gcc 编译器: $(gcc --version | head -n1)"

    if ! command -v g++ &> /dev/null; then
        print_error "g++ 编译器未安装"
        exit 1
    fi
    print_success "g++ 编译器: $(g++ --version | head -n1)"

    # 检查make
    if ! command -v make &> /dev/null; then
        print_error "make 工具未安装"
        exit 1
    fi
    print_success "make 工具: $(make --version | head -n1)"

    # 检查wiringPi
    if ! ldconfig -p | grep -q wiringPi; then
        print_warning "wiringPi 库可能未安装，某些功能可能无法使用"
    else
        print_success "wiringPi 库已安装"
    fi

    echo ""
}

# 函数: 清理项目
clean_project() {
    print_step "2" "清理项目"

    if make clean > /dev/null 2>&1; then
        print_success "项目清理完成"
    else
        print_warning "项目清理失败，继续执行"
    fi
    echo ""
}

# 函数: 编译主程序
build_main() {
    print_step "3" "编译主程序"

    echo "正在编译主程序..."
    if make all > build.log 2>&1; then
        print_success "主程序编译成功: bin/GreenLand"

        # 检查文件大小
        if [ -f "bin/GreenLand" ]; then
            size=$(ls -lh bin/GreenLand | awk '{print $5}')
            print_success "可执行文件大小: $size"
        fi
    else
        print_error "主程序编译失败"
        echo "错误详情:"
        tail -20 build.log
        exit 1
    fi
    echo ""
}

# 函数: 检查模块库
check_modules() {
    print_step "4" "检查模块库"

    modules=("aht20" "bh1750" "hcsr04" "logger")

    for module in "${modules[@]}"; do
        if [ -f "lib/lib${module}.so" ]; then
            size=$(ls -lh lib/lib${module}.so | awk '{print $5}')
            print_success "模块 $module: $size"
        else
            print_error "模块 $module 库文件缺失"
            exit 1
        fi
    done
    echo ""
}

# 函数: 编译测试程序
build_tests() {
    print_step "5" "编译测试程序"

    echo "正在编译测试程序..."
    if make tests >> build.log 2>&1; then
        print_success "测试程序编译成功"

        # 统计测试程序数量
        if [ -d "bin/Tests" ]; then
            test_count=$(find bin/Tests -type f -executable | wc -l)
            print_success "生成测试程序数量: $test_count"
        fi
    else
        print_warning "部分测试程序编译失败，这是正常的"
        echo "  (某些测试程序可能需要特定的硬件或库)"
    fi
    echo ""
}

# 函数: 验证编译结果
verify_build() {
    print_step "6" "验证编译结果"

    # 检查主程序
    if [ -f "bin/GreenLand" ] && [ -x "bin/GreenLand" ]; then
        print_success "主程序可执行文件正常"
    else
        print_error "主程序可执行文件异常"
        exit 1
    fi

    # 检查库文件
    lib_count=0
    if [ -d "lib" ]; then
        for lib in lib/*.so; do
            if [ -f "$lib" ]; then
                ((lib_count++))
            fi
        done
    fi

    if [ $lib_count -gt 0 ]; then
        print_success "生成库文件数量: $lib_count"
    else
        print_error "未生成任何库文件"
        exit 1
    fi

    # 检查依赖关系
    echo "检查主程序依赖关系:"
    if command -v ldd &> /dev/null; then
        if ldd bin/GreenLand 2>/dev/null | grep -q "not found"; then
            print_warning "主程序存在未找到的依赖库"
            ldd bin/GreenLand | grep "not found"
        else
            print_success "主程序依赖关系正常"
        fi
    else
        print_warning "ldd 命令不可用，跳过依赖检查"
    fi
    echo ""
}

# 函数: 生成编译报告
generate_report() {
    print_step "7" "生成编译报告"

    report_file="build_report.txt"

    cat > "$report_file" << EOF
GreenLand项目编译报告
====================
编译时间: $(date)
编译环境: $(uname -a)
编译器版本: $(gcc --version | head -n1)

编译结果:
EOF

    # 主程序信息
    if [ -f "bin/GreenLand" ]; then
        echo "✅ 主程序: bin/GreenLand ($(ls -lh bin/GreenLand | awk '{print $5}'))" >> "$report_file"
    else
        echo "❌ 主程序: 编译失败" >> "$report_file"
    fi

    # 库文件信息
    echo "" >> "$report_file"
    echo "库文件:" >> "$report_file"
    for lib in lib/*.so; do
        if [ -f "$lib" ]; then
            echo "✅ $(basename "$lib") ($(ls -lh "$lib" | awk '{print $5}'))" >> "$report_file"
        fi
    done

    # 测试程序信息
    echo "" >> "$report_file"
    echo "测试程序:" >> "$report_file"
    if [ -d "bin/Tests" ]; then
        find bin/Tests -type f -executable | while read test_file; do
            echo "✅ $(basename "$test_file")" >> "$report_file"
        done
    fi

    print_success "编译报告已生成: $report_file"
    echo ""
}

# 函数: 清理临时文件
cleanup() {
    if [ -f "build.log" ]; then
        rm -f build.log
    fi
}

# 主函数
main() {
    # 设置错误处理
    trap cleanup EXIT

    check_dependencies
    clean_project
    build_main
    check_modules
    build_tests
    verify_build
    generate_report

    echo -e "${GREEN}🎉 编译测试完成！${NC}"
    echo -e "${GREEN}================================${NC}"
    echo -e "✅ 主程序: bin/GreenLand"
    echo -e "✅ 库文件: lib/*.so"
    echo -e "✅ 测试程序: bin/Tests/*"
    echo -e "📊 详细报告: build_report.txt"
    echo ""
    echo -e "${CYAN}💡 使用方法:${NC}"
    echo -e "  运行主程序: sudo ./bin/GreenLand"
    echo -e "  查看帮助: make help"
    echo -e "  运行测试: 查看 bin/Tests/ 目录"
}

# 执行主函数
main "$@"
