#!/bin/bash

# GreenLand项目快速编译脚本
# 作者: GreenLand Team
# 日期: 2024

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

echo -e "${CYAN}🚀 GreenLand项目快速编译${NC}"
echo -e "${CYAN}========================${NC}"
echo -e "项目目录: ${PROJECT_ROOT}"
echo -e "执行时间: $(date)"
echo ""

# 函数: 打印成功
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数: 打印警告
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数: 打印错误
print_error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

# 检查基本依赖
echo -e "${BLUE}📋 检查编译环境${NC}"
command -v gcc >/dev/null 2>&1 || print_error "gcc 编译器未安装"
command -v g++ >/dev/null 2>&1 || print_error "g++ 编译器未安装"
command -v make >/dev/null 2>&1 || print_error "make 工具未安装"
print_success "编译环境检查通过"
echo ""

# 清理项目
echo -e "${BLUE}🧹 清理项目${NC}"
make clean >/dev/null 2>&1
print_success "项目清理完成"
echo ""

# 编译项目
echo -e "${BLUE}🔨 编译项目${NC}"
echo "正在编译..."
if make all >/dev/null 2>&1; then
    print_success "项目编译成功"
else
    print_error "项目编译失败"
fi
echo ""

# 验证结果
echo -e "${BLUE}📊 编译结果${NC}"

# 检查主程序
if [ -f "bin/GreenLand" ]; then
    size=$(ls -lh bin/GreenLand | awk '{print $5}')
    print_success "主程序: bin/GreenLand ($size)"
else
    print_error "主程序编译失败"
fi

# 检查库文件
lib_count=0
if [ -d "lib" ]; then
    for lib in lib/*.so; do
        if [ -f "$lib" ]; then
            size=$(ls -lh "$lib" | awk '{print $5}')
            print_success "库文件: $(basename "$lib") ($size)"
            ((lib_count++))
        fi
    done
fi

if [ $lib_count -eq 0 ]; then
    print_error "未生成任何库文件"
fi

# 检查测试程序
test_count=0
if [ -d "bin/Tests" ]; then
    test_count=$(find bin/Tests -type f -executable | wc -l)
    if [ $test_count -gt 0 ]; then
        print_success "测试程序: $test_count 个"
    else
        print_warning "未生成测试程序"
    fi
else
    print_warning "测试程序目录不存在"
fi

echo ""

# 依赖检查
echo -e "${BLUE}🔗 依赖检查${NC}"
if command -v ldd >/dev/null 2>&1; then
    if ldd bin/GreenLand 2>/dev/null | grep -q "not found"; then
        print_warning "主程序存在未找到的依赖库"
        ldd bin/GreenLand | grep "not found"
    else
        print_success "主程序依赖关系正常"
    fi
else
    print_warning "ldd 命令不可用，跳过依赖检查"
fi

echo ""

# 生成简要报告
echo -e "${GREEN}🎉 编译完成！${NC}"
echo -e "${GREEN}===============${NC}"
echo -e "✅ 主程序: bin/GreenLand"
echo -e "✅ 库文件: $lib_count 个"
echo -e "✅ 测试程序: $test_count 个"
echo ""
echo -e "${CYAN}💡 使用方法:${NC}"
echo -e "  运行主程序: sudo ./bin/GreenLand"
echo -e "  查看帮助: make help"
echo -e "  运行测试: 查看 bin/Tests/ 目录"
echo ""
