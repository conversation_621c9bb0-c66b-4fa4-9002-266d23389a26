#!/bin/bash

# 测试Makefile编译脚本
# 作者: Alex
# 邮箱: <EMAIL>

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${GREEN}🔧 测试Makefile编译系统${NC}"
echo -e "${GREEN}========================${NC}"

# 确保在正确的目录
cd /home/<USER>/Workspace/GreenLandV01

echo -e "${BLUE}📁 当前目录: $(pwd)${NC}"
echo -e "${BLUE}📋 项目文件:${NC}"
ls -la

echo ""
echo -e "${BLUE}🧪 测试文件:${NC}"
ls -la tests/

echo ""
echo -e "${BLUE}🔧 开始编译...${NC}"

# 清理
echo -e "${YELLOW}清理构建文件...${NC}"
make clean

echo ""
echo -e "${YELLOW}显示项目信息...${NC}"
make info

echo ""
echo -e "${YELLOW}编译主程序...${NC}"
make 2>&1

echo ""
echo -e "${BLUE}📊 编译结果:${NC}"
if [ -f "bin/GreenLand" ]; then
    echo -e "${GREEN}✅ 主程序编译成功: bin/GreenLand${NC}"
    ls -lh bin/GreenLand
else
    echo -e "${RED}❌ 主程序编译失败${NC}"
fi

echo ""
echo -e "${BLUE}🧪 测试程序:${NC}"
if [ -d "bin/Tests" ]; then
    echo -e "${GREEN}✅ 测试目录存在${NC}"
    ls -la bin/Tests/
    
    for test_file in bin/Tests/*; do
        if [ -f "$test_file" ]; then
            echo -e "${GREEN}✅ 测试程序: $test_file${NC}"
            ls -lh "$test_file"
        fi
    done
else
    echo -e "${RED}❌ 测试目录不存在${NC}"
fi

echo ""
echo -e "${BLUE}📋 编译总结:${NC}"
echo -e "${CYAN}主程序: $([ -f "bin/GreenLand" ] && echo "✅ 成功" || echo "❌ 失败")${NC}"
echo -e "${CYAN}测试程序: $([ -d "bin/Tests" ] && echo "✅ $(ls bin/Tests/ 2>/dev/null | wc -l) 个" || echo "❌ 0 个")${NC}"

echo ""
echo -e "${GREEN}🎉 测试完成！${NC}"
