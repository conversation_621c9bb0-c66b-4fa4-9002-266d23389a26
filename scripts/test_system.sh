#!/bin/bash

# GreenLand 系统测试脚本
# 作者: 刘旭

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 编译测试程序
compile_tests() {
    print_message $BLUE "🔧 编译测试程序..."

    # 创建目录
    mkdir -p bin/Tests

    # 编译快速测试
    gcc -o bin/Tests/quick_test tests/quick_test.c \
        src/modules/logger/src/logger.c \
        src/modules/hcsr04/src/hcsr04.c \
        -Isrc/modules/logger/include \
        -Isrc/modules/hcsr04/include \
        -lwiringPi -lpthread

    # 编译稳定性测试
    gcc -o bin/Tests/system_stability_test tests/system_stability_test.c \
        src/modules/logger/src/logger.c \
        src/modules/hcsr04/src/hcsr04.c \
        -Isrc/modules/logger/include \
        -Isrc/modules/hcsr04/include \
        -lwiringPi -lpthread

    # 编译示例程序
    gcc -o bin/Tests/basic_usage examples/basic_usage.c \
        src/modules/logger/src/logger.c \
        src/modules/hcsr04/src/hcsr04.c \
        -Isrc/modules/logger/include \
        -Isrc/modules/hcsr04/include \
        -lwiringPi -lpthread

    gcc -o bin/Tests/sensor_demo examples/sensor_demo.c \
        src/modules/logger/src/logger.c \
        src/modules/hcsr04/src/hcsr04.c \
        -Isrc/modules/logger/include \
        -Isrc/modules/hcsr04/include \
        -lwiringPi -lpthread

    print_message $GREEN "✅ 编译完成"
}

# 运行快速测试
run_quick_test() {
    print_message $BLUE "⚡ 运行快速测试..."

    if [ -f "bin/Tests/quick_test" ]; then
        ./bin/Tests/quick_test
        if [ $? -eq 0 ]; then
            print_message $GREEN "✅ 快速测试通过"
        else
            print_message $RED "❌ 快速测试失败"
            return 1
        fi
    else
        print_message $RED "❌ 快速测试程序不存在"
        return 1
    fi
}

# 运行稳定性测试
run_stability_test() {
    print_message $BLUE "🔍 运行稳定性测试..."

    if [ -f "bin/Tests/system_stability_test" ]; then
        timeout 60 ./bin/Tests/system_stability_test || true
        print_message $YELLOW "ℹ️ 稳定性测试完成（可能因超时而终止）"
    else
        print_message $RED "❌ 稳定性测试程序不存在"
        return 1
    fi
}

# 主函数
main() {
    print_message $GREEN "🧪 GreenLand 系统测试"
    print_message $GREEN "====================="

    # 创建bin目录
    mkdir -p bin

    # 编译测试程序
    compile_tests

    # 运行测试
    case "${1:-all}" in
        quick)
            run_quick_test
            ;;
        stability)
            run_stability_test
            ;;
        all)
            run_quick_test
            echo ""
            run_stability_test
            ;;
        *)
            echo "用法: $0 [quick|stability|all]"
            exit 1
            ;;
    esac

    print_message $GREEN "🎉 测试完成"
}

main "$@"
