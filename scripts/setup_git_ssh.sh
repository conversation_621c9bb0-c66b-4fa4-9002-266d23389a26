#!/bin/bash

# Git SSH仓库配置脚本
# 用户: aubuty
# 邮箱: <EMAIL>

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${GREEN}🔧 Git SSH仓库配置脚本${NC}"
echo -e "${GREEN}========================${NC}"

# 检查是否在Git仓库中
if [ ! -d ".git" ]; then
    echo -e "${RED}❌ 错误: 当前目录不是Git仓库${NC}"
    exit 1
fi

# 显示当前配置
echo -e "${BLUE}📋 当前Git配置:${NC}"
echo -e "${YELLOW}远程仓库:${NC}"
git remote -v
echo ""

# 删除现有的origin配置
echo -e "${BLUE}🗑️ 删除现有的远程仓库配置...${NC}"
git remote remove origin 2>/dev/null || echo "没有找到origin远程仓库"

# 添加SSH远程仓库配置
echo -e "${BLUE}➕ 添加SSH远程仓库配置...${NC}"
git remote <NAME_EMAIL>:lvjing_nmg/hardware-fundamentals.git

# 配置用户信息
echo -e "${BLUE}👤 配置Git用户信息...${NC}"
git config user.name "aubuty"
git config user.email "<EMAIL>"

# 验证配置
echo -e "${BLUE}✅ 验证新配置:${NC}"
echo -e "${YELLOW}远程仓库:${NC}"
git remote -v
echo -e "${YELLOW}用户配置:${NC}"
echo "用户名: $(git config user.name)"
echo "邮箱: $(git config user.email)"
echo ""

# 测试SSH连接
echo -e "${BLUE}🔗 测试SSH连接到Gitee...${NC}"
if ssh -T ************* 2>&1 | grep -q "successfully authenticated"; then
    echo -e "${GREEN}✅ SSH连接测试成功！${NC}"
else
    echo -e "${YELLOW}⚠️ SSH连接测试 - 请确保已添加公钥到Gitee${NC}"
fi

# 测试Git操作
echo -e "${BLUE}📡 测试Git远程操作...${NC}"
if git ls-remote origin >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Git远程连接测试成功！${NC}"
    
    # 尝试拉取
    echo -e "${BLUE}⬇️ 尝试拉取最新代码...${NC}"
    git fetch origin develop
    
    echo -e "${GREEN}🎉 Git SSH配置完成！${NC}"
    echo -e "${BLUE}现在可以使用以下命令:${NC}"
    echo -e "${YELLOW}git pull origin develop${NC}    # 拉取最新代码"
    echo -e "${YELLOW}git push origin develop${NC}    # 推送代码"
    echo -e "${YELLOW}git pull --tags origin develop${NC}  # 拉取标签"
else
    echo -e "${RED}❌ Git远程连接测试失败${NC}"
    echo -e "${YELLOW}请检查:${NC}"
    echo -e "${YELLOW}1. SSH公钥是否已添加到Gitee账户${NC}"
    echo -e "${YELLOW}2. 网络连接是否正常${NC}"
    echo -e "${YELLOW}3. 仓库权限是否正确${NC}"
fi
