#!/bin/bash

# Tests Makefile 演示脚本
# 作者: Alex
# 邮箱: <EMAIL>

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${GREEN}🧪 GreenLand Tests Makefile 演示${NC}"
echo -e "${GREEN}===============================${NC}"

# 确保在正确的目录
cd /home/<USER>/Workspace/GreenLandV01

echo -e "${BLUE}📁 当前项目目录: $(pwd)${NC}"
echo ""

# 显示tests目录内容
echo -e "${BLUE}📋 Tests目录内容:${NC}"
ls -la tests/
echo ""

# 演示主Makefile的tests目标
echo -e "${CYAN}=== 1. 使用主Makefile编译测试程序 ===${NC}"
echo -e "${YELLOW}命令: make tests${NC}"
make tests
echo ""

# 演示tests目录的Makefile
echo -e "${CYAN}=== 2. 使用tests/Makefile ===${NC}"
echo ""

echo -e "${BLUE}📊 显示项目信息:${NC}"
echo -e "${YELLOW}命令: cd tests && make info${NC}"
cd tests
make info
echo ""

echo -e "${BLUE}📋 列出测试文件:${NC}"
echo -e "${YELLOW}命令: make list${NC}"
make list
echo ""

echo -e "${BLUE}🔧 编译所有测试程序:${NC}"
echo -e "${YELLOW}命令: make${NC}"
make
echo ""

echo -e "${BLUE}📖 显示帮助信息:${NC}"
echo -e "${YELLOW}命令: make help${NC}"
make help
echo ""

# 返回主目录
cd ..

# 检查编译结果
echo -e "${CYAN}=== 3. 检查编译结果 ===${NC}"
echo -e "${BLUE}📁 bin/Tests 目录内容:${NC}"
if [ -d "bin/Tests" ]; then
    ls -la bin/Tests/
    echo ""
    
    echo -e "${BLUE}🧪 可执行的测试程序:${NC}"
    for test in bin/Tests/*; do
        if [ -f "$test" ] && [ -x "$test" ]; then
            echo -e "${GREEN}✅ $test${NC}"
            ls -lh "$test"
        fi
    done
else
    echo -e "${RED}❌ bin/Tests 目录不存在${NC}"
fi
echo ""

# 演示自动检测新文件
echo -e "${CYAN}=== 4. 演示自动检测新文件 ===${NC}"
echo -e "${BLUE}📝 创建新的测试文件...${NC}"

# 创建一个简单的测试文件
cat > tests/demo_test.c << 'EOF'
#include <stdio.h>
#include <stdlib.h>

int main() {
    printf("🧪 这是一个演示测试程序\n");
    printf("✅ 测试通过！\n");
    return 0;
}
EOF

echo -e "${GREEN}✅ 创建了 tests/demo_test.c${NC}"
echo ""

echo -e "${BLUE}📋 重新列出测试文件 (应该包含新文件):${NC}"
cd tests
make list
echo ""

echo -e "${BLUE}🔧 编译新的测试文件:${NC}"
make
echo ""

cd ..

echo -e "${BLUE}📁 检查新编译的测试程序:${NC}"
if [ -f "bin/Tests/demo_test" ]; then
    echo -e "${GREEN}✅ 新测试程序编译成功: bin/Tests/demo_test${NC}"
    ls -lh bin/Tests/demo_test
    echo ""
    
    echo -e "${BLUE}🚀 运行新测试程序:${NC}"
    ./bin/Tests/demo_test
else
    echo -e "${RED}❌ 新测试程序编译失败${NC}"
fi
echo ""

# 清理演示文件
echo -e "${BLUE}🧹 清理演示文件...${NC}"
rm -f tests/demo_test.c
cd tests
make clean
cd ..

echo ""
echo -e "${GREEN}🎉 Tests Makefile 演示完成！${NC}"
echo ""
echo -e "${CYAN}总结:${NC}"
echo -e "${GREEN}✅ tests/Makefile 可以自动检测新的.c和.cpp文件${NC}"
echo -e "${GREEN}✅ 支持彩色编译进度显示${NC}"
echo -e "${GREEN}✅ 自动配置OpenCV和摄像头支持${NC}"
echo -e "${GREEN}✅ 智能依赖管理${NC}"
echo -e "${GREEN}✅ 与主项目Makefile完美集成${NC}"
echo ""
echo -e "${YELLOW}使用方法:${NC}"
echo -e "${CYAN}1. 在tests目录下添加.c或.cpp测试文件${NC}"
echo -e "${CYAN}2. 运行 'make tests' 或 'cd tests && make'${NC}"
echo -e "${CYAN}3. 测试程序自动生成在 bin/Tests/ 目录${NC}"
