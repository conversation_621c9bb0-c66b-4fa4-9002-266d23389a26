#!/bin/bash

# SSH密钥配置脚本
# 用户: aubuty
# 邮箱: <EMAIL>
# 私钥: 93aae187ac65e2e7ec8020a9144d4fa5

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${GREEN}🔐 SSH密钥配置脚本${NC}"
echo -e "${GREEN}==================${NC}"

# 创建SSH目录
echo -e "${BLUE}📁 创建SSH目录...${NC}"
mkdir -p ~/.ssh
chmod 700 ~/.ssh

echo -e "${BLUE}🔑 生成SSH密钥对...${NC}"
# 生成新的SSH密钥对
ssh-keygen -t rsa -b 4096 -C "<EMAIL>" -f ~/.ssh/id_rsa_gitee -N ""

# 设置权限
chmod 600 ~/.ssh/id_rsa_gitee
chmod 644 ~/.ssh/id_rsa_gitee.pub

echo -e "${BLUE}⚙️ 创建SSH配置文件...${NC}"
# 创建SSH配置文件
cat > ~/.ssh/config << 'EOF'
Host gitee.com
    HostName gitee.com
    User git
    IdentityFile ~/.ssh/id_rsa_gitee
    PreferredAuthentications publickey
    IdentitiesOnly yes
    StrictHostKeyChecking no
EOF

chmod 600 ~/.ssh/config

echo -e "${BLUE}👤 配置Git用户信息...${NC}"
git config --global user.name "aubuty"
git config --global user.email "<EMAIL>"

echo -e "${GREEN}📋 SSH公钥内容:${NC}"
echo -e "${YELLOW}请将以下公钥添加到您的Gitee账户中:${NC}"
echo "----------------------------------------"
cat ~/.ssh/id_rsa_gitee.pub
echo "----------------------------------------"

echo -e "${BLUE}🔗 测试SSH连接...${NC}"
ssh -T *************

echo ""
echo -e "${GREEN}✅ SSH配置完成！${NC}"
echo -e "${YELLOW}下一步操作:${NC}"
echo -e "${YELLOW}1. 复制上面的公钥内容${NC}"
echo -e "${YELLOW}2. 登录Gitee -> 设置 -> SSH公钥 -> 添加公钥${NC}"
echo -e "${YELLOW}3. 运行测试脚本验证连接${NC}"
