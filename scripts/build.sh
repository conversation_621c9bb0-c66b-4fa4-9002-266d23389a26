#!/bin/bash

# GreenLand 简单编译脚本
# 作者: aubuty
# 版本: 2.0.0

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${GREEN}🌱 GreenLand 简单编译脚本${NC}"
echo -e "${GREEN}=========================${NC}"

# 创建bin目录
mkdir -p bin

# 基础编译参数
CFLAGS="-Wall -Wextra -std=c++11 -O2"
INCLUDES="-Isrc/modules/logger/include -Isrc/modules/hcsr04/include -Isrc/modules/aht20/include -Isrc/modules/bh1750/include"
SOURCES="src/main.cpp src/modules/logger/src/logger.c src/modules/hcsr04/src/hcsr04.c src/modules/aht20/src/aht20.c src/modules/bh1750/src/bh1750.c"
LIBS="-lwiringPi -lpthread"

# 检查是否有OpenCV
if pkg-config --exists opencv4 2>/dev/null; then
    echo -e "${GREEN}✅ 检测到OpenCV4，启用摄像头支持${NC}"
    SOURCES="$SOURCES src/modules/camera/src/camera.cpp"
    INCLUDES="$INCLUDES -Isrc/modules/camera/include -I/usr/include/opencv4"
    OPENCV_LIBS="-lopencv_core -lopencv_imgproc -lopencv_imgcodecs -lopencv_videoio -lopencv_highgui"
    LIBS="$LIBS $OPENCV_LIBS"
    CFLAGS="$CFLAGS -DENABLE_CAMERA"
    OUTPUT="bin/GreenLand"
elif pkg-config --exists opencv 2>/dev/null; then
    echo -e "${GREEN}✅ 检测到OpenCV，启用摄像头支持${NC}"
    SOURCES="$SOURCES src/modules/camera/src/camera.cpp"
    INCLUDES="$INCLUDES -Isrc/modules/camera/include -I/usr/include/opencv"
    OPENCV_LIBS="-lopencv_core -lopencv_imgproc -lopencv_imgcodecs -lopencv_videoio -lopencv_highgui"
    LIBS="$LIBS $OPENCV_LIBS"
    CFLAGS="$CFLAGS -DENABLE_CAMERA"
    OUTPUT="bin/GreenLand"
else
    echo -e "${YELLOW}⚠️ 未检测到OpenCV，编译无摄像头版本${NC}"
    OUTPUT="bin/GreenLand_no_camera"
fi

# 编译命令
COMPILE_CMD="g++ $CFLAGS $INCLUDES $SOURCES $LIBS -o $OUTPUT"

echo -e "${BLUE}🔧 开始编译...${NC}"
echo "编译命令: $COMPILE_CMD"

# 执行编译
if $COMPILE_CMD; then
    echo -e "${GREEN}✅ 编译成功！${NC}"
    echo -e "${GREEN}   可执行文件: $OUTPUT${NC}"

    # 显示文件信息
    if [ -f "$OUTPUT" ]; then
        file_size=$(ls -lh "$OUTPUT" | awk '{print $5}')
        echo -e "${BLUE}   文件大小: $file_size${NC}"
    fi

    # 设置执行权限
    chmod +x "$OUTPUT"

    echo -e "${GREEN}🎉 编译完成！${NC}"
    echo -e "${BLUE}运行程序: ./$OUTPUT${NC}"
else
    echo -e "${RED}❌ 编译失败${NC}"
    exit 1
fi
