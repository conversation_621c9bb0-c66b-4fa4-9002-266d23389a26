#!/bin/bash

# GreenLand项目编译脚本
# 用于编译所有模块、库文件和测试程序

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 获取项目根目录
PROJECT_ROOT=$(dirname $(dirname $(realpath $0)))
cd "$PROJECT_ROOT"

echo -e "${BLUE}=== GreenLand项目编译脚本 ===${NC}"
echo "项目根目录: $PROJECT_ROOT"

# 解析命令行参数
CLEAN_BUILD=false
BUILD_TESTS=true
BUILD_MAIN=true
VERBOSE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--clean)
            CLEAN_BUILD=true
            shift
            ;;
        --no-tests)
            BUILD_TESTS=false
            shift
            ;;
        --no-main)
            BUILD_MAIN=false
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  -c, --clean     清理后重新编译"
            echo "  --no-tests      不编译测试程序"
            echo "  --no-main       不编译主程序"
            echo "  -v, --verbose   详细输出"
            echo "  -h, --help      显示此帮助信息"
            exit 0
            ;;
        *)
            echo -e "${RED}未知选项: $1${NC}"
            exit 1
            ;;
    esac
done

# 清理函数
clean_build() {
    echo -e "${YELLOW}正在清理编译文件...${NC}"
    rm -rf build bin lib
    echo -e "${GREEN}✅ 清理完成${NC}"
}

# 创建目录函数
create_directories() {
    echo -e "${YELLOW}创建编译目录...${NC}"
    mkdir -p build/{aht20,bh1750,main,tests}
    mkdir -p bin/Tests
    mkdir -p lib
    echo -e "${GREEN}✅ 目录创建完成${NC}"
}

# 编译模块函数
compile_module() {
    local module_name=$1
    echo -e "${YELLOW}编译模块: ${module_name}${NC}"
    
    local src_file="src/modules/${module_name}/src/${module_name}.c"
    local obj_file="build/${module_name}/${module_name}.o"
    local lib_file="lib/lib${module_name}.so"
    local include_dir="src/modules/${module_name}/include"
    
    if [ ! -f "$src_file" ]; then
        echo -e "${RED}❌ 源文件不存在: $src_file${NC}"
        return 1
    fi
    
    # 编译目标文件
    if [ "$VERBOSE" = true ]; then
        echo "gcc -Wall -O2 -fPIC -I./src/modules -I./${include_dir} -c -o ${obj_file} ${src_file}"
    fi
    
    gcc -Wall -O2 -fPIC -I./src/modules -I./${include_dir} -c -o ${obj_file} ${src_file}
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 编译模块 ${module_name} 失败${NC}"
        return 1
    fi
    
    # 创建共享库
    if [ "$VERBOSE" = true ]; then
        echo "gcc -shared -o ${lib_file} ${obj_file}"
    fi
    
    gcc -shared -o ${lib_file} ${obj_file}
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 创建库文件 ${module_name} 失败${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ 模块 ${module_name} 编译完成${NC}"
    return 0
}

# 编译测试程序函数
compile_test() {
    local test_name=$1
    echo -e "${YELLOW}编译测试程序: ${test_name}${NC}"
    
    local src_file="tests/${test_name}.c"
    local obj_file="build/tests/${test_name}.o"
    local exe_file="bin/Tests/${test_name}"
    
    if [ ! -f "$src_file" ]; then
        echo -e "${RED}❌ 测试源文件不存在: $src_file${NC}"
        return 1
    fi
    
    # 确定需要的头文件路径
    local include_flags="-I./src/modules"
    if [[ "$test_name" == *"aht20"* ]]; then
        include_flags="$include_flags -I./src/modules/aht20/include"
    elif [[ "$test_name" == *"bh1750"* ]]; then
        include_flags="$include_flags -I./src/modules/bh1750/include"
    fi
    
    # 编译目标文件
    if [ "$VERBOSE" = true ]; then
        echo "gcc -Wall -O2 -fPIC $include_flags -c -o ${obj_file} ${src_file}"
    fi
    
    gcc -Wall -O2 -fPIC $include_flags -c -o ${obj_file} ${src_file}
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 编译测试 ${test_name} 失败${NC}"
        return 1
    fi
    
    # 确定需要链接的库
    local link_flags="-L./lib -Wl,-rpath,$PWD/lib -lpthread"
    if [[ "$test_name" == *"aht20"* ]]; then
        link_flags="$link_flags -laht20"
    elif [[ "$test_name" == *"bh1750"* ]]; then
        link_flags="$link_flags -lbh1750"
    fi
    
    # 链接可执行文件
    if [ "$VERBOSE" = true ]; then
        echo "gcc -o ${exe_file} ${obj_file} ${link_flags}"
    fi
    
    gcc -o ${exe_file} ${obj_file} ${link_flags}
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 链接测试 ${test_name} 失败${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ 测试程序 ${test_name} 编译完成${NC}"
    return 0
}

# 主编译流程
main() {
    echo -e "${BLUE}开始编译流程...${NC}"
    
    # 清理（如果需要）
    if [ "$CLEAN_BUILD" = true ]; then
        clean_build
    fi
    
    # 创建目录
    create_directories
    
    # 编译模块
    echo -e "${BLUE}=== 编译模块 ===${NC}"
    local modules=("aht20" "bh1750")
    local failed_modules=()
    
    for module in "${modules[@]}"; do
        if ! compile_module "$module"; then
            failed_modules+=("$module")
        fi
    done
    
    if [ ${#failed_modules[@]} -gt 0 ]; then
        echo -e "${RED}❌ 以下模块编译失败: ${failed_modules[*]}${NC}"
        exit 1
    fi
    
    # 编译测试程序
    if [ "$BUILD_TESTS" = true ]; then
        echo -e "${BLUE}=== 编译测试程序 ===${NC}"
        local tests=()
        local failed_tests=()
        
        # 自动发现测试文件
        for test_file in tests/*.c; do
            if [ -f "$test_file" ]; then
                test_name=$(basename "$test_file" .c)
                tests+=("$test_name")
            fi
        done
        
        for test in "${tests[@]}"; do
            if ! compile_test "$test"; then
                failed_tests+=("$test")
            fi
        done
        
        if [ ${#failed_tests[@]} -gt 0 ]; then
            echo -e "${YELLOW}⚠️  以下测试程序编译失败: ${failed_tests[*]}${NC}"
        fi
    fi
    
    # 显示编译结果
    echo -e "${BLUE}=== 编译完成 ===${NC}"
    echo -e "${GREEN}生成的库文件:${NC}"
    ls -la lib/*.so 2>/dev/null || echo "无库文件"
    
    if [ "$BUILD_TESTS" = true ]; then
        echo -e "${GREEN}生成的测试程序:${NC}"
        ls -la bin/Tests/* 2>/dev/null || echo "无测试程序"
    fi
    
    echo -e "${GREEN}🎉 编译流程完成!${NC}"
}

# 运行主函数
main "$@"
