#!/bin/bash

# GreenLand项目测试报告生成脚本

# 获取脚本所在目录的父目录（项目根目录）
PROJECT_ROOT=$(dirname $(dirname $(realpath $0)))

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 报告目录
REPORT_DIR="$PROJECT_ROOT/test_reports"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
REPORT_FILE="$REPORT_DIR/test_report_$TIMESTAMP.md"
HTML_REPORT="$REPORT_DIR/test_report_$TIMESTAMP.html"

echo -e "${BLUE}=== GreenLand 测试报告生成器 ===${NC}"
echo "项目路径: $PROJECT_ROOT"
echo "报告目录: $REPORT_DIR"

# 创建报告目录
mkdir -p "$REPORT_DIR"

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 检查是否需要sudo权限
check_sudo() {
    if [ "$EUID" -ne 0 ]; then
        echo -e "${YELLOW}传感器测试需要root权限来访问I2C设备${NC}"
        echo "正在使用sudo重新运行..."
        sudo "$0" "$@"
        exit $?
    fi
}

# 生成Markdown报告头部
generate_report_header() {
    cat > "$REPORT_FILE" << EOF
# GreenLand 环境监控系统测试报告

**生成时间**: $(date '+%Y-%m-%d %H:%M:%S')  
**项目版本**: 1.0.0  
**测试环境**: $(uname -a)  
**硬件平台**: Orange Pi Zero 2W  

---

## 📋 测试概览

本报告包含GreenLand环境监控系统的全面测试结果，涵盖：

- 🧪 单元测试 (Unit Tests)
- 🔗 集成测试 (Integration Tests)  
- ⚡ 性能测试 (Performance Tests)
- 🛡️ 稳定性测试 (Stability Tests)

---

EOF
}

# 运行单个测试并记录结果
run_test_with_report() {
    local test_name=$1
    local test_executable="$PROJECT_ROOT/bin/Tests/$test_name"
    local test_log="$REPORT_DIR/${test_name}_$TIMESTAMP.log"

    echo -e "${YELLOW}正在运行测试: $test_name${NC}"

    # 检查测试程序是否存在
    if [ ! -f "$test_executable" ]; then
        echo -e "${RED}❌ 测试程序不存在: $test_executable${NC}"
        cat >> "$REPORT_FILE" << EOF
### ❌ $test_name

**状态**: 失败  
**原因**: 测试程序不存在  
**文件**: $test_executable  

EOF
        return 1
    fi

    # 运行测试并记录输出
    echo "## 测试: $test_name" >> "$test_log"
    echo "执行时间: $(date)" >> "$test_log"
    echo "---" >> "$test_log"

    if LD_LIBRARY_PATH="${PROJECT_ROOT}/lib:$LD_LIBRARY_PATH" timeout 300 "$test_executable" >> "$test_log" 2>&1; then
        echo -e "${GREEN}✅ ${test_name} 测试通过${NC}"
        
        # 提取测试统计信息
        local total_tests=$(grep -o "总测试数: [0-9]*" "$test_log" | grep -o "[0-9]*" || echo "0")
        local passed_tests=$(grep -o "通过: [0-9]*" "$test_log" | grep -o "[0-9]*" || echo "0")
        local failed_tests=$(grep -o "失败: [0-9]*" "$test_log" | grep -o "[0-9]*" || echo "0")
        local total_time=$(grep -o "总耗时: [0-9.]*" "$test_log" | grep -o "[0-9.]*" || echo "0.000")

        cat >> "$REPORT_FILE" << EOF
### ✅ $test_name

**状态**: 通过  
**总测试数**: $total_tests  
**通过**: $passed_tests  
**失败**: $failed_tests  
**耗时**: ${total_time}s  

EOF
        return 0
    else
        echo -e "${RED}❌ ${test_name} 测试失败${NC}"
        
        # 提取错误信息
        local error_summary=$(tail -10 "$test_log" | grep -E "(❌|错误|失败)" | head -3)
        
        cat >> "$REPORT_FILE" << EOF
### ❌ $test_name

**状态**: 失败  
**错误摘要**:
\`\`\`
$error_summary
\`\`\`

详细日志: [\`${test_name}_$TIMESTAMP.log\`](${test_name}_$TIMESTAMP.log)

EOF
        return 1
    fi
}

# 生成系统信息
generate_system_info() {
    cat >> "$REPORT_FILE" << EOF
## 🖥️ 系统信息

| 项目 | 值 |
|------|-----|
| 操作系统 | $(uname -s) $(uname -r) |
| 架构 | $(uname -m) |
| CPU信息 | $(grep "model name" /proc/cpuinfo | head -1 | cut -d: -f2 | xargs) |
| 内存总量 | $(free -h | grep "Mem:" | awk '{print $2}') |
| 磁盘空间 | $(df -h . | tail -1 | awk '{print $4}') 可用 |
| 编译器版本 | $(gcc --version | head -1) |

EOF
}

# 检查I2C设备
check_i2c_devices() {
    cat >> "$REPORT_FILE" << EOF
## 🔌 硬件连接检查

### I2C设备扫描

\`\`\`
$(i2cdetect -y 2 2>/dev/null || echo "I2C设备扫描失败")
\`\`\`

### 传感器状态

EOF

    # 检查AHT20
    if i2cdetect -y 2 2>/dev/null | grep -q "38"; then
        echo "- ✅ AHT20 (0x38): 已连接" >> "$REPORT_FILE"
    else
        echo "- ❌ AHT20 (0x38): 未检测到" >> "$REPORT_FILE"
    fi

    # 检查BH1750
    if i2cdetect -y 2 2>/dev/null | grep -q "23"; then
        echo "- ✅ BH1750 (0x23): 已连接" >> "$REPORT_FILE"
    else
        echo "- ❌ BH1750 (0x23): 未检测到" >> "$REPORT_FILE"
    fi

    echo "" >> "$REPORT_FILE"
}

# 生成测试总结
generate_test_summary() {
    local total_test_suites=$1
    local passed_test_suites=$2
    local failed_test_suites=$3

    cat >> "$REPORT_FILE" << EOF
## 📊 测试总结

| 指标 | 值 |
|------|-----|
| 总测试套件 | $total_test_suites |
| 通过套件 | $passed_test_suites |
| 失败套件 | $failed_test_suites |
| 成功率 | $(echo "scale=1; $passed_test_suites * 100 / $total_test_suites" | bc -l)% |
| 报告生成时间 | $(date '+%Y-%m-%d %H:%M:%S') |

EOF

    if [ $failed_test_suites -eq 0 ]; then
        cat >> "$REPORT_FILE" << EOF
### 🎉 测试结果

**所有测试通过！** 系统运行正常，可以投入使用。

EOF
    else
        cat >> "$REPORT_FILE" << EOF
### ⚠️ 测试结果

**有 $failed_test_suites 个测试套件失败**，请检查相关问题后重新测试。

EOF
    fi
}

# 转换为HTML格式
convert_to_html() {
    if command -v pandoc >/dev/null 2>&1; then
        echo -e "${BLUE}正在生成HTML报告...${NC}"
        pandoc "$REPORT_FILE" -o "$HTML_REPORT" --standalone --css=<(echo "
            body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }
            h1, h2, h3 { color: #2c3e50; }
            table { border-collapse: collapse; width: 100%; margin: 10px 0; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
            code { background-color: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
            pre { background-color: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto; }
            .success { color: #27ae60; }
            .error { color: #e74c3c; }
        ")
        echo -e "${GREEN}HTML报告已生成: $HTML_REPORT${NC}"
    else
        echo -e "${YELLOW}pandoc未安装，跳过HTML报告生成${NC}"
    fi
}

# 主函数
main() {
    # 检查sudo权限
    check_sudo

    echo -e "${BLUE}开始生成测试报告...${NC}"

    # 生成报告头部
    generate_report_header

    # 添加系统信息
    generate_system_info

    # 检查硬件连接
    check_i2c_devices

    # 确保项目已编译
    if [ ! -d "bin/Tests" ]; then
        echo -e "${YELLOW}正在编译项目...${NC}"
        make clean && make all
    fi

    echo "## 🧪 测试执行结果" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"

    # 运行所有测试
    total_test_suites=0
    passed_test_suites=0
    failed_test_suites=0

    # 发现所有测试程序
    test_programs=()
    for test_file in bin/Tests/*; do
        if [ -f "$test_file" ] && [ -x "$test_file" ]; then
            test_name=$(basename "$test_file")
            test_programs+=("$test_name")
        fi
    done

    echo -e "${BLUE}发现 ${#test_programs[@]} 个测试程序${NC}"

    # 运行每个测试
    for test_name in "${test_programs[@]}"; do
        total_test_suites=$((total_test_suites + 1))
        
        if run_test_with_report "$test_name"; then
            passed_test_suites=$((passed_test_suites + 1))
        else
            failed_test_suites=$((failed_test_suites + 1))
        fi
        
        echo ""
    done

    # 生成测试总结
    generate_test_summary $total_test_suites $passed_test_suites $failed_test_suites

    # 转换为HTML
    convert_to_html

    echo -e "${GREEN}测试报告生成完成！${NC}"
    echo "Markdown报告: $REPORT_FILE"
    if [ -f "$HTML_REPORT" ]; then
        echo "HTML报告: $HTML_REPORT"
    fi

    # 返回适当的退出码
    if [ $failed_test_suites -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# 运行主函数
main "$@"
