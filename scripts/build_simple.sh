#!/bin/bash

# GreenLand 简单编译脚本
# 作者: 刘旭

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查依赖
check_dependencies() {
    print_message $BLUE "🔍 检查编译依赖..."
    
    if ! command -v gcc &> /dev/null; then
        print_message $RED "❌ gcc 未安装"
        exit 1
    fi
    
    if ! command -v g++ &> /dev/null; then
        print_message $RED "❌ g++ 未安装"
        exit 1
    fi
    
    # 检查wiringPi
    if ! ldconfig -p | grep -q wiringPi; then
        print_message $YELLOW "⚠️ wiringPi 库未找到，传感器功能可能不可用"
    fi
    
    # 检查OpenCV (可选)
    if ! pkg-config --exists opencv4 2>/dev/null && ! pkg-config --exists opencv 2>/dev/null; then
        print_message $YELLOW "⚠️ OpenCV 库未找到，摄像头功能将被禁用"
        ENABLE_CAMERA=0
    else
        print_message $GREEN "✅ 找到 OpenCV 库"
        ENABLE_CAMERA=1
    fi
    
    print_message $GREEN "✅ 依赖检查完成"
}

# 编译程序
compile_program() {
    print_message $BLUE "🔧 编译 GreenLand 程序..."
    
    # 创建bin目录
    mkdir -p bin
    
    # 基础源文件
    SOURCES="src/simple_main.c src/modules/logger/src/logger.c src/modules/hcsr04/src/hcsr04.c"
    
    # 包含路径
    INCLUDES="-Isrc/modules/logger/include -Isrc/modules/hcsr04/include"
    
    # 链接库
    LIBS="-lwiringPi -lpthread"
    
    # 编译选项
    CFLAGS="-Wall -Wextra -std=c99 -O2"
    
    # 如果有OpenCV，添加摄像头支持
    if [ "$ENABLE_CAMERA" = "1" ]; then
        print_message $BLUE "📷 启用摄像头支持..."
        SOURCES="$SOURCES src/modules/camera/src/camera.cpp"
        INCLUDES="$INCLUDES -Isrc/modules/camera/include"
        CFLAGS="$CFLAGS -DENABLE_CAMERA"
        
        # 获取OpenCV编译选项
        if pkg-config --exists opencv4; then
            OPENCV_CFLAGS=$(pkg-config --cflags opencv4)
            OPENCV_LIBS=$(pkg-config --libs opencv4)
        else
            OPENCV_CFLAGS=$(pkg-config --cflags opencv)
            OPENCV_LIBS=$(pkg-config --libs opencv)
        fi
        
        INCLUDES="$INCLUDES $OPENCV_CFLAGS"
        LIBS="$LIBS $OPENCV_LIBS"
        
        # 使用g++编译（因为有C++代码）
        COMPILER="g++"
        CFLAGS="$CFLAGS -std=c++11"
    else
        COMPILER="gcc"
    fi
    
    # 执行编译
    print_message $BLUE "编译命令: $COMPILER $CFLAGS $INCLUDES $SOURCES $LIBS -o bin/greenland"
    
    $COMPILER $CFLAGS $INCLUDES $SOURCES $LIBS -o bin/greenland
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✅ 编译成功"
        print_message $GREEN "📁 可执行文件: bin/greenland"
    else
        print_message $RED "❌ 编译失败"
        exit 1
    fi
}

# 运行程序
run_program() {
    local mode="$1"
    
    if [ ! -f "bin/greenland" ]; then
        print_message $RED "❌ 程序未编译，请先运行编译"
        exit 1
    fi
    
    print_message $BLUE "🚀 运行 GreenLand 程序..."
    
    case "$mode" in
        test)
            print_message $BLUE "🧪 测试模式"
            ./bin/greenland -t
            ;;
        help)
            ./bin/greenland -h
            ;;
        *)
            print_message $BLUE "🔄 监控模式"
            ./bin/greenland
            ;;
    esac
}

# 清理编译文件
clean_build() {
    print_message $BLUE "🧹 清理编译文件..."
    rm -rf bin/greenland
    print_message $GREEN "✅ 清理完成"
}

# 显示帮助
show_help() {
    echo "GreenLand 简单编译脚本"
    echo ""
    echo "用法:"
    echo "  $0 [命令]"
    echo ""
    echo "命令:"
    echo "  build     编译程序 (默认)"
    echo "  run       编译并运行程序"
    echo "  test      编译并运行测试模式"
    echo "  clean     清理编译文件"
    echo "  help      显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 build    # 只编译"
    echo "  $0 run      # 编译并运行"
    echo "  $0 test     # 编译并运行测试"
}

# 主函数
main() {
    local command="${1:-build}"
    
    print_message $GREEN "🌱 GreenLand 简单编译脚本"
    print_message $GREEN "=========================="
    
    case "$command" in
        build)
            check_dependencies
            compile_program
            ;;
        run)
            check_dependencies
            compile_program
            run_program
            ;;
        test)
            check_dependencies
            compile_program
            run_program test
            ;;
        clean)
            clean_build
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_message $RED "❌ 未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
