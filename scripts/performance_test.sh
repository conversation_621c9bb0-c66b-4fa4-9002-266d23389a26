#!/bin/bash

# GreenLand 性能测试脚本
# 作者: 刘旭

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 获取系统信息
get_system_info() {
    print_message $BLUE "📊 系统信息"
    print_message $BLUE "============"
    
    echo "CPU信息:"
    cat /proc/cpuinfo | grep "model name" | head -1 | cut -d: -f2 | xargs
    echo "CPU核心数: $(nproc)"
    echo "CPU频率: $(cat /proc/cpuinfo | grep "cpu MHz" | head -1 | cut -d: -f2 | xargs) MHz"
    
    echo ""
    echo "内存信息:"
    free -h | grep "Mem:" | awk '{print "总内存: " $2 ", 可用: " $7}'
    
    echo ""
    echo "存储信息:"
    df -h . | tail -1 | awk '{print "磁盘空间: " $2 " 总计, " $4 " 可用 (" $5 " 已用)"}'
    
    echo ""
    echo "系统负载:"
    uptime
    
    echo ""
    echo "温度信息:"
    if [ -f /sys/class/thermal/thermal_zone0/temp ]; then
        temp=$(cat /sys/class/thermal/thermal_zone0/temp)
        temp_c=$((temp / 1000))
        echo "CPU温度: ${temp_c}°C"
    else
        echo "CPU温度: 无法获取"
    fi
    
    echo ""
}

# 编译性能测试
compile_performance_test() {
    print_message $BLUE "🔧 编译性能测试程序..."
    
    # 创建性能测试源文件
    cat > tests/performance_test.c << 'EOF'
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <time.h>
#include <sys/time.h>
#include <string.h>
#include "../src/modules/logger/include/logger.h"
#include "../src/modules/hcsr04/include/hcsr04.h"

static double get_time_ms() {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return tv.tv_sec * 1000.0 + tv.tv_usec / 1000.0;
}

// 测试日志系统性能
void test_logger_performance() {
    printf("📝 测试日志系统性能...\n");
    
    log_config_t config;
    logger_get_default_config(&config, "perf_test");
    config.enable_console = false;  // 禁用控制台输出以提高性能
    
    logger_t logger = logger_create(&config);
    if (!logger) {
        printf("❌ 日志系统创建失败\n");
        return;
    }
    
    const int test_count = 10000;
    double start_time = get_time_ms();
    
    for (int i = 0; i < test_count; i++) {
        logger_info(logger, "性能测试日志 %d", i);
    }
    
    double end_time = get_time_ms();
    double duration = end_time - start_time;
    double logs_per_sec = test_count / (duration / 1000.0);
    
    printf("  测试结果:\n");
    printf("    日志条数: %d\n", test_count);
    printf("    总耗时: %.2f ms\n", duration);
    printf("    平均耗时: %.3f ms/条\n", duration / test_count);
    printf("    吞吐量: %.0f 条/秒\n", logs_per_sec);
    
    logger_destroy(logger);
}

// 测试传感器读取性能
void test_sensor_performance() {
    printf("\n🌊 测试传感器读取性能...\n");
    
    if (hcsr04_init() != HCSR04_OK) {
        printf("⚠️ 传感器初始化失败，跳过性能测试\n");
        return;
    }
    
    const int test_count = 100;
    int success_count = 0;
    double total_time = 0;
    double min_time = 999999;
    double max_time = 0;
    
    printf("  开始 %d 次传感器读取测试...\n", test_count);
    
    for (int i = 0; i < test_count; i++) {
        double start_time = get_time_ms();
        
        float distance;
        if (hcsr04_read_distance(&distance) == HCSR04_OK) {
            success_count++;
        }
        
        double end_time = get_time_ms();
        double duration = end_time - start_time;
        
        total_time += duration;
        if (duration < min_time) min_time = duration;
        if (duration > max_time) max_time = duration;
        
        if ((i + 1) % 20 == 0) {
            printf("    进度: %d/%d (成功率: %.1f%%)\n", 
                   i + 1, test_count, (float)success_count / (i + 1) * 100);
        }
        
        usleep(50000);  // 50ms间隔
    }
    
    printf("  测试结果:\n");
    printf("    测试次数: %d\n", test_count);
    printf("    成功次数: %d (%.1f%%)\n", success_count, (float)success_count / test_count * 100);
    printf("    总耗时: %.2f ms\n", total_time);
    printf("    平均耗时: %.2f ms\n", total_time / test_count);
    printf("    最短耗时: %.2f ms\n", min_time);
    printf("    最长耗时: %.2f ms\n", max_time);
    printf("    读取频率: %.1f Hz\n", 1000.0 / (total_time / test_count));
    
    hcsr04_deinit();
}

// 内存使用测试
void test_memory_usage() {
    printf("\n💾 测试内存使用...\n");
    
    // 读取初始内存
    FILE* status = fopen("/proc/self/status", "r");
    if (!status) {
        printf("❌ 无法读取内存信息\n");
        return;
    }
    
    char line[256];
    int vm_rss_kb = 0;
    int vm_size_kb = 0;
    
    while (fgets(line, sizeof(line), status)) {
        if (strncmp(line, "VmRSS:", 6) == 0) {
            sscanf(line, "VmRSS: %d kB", &vm_rss_kb);
        } else if (strncmp(line, "VmSize:", 7) == 0) {
            sscanf(line, "VmSize: %d kB", &vm_size_kb);
        }
    }
    fclose(status);
    
    printf("  内存使用情况:\n");
    printf("    物理内存 (RSS): %d KB (%.1f MB)\n", vm_rss_kb, vm_rss_kb / 1024.0);
    printf("    虚拟内存 (VSZ): %d KB (%.1f MB)\n", vm_size_kb, vm_size_kb / 1024.0);
    
    // 测试内存分配性能
    const int alloc_count = 1000;
    double start_time = get_time_ms();
    
    void** ptrs = malloc(alloc_count * sizeof(void*));
    for (int i = 0; i < alloc_count; i++) {
        ptrs[i] = malloc(1024);  // 分配1KB
        if (ptrs[i]) {
            memset(ptrs[i], i % 256, 1024);  // 写入数据
        }
    }
    
    for (int i = 0; i < alloc_count; i++) {
        if (ptrs[i]) {
            free(ptrs[i]);
        }
    }
    free(ptrs);
    
    double end_time = get_time_ms();
    double duration = end_time - start_time;
    
    printf("  内存分配性能:\n");
    printf("    分配/释放次数: %d\n", alloc_count);
    printf("    总耗时: %.2f ms\n", duration);
    printf("    平均耗时: %.3f ms/次\n", duration / alloc_count);
}

int main() {
    printf("🚀 GreenLand 性能测试\n");
    printf("====================\n\n");
    
    test_logger_performance();
    test_sensor_performance();
    test_memory_usage();
    
    printf("\n🎉 性能测试完成\n");
    return 0;
}
EOF

    # 编译性能测试
    gcc -o bin/performance_test tests/performance_test.c \
        src/modules/logger/src/logger.c \
        src/modules/hcsr04/src/hcsr04.c \
        -Isrc/modules/logger/include \
        -Isrc/modules/hcsr04/include \
        -lwiringPi -lpthread -O2
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✅ 性能测试程序编译成功"
    else
        print_message $RED "❌ 性能测试程序编译失败"
        exit 1
    fi
}

# 运行性能测试
run_performance_test() {
    print_message $BLUE "🏃 运行性能测试..."
    
    if [ ! -f "bin/performance_test" ]; then
        print_message $RED "❌ 性能测试程序不存在"
        exit 1
    fi
    
    ./bin/performance_test
}

# 系统压力测试
run_stress_test() {
    print_message $BLUE "💪 运行系统压力测试..."
    
    # 检查是否有stress工具
    if ! command -v stress &> /dev/null; then
        print_message $YELLOW "⚠️ stress工具未安装，跳过压力测试"
        print_message $YELLOW "   安装命令: sudo apt-get install stress"
        return
    fi
    
    print_message $BLUE "开始30秒CPU压力测试..."
    stress --cpu $(nproc) --timeout 30s &
    STRESS_PID=$!
    
    # 监控系统状态
    for i in {1..6}; do
        sleep 5
        temp=$(cat /sys/class/thermal/thermal_zone0/temp 2>/dev/null || echo "0")
        temp_c=$((temp / 1000))
        load=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | tr -d ',')
        
        printf "  %2ds: 温度=%d°C, 负载=%.2f\n" $((i*5)) $temp_c $load
    done
    
    wait $STRESS_PID
    print_message $GREEN "✅ 压力测试完成"
}

# 网络性能测试
test_network_performance() {
    print_message $BLUE "🌐 测试网络性能..."
    
    # 测试本地回环
    print_message $BLUE "测试本地回环延迟..."
    ping -c 10 127.0.0.1 | tail -1 | awk -F'/' '{print "平均延迟: " $5 " ms"}'
    
    # 测试网关连接
    gateway=$(ip route | grep default | awk '{print $3}' | head -1)
    if [ ! -z "$gateway" ]; then
        print_message $BLUE "测试网关连接 ($gateway)..."
        ping -c 5 $gateway | tail -1 | awk -F'/' '{print "网关延迟: " $5 " ms"}'
    fi
    
    # 测试DNS解析
    print_message $BLUE "测试DNS解析..."
    time_start=$(date +%s%N)
    nslookup gitee.com > /dev/null 2>&1
    time_end=$(date +%s%N)
    dns_time=$(( (time_end - time_start) / 1000000 ))
    echo "DNS解析时间: ${dns_time} ms"
}

# 生成性能报告
generate_report() {
    local report_file="performance_report_$(date +%Y%m%d_%H%M%S).txt"
    
    print_message $BLUE "📋 生成性能报告: $report_file"
    
    {
        echo "GreenLand 性能测试报告"
        echo "======================"
        echo "测试时间: $(date)"
        echo "测试平台: Orange Pi Zero 2W"
        echo ""
        
        echo "系统信息:"
        echo "--------"
        cat /proc/cpuinfo | grep "model name" | head -1 | cut -d: -f2 | xargs
        echo "CPU核心数: $(nproc)"
        free -h | grep "Mem:" | awk '{print "内存: " $2 " 总计, " $7 " 可用"}'
        df -h . | tail -1 | awk '{print "存储: " $4 " 可用"}'
        
        if [ -f /sys/class/thermal/thermal_zone0/temp ]; then
            temp=$(cat /sys/class/thermal/thermal_zone0/temp)
            temp_c=$((temp / 1000))
            echo "CPU温度: ${temp_c}°C"
        fi
        
        echo ""
        echo "性能测试结果:"
        echo "------------"
        echo "详细结果请查看上方测试输出"
        
    } > "$report_file"
    
    print_message $GREEN "✅ 报告已保存到: $report_file"
}

# 主函数
main() {
    local test_type="${1:-all}"
    
    print_message $GREEN "🌱 GreenLand 性能测试工具"
    print_message $GREEN "=========================="
    
    # 创建必要目录
    mkdir -p bin tests
    
    case "$test_type" in
        system)
            get_system_info
            ;;
        compile)
            compile_performance_test
            ;;
        performance)
            compile_performance_test
            run_performance_test
            ;;
        stress)
            run_stress_test
            ;;
        network)
            test_network_performance
            ;;
        all)
            get_system_info
            compile_performance_test
            run_performance_test
            test_network_performance
            generate_report
            ;;
        *)
            echo "用法: $0 [system|compile|performance|stress|network|all]"
            echo ""
            echo "测试类型:"
            echo "  system      - 显示系统信息"
            echo "  compile     - 编译性能测试程序"
            echo "  performance - 运行性能测试"
            echo "  stress      - 运行压力测试"
            echo "  network     - 测试网络性能"
            echo "  all         - 运行所有测试 (默认)"
            exit 1
            ;;
    esac
    
    print_message $GREEN "🎉 测试完成"
}

# 运行主函数
main "$@"
