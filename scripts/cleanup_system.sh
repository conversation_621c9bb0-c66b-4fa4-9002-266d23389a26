#!/bin/bash

# 系统清理脚本
# 作者: Alex
# 邮箱: <EMAIL>

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${GREEN}🧹 GreenLand 系统清理脚本${NC}"
echo -e "${GREEN}===========================${NC}"

# 显示清理前的状态
echo -e "${BLUE}📊 清理前状态统计:${NC}"

# 统计日志文件
if [ -d "Log" ]; then
    log_count=$(find Log -name "*.log" 2>/dev/null | wc -l)
    log_size=$(du -sh Log 2>/dev/null | cut -f1)
    echo -e "${YELLOW}日志文件: ${log_count} 个文件, 总大小: ${log_size}${NC}"
else
    echo -e "${YELLOW}日志目录: 不存在${NC}"
fi

# 统计照片文件
if [ -d "media/photos" ]; then
    photo_count=$(find media/photos -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" 2>/dev/null | wc -l)
    photo_size=$(du -sh media/photos 2>/dev/null | cut -f1)
    echo -e "${YELLOW}照片文件: ${photo_count} 个文件, 总大小: ${photo_size}${NC}"
else
    echo -e "${YELLOW}照片目录: 不存在${NC}"
fi

# 统计视频文件
if [ -d "media/videos" ]; then
    video_count=$(find media/videos -name "*.mp4" -o -name "*.avi" -o -name "*.mov" 2>/dev/null | wc -l)
    video_size=$(du -sh media/videos 2>/dev/null | cut -f1)
    echo -e "${YELLOW}视频文件: ${video_count} 个文件, 总大小: ${video_size}${NC}"
else
    echo -e "${YELLOW}视频目录: 不存在${NC}"
fi

# 统计bin目录
if [ -d "bin" ]; then
    bin_count=$(find bin -type f 2>/dev/null | wc -l)
    bin_size=$(du -sh bin 2>/dev/null | cut -f1)
    echo -e "${YELLOW}bin目录: ${bin_count} 个文件, 总大小: ${bin_size}${NC}"
else
    echo -e "${YELLOW}bin目录: 不存在${NC}"
fi

echo ""

# 询问用户确认
echo -e "${CYAN}🗑️ 即将清理以下内容:${NC}"
echo -e "${RED}1. 清理所有日志文件 (Log/*.log)${NC}"
echo -e "${RED}2. 清理所有照片文件 (media/photos/*)${NC}"
echo -e "${RED}3. 清理所有视频文件 (media/videos/*)${NC}"
echo -e "${RED}4. 清理bin目录内容 (保留目录结构)${NC}"
echo ""

read -p "确认清理? (y/N): " confirm
if [[ $confirm != [yY] && $confirm != [yY][eE][sS] ]]; then
    echo -e "${YELLOW}❌ 清理已取消${NC}"
    exit 0
fi

echo ""
echo -e "${BLUE}🧹 开始清理...${NC}"

# 清理日志文件
echo -e "${BLUE}📝 清理日志文件...${NC}"
if [ -d "Log" ]; then
    log_files_removed=0
    for log_file in Log/*.log; do
        if [ -f "$log_file" ]; then
            echo "  删除: $log_file"
            rm -f "$log_file"
            ((log_files_removed++))
        fi
    done
    echo -e "${GREEN}✅ 已删除 ${log_files_removed} 个日志文件${NC}"
else
    echo -e "${YELLOW}⚠️ Log目录不存在${NC}"
fi

# 清理照片文件
echo -e "${BLUE}📸 清理照片文件...${NC}"
if [ -d "media/photos" ]; then
    photo_files_removed=0
    for photo_file in media/photos/*; do
        if [ -f "$photo_file" ]; then
            echo "  删除: $photo_file"
            rm -f "$photo_file"
            ((photo_files_removed++))
        fi
    done
    echo -e "${GREEN}✅ 已删除 ${photo_files_removed} 个照片文件${NC}"
else
    echo -e "${YELLOW}⚠️ media/photos目录不存在${NC}"
fi

# 清理视频文件
echo -e "${BLUE}🎥 清理视频文件...${NC}"
if [ -d "media/videos" ]; then
    video_files_removed=0
    for video_file in media/videos/*; do
        if [ -f "$video_file" ]; then
            echo "  删除: $video_file"
            rm -f "$video_file"
            ((video_files_removed++))
        fi
    done
    echo -e "${GREEN}✅ 已删除 ${video_files_removed} 个视频文件${NC}"
else
    echo -e "${YELLOW}⚠️ media/videos目录不存在${NC}"
fi

# 清理bin目录
echo -e "${BLUE}🗂️ 清理bin目录...${NC}"
if [ -d "bin" ]; then
    bin_files_removed=0
    for bin_file in bin/*; do
        if [ -f "$bin_file" ]; then
            echo "  删除: $bin_file"
            rm -f "$bin_file"
            ((bin_files_removed++))
        elif [ -d "$bin_file" ] && [ "$bin_file" != "bin/." ] && [ "$bin_file" != "bin/.." ]; then
            echo "  删除目录: $bin_file"
            rm -rf "$bin_file"
            ((bin_files_removed++))
        fi
    done
    echo -e "${GREEN}✅ 已删除 ${bin_files_removed} 个文件/目录${NC}"
else
    echo -e "${YELLOW}⚠️ bin目录不存在${NC}"
fi

echo ""
echo -e "${GREEN}🎉 清理完成！${NC}"

# 显示清理后的状态
echo -e "${BLUE}📊 清理后状态:${NC}"
echo -e "${GREEN}✅ 日志目录: $(ls -la Log 2>/dev/null | wc -l) 个文件${NC}"
echo -e "${GREEN}✅ 照片目录: $(ls -la media/photos 2>/dev/null | wc -l) 个文件${NC}"
echo -e "${GREEN}✅ 视频目录: $(ls -la media/videos 2>/dev/null | wc -l) 个文件${NC}"
echo -e "${GREEN}✅ bin目录: $(ls -la bin 2>/dev/null | wc -l) 个文件${NC}"

echo ""
echo -e "${CYAN}💡 提示:${NC}"
echo -e "${YELLOW}- 重新编译程序: make${NC}"
echo -e "${YELLOW}- 运行程序会重新生成日志和媒体文件${NC}"
echo -e "${YELLOW}- 目录结构已保留，程序可正常运行${NC}"
