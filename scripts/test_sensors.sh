#!/bin/bash

# 传感器测试脚本 - 专门用于测试AHT20和BH1750传感器

# 获取脚本所在目录的父目录（项目根目录）
PROJECT_ROOT=$(dirname $(dirname $(realpath $0)))

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 传感器测试脚本 ===${NC}"
echo "项目路径: $PROJECT_ROOT"

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 检查是否需要sudo权限
if [ "$EUID" -ne 0 ]; then
    echo -e "${YELLOW}传感器测试需要root权限来访问I2C设备${NC}"
    echo "正在使用sudo重新运行..."
    sudo "$0" "$@"
    exit $?
fi

# 解析命令行参数
SENSOR=""
TEST_TYPE="simple"

while [[ $# -gt 0 ]]; do
    case $1 in
        -s|--sensor)
            SENSOR="$2"
            shift 2
            ;;
        -t|--type)
            TEST_TYPE="$2"
            shift 2
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  -s, --sensor <传感器>  指定传感器 (aht20, bh1750, all)"
            echo "  -t, --type <类型>      测试类型 (simple, full)"
            echo "  -h, --help            显示此帮助信息"
            echo ""
            echo "示例:"
            echo "  $0 -s aht20 -t simple    # 运行AHT20简单测试"
            echo "  $0 -s bh1750 -t full     # 运行BH1750完整测试"
            echo "  $0 -s all                # 运行所有传感器简单测试"
            exit 0
            ;;
        *)
            echo -e "${RED}未知选项: $1${NC}"
            exit 1
            ;;
    esac
done

# 检查I2C设备
echo -e "${BLUE}检查I2C设备...${NC}"
if ! command -v i2cdetect &> /dev/null; then
    echo -e "${RED}❌ i2cdetect命令未找到，请安装i2c-tools${NC}"
    exit 1
fi

echo "I2C-2总线设备扫描:"
i2cdetect -y 2

# 检查传感器是否存在
check_sensor() {
    local sensor_name=$1
    local i2c_addr=$2
    
    if i2cdetect -y 2 | grep -q "$i2c_addr"; then
        echo -e "${GREEN}✅ $sensor_name 传感器已检测到 (地址: 0x$i2c_addr)${NC}"
        return 0
    else
        echo -e "${RED}❌ $sensor_name 传感器未检测到 (地址: 0x$i2c_addr)${NC}"
        return 1
    fi
}

# 运行传感器测试
run_sensor_test() {
    local sensor=$1
    local type=$2
    
    local test_program=""
    if [ "$type" = "simple" ]; then
        test_program="bin/Tests/${sensor}_simple_test"
    else
        test_program="bin/Tests/${sensor}_test"
    fi
    
    if [ ! -f "$test_program" ]; then
        echo -e "${RED}❌ 测试程序不存在: $test_program${NC}"
        echo "请先运行 './scripts/build.sh' 编译项目"
        return 1
    fi
    
    echo -e "${YELLOW}运行 $sensor $type 测试...${NC}"
    echo "----------------------------------------"
    
    # 设置库路径并运行测试
    if LD_LIBRARY_PATH="$PROJECT_ROOT/lib:$LD_LIBRARY_PATH" "$test_program"; then
        echo -e "${GREEN}✅ $sensor $type 测试通过${NC}"
        return 0
    else
        echo -e "${RED}❌ $sensor $type 测试失败${NC}"
        return 1
    fi
}

# 主测试逻辑
echo -e "${BLUE}开始传感器测试...${NC}"

# 检查传感器连接
echo -e "${BLUE}=== 传感器连接检查 ===${NC}"
aht20_connected=false
bh1750_connected=false

if check_sensor "AHT20" "38"; then
    aht20_connected=true
fi

if check_sensor "BH1750" "23"; then
    bh1750_connected=true
fi

echo ""

# 根据参数运行测试
case "$SENSOR" in
    "aht20")
        if [ "$aht20_connected" = true ]; then
            run_sensor_test "aht20" "$TEST_TYPE"
        else
            echo -e "${RED}❌ AHT20传感器未连接，跳过测试${NC}"
            exit 1
        fi
        ;;
    "bh1750")
        if [ "$bh1750_connected" = true ]; then
            run_sensor_test "bh1750" "$TEST_TYPE"
        else
            echo -e "${RED}❌ BH1750传感器未连接，跳过测试${NC}"
            exit 1
        fi
        ;;
    "all"|"")
        # 运行所有可用传感器的测试
        test_results=()
        
        if [ "$aht20_connected" = true ]; then
            echo -e "${BLUE}=== AHT20 测试 ===${NC}"
            if run_sensor_test "aht20" "$TEST_TYPE"; then
                test_results+=("AHT20: 通过")
            else
                test_results+=("AHT20: 失败")
            fi
            echo ""
        fi
        
        if [ "$bh1750_connected" = true ]; then
            echo -e "${BLUE}=== BH1750 测试 ===${NC}"
            if run_sensor_test "bh1750" "$TEST_TYPE"; then
                test_results+=("BH1750: 通过")
            else
                test_results+=("BH1750: 失败")
            fi
            echo ""
        fi
        
        # 输出总结
        echo -e "${BLUE}=== 测试总结 ===${NC}"
        for result in "${test_results[@]}"; do
            if [[ "$result" == *"通过"* ]]; then
                echo -e "${GREEN}✅ $result${NC}"
            else
                echo -e "${RED}❌ $result${NC}"
            fi
        done
        
        # 检查是否有失败的测试
        if printf '%s\n' "${test_results[@]}" | grep -q "失败"; then
            exit 1
        fi
        ;;
    *)
        echo -e "${RED}❌ 未知传感器: $SENSOR${NC}"
        echo "支持的传感器: aht20, bh1750, all"
        exit 1
        ;;
esac

echo -e "${GREEN}🎉 传感器测试完成!${NC}"
