#!/bin/bash

# SSH连接测试脚本
# 用户: aubuty
# 邮箱: <EMAIL>

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${GREEN}🔍 SSH连接测试脚本${NC}"
echo -e "${GREEN}==================${NC}"

# 检查SSH目录和文件
echo -e "${BLUE}📁 检查SSH配置文件...${NC}"
if [ -f ~/.ssh/id_rsa_gitee ]; then
    echo -e "${GREEN}✅ 私钥文件存在: ~/.ssh/id_rsa_gitee${NC}"
    ls -la ~/.ssh/id_rsa_gitee
else
    echo -e "${RED}❌ 私钥文件不存在: ~/.ssh/id_rsa_gitee${NC}"
fi

if [ -f ~/.ssh/id_rsa_gitee.pub ]; then
    echo -e "${GREEN}✅ 公钥文件存在: ~/.ssh/id_rsa_gitee.pub${NC}"
    ls -la ~/.ssh/id_rsa_gitee.pub
else
    echo -e "${RED}❌ 公钥文件不存在: ~/.ssh/id_rsa_gitee.pub${NC}"
fi

if [ -f ~/.ssh/config ]; then
    echo -e "${GREEN}✅ SSH配置文件存在: ~/.ssh/config${NC}"
    echo -e "${YELLOW}配置内容:${NC}"
    cat ~/.ssh/config
else
    echo -e "${RED}❌ SSH配置文件不存在: ~/.ssh/config${NC}"
fi

echo ""

# 显示公钥内容
if [ -f ~/.ssh/id_rsa_gitee.pub ]; then
    echo -e "${BLUE}🔑 SSH公钥内容:${NC}"
    echo -e "${YELLOW}请确保以下公钥已添加到Gitee账户:${NC}"
    echo "----------------------------------------"
    cat ~/.ssh/id_rsa_gitee.pub
    echo "----------------------------------------"
    echo ""
fi

# 测试SSH连接
echo -e "${BLUE}🔗 测试SSH连接到Gitee...${NC}"
ssh_result=$(ssh -T ************* 2>&1)
echo "$ssh_result"

if echo "$ssh_result" | grep -q "successfully authenticated"; then
    echo -e "${GREEN}✅ SSH认证成功！${NC}"
elif echo "$ssh_result" | grep -q "Permission denied"; then
    echo -e "${RED}❌ SSH认证失败 - 权限被拒绝${NC}"
    echo -e "${YELLOW}请检查公钥是否正确添加到Gitee账户${NC}"
elif echo "$ssh_result" | grep -q "Connection refused"; then
    echo -e "${RED}❌ 连接被拒绝 - 网络问题${NC}"
else
    echo -e "${YELLOW}⚠️ SSH连接状态未知${NC}"
fi

echo ""

# 测试Git操作
if [ -d ".git" ]; then
    echo -e "${BLUE}📡 测试Git远程操作...${NC}"
    
    # 显示当前远程配置
    echo -e "${YELLOW}当前远程仓库配置:${NC}"
    git remote -v
    
    # 测试远程连接
    if git ls-remote origin >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Git远程连接成功！${NC}"
    else
        echo -e "${RED}❌ Git远程连接失败${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ 当前目录不是Git仓库，跳过Git测试${NC}"
fi

echo ""
echo -e "${BLUE}📋 操作指南:${NC}"
echo -e "${YELLOW}1. 如果SSH认证失败，请将公钥添加到Gitee:${NC}"
echo -e "${YELLOW}   https://gitee.com/profile/sshkeys${NC}"
echo -e "${YELLOW}2. 如果连接成功，可以使用SSH方式操作Git:${NC}"
echo -e "${YELLOW}   <NAME_EMAIL>:lvjing_nmg/hardware-fundamentals.git${NC}"
echo -e "${YELLOW}   git push origin develop${NC}"
echo -e "${YELLOW}   git pull origin develop${NC}"
