#!/bin/bash

# 编译测试程序脚本
# 作者: Alex
# 邮箱: <EMAIL>

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${GREEN}🧪 编译测试程序${NC}"
echo -e "${GREEN}===============${NC}"

# 确保在正确的目录
cd /home/<USER>/Workspace/GreenLandV01

# 创建测试目录
mkdir -p bin/Tests

# 编译选项
CFLAGS="-Wall -Wextra -Wno-unused-function -std=c99 -O2 -fPIC"
INCLUDES="-Isrc/modules/logger/include -Isrc/modules/hcsr04/include -Isrc/modules/aht20/include -Isrc/modules/bh1750/include"
LIBS="-lwiringPi -lpthread"

# 检查OpenCV
if pkg-config --exists opencv4 2>/dev/null; then
    OPENCV_CFLAGS=$(pkg-config --cflags opencv4)
    OPENCV_LIBS="-lopencv_core -lopencv_imgproc -lopencv_imgcodecs -lopencv_videoio -lopencv_highgui"
    INCLUDES="$INCLUDES -Isrc/modules/camera/include $OPENCV_CFLAGS"
    LIBS="$LIBS $OPENCV_LIBS"
    CAMERA_ENABLED=1
    echo -e "${GREEN}✅ 检测到OpenCV4，启用摄像头支持${NC}"
else
    CAMERA_ENABLED=0
    echo -e "${YELLOW}⚠️ 未检测到OpenCV，禁用摄像头支持${NC}"
fi

# 编译模块对象文件
echo -e "${BLUE}🔧 编译模块对象文件...${NC}"
mkdir -p build/obj

# 编译各个模块
echo "编译日志模块..."
gcc $CFLAGS $INCLUDES -c src/modules/logger/src/logger.c -o build/obj/logger.o

echo "编译水位传感器模块..."
gcc $CFLAGS $INCLUDES -c src/modules/hcsr04/src/hcsr04.c -o build/obj/hcsr04.o

echo "编译温湿度传感器模块..."
gcc $CFLAGS $INCLUDES -c src/modules/aht20/src/aht20.c -o build/obj/aht20.o

echo "编译光照传感器模块..."
gcc $CFLAGS $INCLUDES -c src/modules/bh1750/src/bh1750.c -o build/obj/bh1750.o

if [ $CAMERA_ENABLED -eq 1 ]; then
    echo "编译摄像头模块..."
    g++ -Wall -Wextra -Wno-unused-function -std=c++11 -O2 -fPIC $INCLUDES -c src/modules/camera/src/camera.cpp -o build/obj/camera.o
    OBJECTS="build/obj/logger.o build/obj/hcsr04.o build/obj/aht20.o build/obj/bh1750.o build/obj/camera.o"
else
    OBJECTS="build/obj/logger.o build/obj/hcsr04.o build/obj/aht20.o build/obj/bh1750.o"
fi

echo ""
echo -e "${BLUE}🧪 编译测试程序...${NC}"

# 编译所有测试文件
for test_file in tests/*.c; do
    if [ -f "$test_file" ]; then
        test_name=$(basename "$test_file" .c)
        echo -e "${CYAN}编译测试: $test_name${NC}"
        
        if gcc $CFLAGS $INCLUDES "$test_file" $OBJECTS $LIBS -o "bin/Tests/$test_name"; then
            echo -e "${GREEN}✅ 编译成功: bin/Tests/$test_name${NC}"
        else
            echo -e "${RED}❌ 编译失败: $test_name${NC}"
        fi
    fi
done

echo ""
echo -e "${GREEN}🎉 测试程序编译完成！${NC}"

# 显示结果
echo -e "${BLUE}📊 编译结果:${NC}"
if [ -d "bin/Tests" ]; then
    test_count=$(ls bin/Tests/ 2>/dev/null | wc -l)
    echo -e "${GREEN}✅ 测试程序数量: $test_count${NC}"
    
    if [ $test_count -gt 0 ]; then
        echo -e "${CYAN}可用的测试程序:${NC}"
        for test in bin/Tests/*; do
            if [ -f "$test" ]; then
                echo -e "${YELLOW}  ./$test${NC}"
            fi
        done
    fi
else
    echo -e "${RED}❌ 测试目录不存在${NC}"
fi
