#!/bin/bash

# Git状态总结脚本
# 作者: Alex
# 邮箱: <EMAIL>

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${GREEN}🎉 Git配置成功总结${NC}"
echo -e "${GREEN}==================${NC}"

# 显示Git配置
echo -e "${BLUE}👤 Git用户配置:${NC}"
echo -e "${YELLOW}用户名: $(git config user.name)${NC}"
echo -e "${YELLOW}邮箱: $(git config user.email)${NC}"
echo ""

# 显示远程仓库配置
echo -e "${BLUE}🔗 远程仓库配置:${NC}"
git remote -v
echo ""

# 显示分支状态
echo -e "${BLUE}🌿 分支状态:${NC}"
git branch -a
echo ""

# 显示最近提交
echo -e "${BLUE}📝 最近提交:${NC}"
git log --oneline -5
echo ""

# SSH连接测试
echo -e "${BLUE}🔐 SSH连接测试:${NC}"
ssh_result=$(ssh -T ************* 2>&1)
if echo "$ssh_result" | grep -q "successfully authenticated"; then
    echo -e "${GREEN}✅ SSH认证成功！${NC}"
    echo "$ssh_result"
else
    echo -e "${RED}❌ SSH认证失败${NC}"
    echo "$ssh_result"
fi
echo ""

# Git操作测试
echo -e "${BLUE}📡 Git操作测试:${NC}"
if git ls-remote origin >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Git远程连接成功！${NC}"
else
    echo -e "${RED}❌ Git远程连接失败${NC}"
fi
echo ""

echo -e "${GREEN}🚀 可用的Git命令:${NC}"
echo -e "${CYAN}git pull origin develop${NC}      # 拉取最新代码"
echo -e "${CYAN}git push origin develop${NC}      # 推送代码"
echo -e "${CYAN}git pull --tags origin develop${NC}  # 拉取标签"
echo -e "${CYAN}git status${NC}                   # 查看状态"
echo -e "${CYAN}git log --oneline${NC}            # 查看提交历史"

echo ""
echo -e "${GREEN}✅ Git SSH配置完成！${NC}"
echo -e "${YELLOW}仓库地址: *************:lvjing_nmg/hardware-fundamentals.git${NC}"
echo -e "${YELLOW}用户: Alex (<EMAIL>)${NC}"
