#!/bin/bash

# GreenLand项目测试运行脚本

# 获取脚本所在目录的父目录（项目根目录）
PROJECT_ROOT=$(dirname $(dirname $(realpath $0)))

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试计数器
total_tests=0
passed_tests=0
failed_tests=0

echo -e "${BLUE}=== GreenLand 项目测试运行器 ===${NC}"
echo "项目路径: $PROJECT_ROOT"

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 检查是否需要sudo权限
check_sudo() {
    if [ "$EUID" -ne 0 ]; then
        echo -e "${YELLOW}传感器测试需要root权限来访问I2C设备${NC}"
        echo "正在使用sudo重新运行..."
        sudo "$0" "$@"
        exit $?
    fi
}

# 检查测试程序目录
if [ ! -d "bin/Tests" ]; then
    echo -e "${RED}❌ 测试程序目录不存在${NC}"
    echo "请先运行 './scripts/build.sh' 编译项目"
    exit 1
fi

# 运行单个测试的函数
run_test() {
    local test_name=$1
    local test_executable="$PROJECT_ROOT/bin/Tests/$test_name"

    echo -e "${YELLOW}正在运行测试: $test_name${NC}"
    ((total_tests++))

    # 检查测试程序是否存在
    if [ ! -f "$test_executable" ]; then
        echo -e "${RED}❌ 测试程序不存在: $test_executable${NC}"
        ((failed_tests++))
        return 1
    fi

    # 设置库路径并运行测试
    if LD_LIBRARY_PATH="${PROJECT_ROOT}/lib:$LD_LIBRARY_PATH" "$test_executable"; then
        echo -e "${GREEN}✅ ${test_name} 测试通过${NC}"
        ((passed_tests++))
        return 0
    else
        echo -e "${RED}❌ ${test_name} 测试失败${NC}"
        ((failed_tests++))
        return 1
    fi
}

# 解析命令行参数
SPECIFIC_TEST=""
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--test)
            SPECIFIC_TEST="$2"
            shift 2
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  -t, --test <测试名>  运行指定的测试"
            echo "  -h, --help          显示此帮助信息"
            echo ""
            echo "可用的测试:"
            for test_file in bin/Tests/*; do
                if [ -f "$test_file" ]; then
                    echo "  $(basename "$test_file")"
                fi
            done
            exit 0
            ;;
        *)
            echo -e "${RED}未知选项: $1${NC}"
            exit 1
            ;;
    esac
done

# 检查sudo权限（对于传感器测试）
check_sudo

echo -e "${BLUE}开始运行测试...${NC}"

# 运行测试的主逻辑
if [ -n "$SPECIFIC_TEST" ]; then
    # 运行指定的测试
    echo -e "${BLUE}运行指定测试: $SPECIFIC_TEST${NC}"
    run_test "$SPECIFIC_TEST"
else
    # 运行所有测试
    echo -e "${BLUE}自动发现测试程序...${NC}"

    # 发现所有测试程序
    test_programs=()
    for test_file in bin/Tests/*; do
        if [ -f "$test_file" ] && [ -x "$test_file" ]; then
            test_name=$(basename "$test_file")
            test_programs+=("$test_name")
            echo "  发现测试: $test_name"
        fi
    done

    if [ ${#test_programs[@]} -eq 0 ]; then
        echo -e "${RED}❌ 未发现任何测试程序${NC}"
        exit 1
    fi

    echo ""

    # 运行所有测试
    for test_name in "${test_programs[@]}"; do
        run_test "$test_name"
        echo ""
    done
fi

# 输出测试结果摘要
echo -e "${BLUE}=== 测试结果摘要 ===${NC}"
echo "----------------------------------------"
echo -e "总测试数: ${total_tests}"
echo -e "${GREEN}通过: ${passed_tests}${NC}"
echo -e "${RED}失败: ${failed_tests}${NC}"

if [ $failed_tests -eq 0 ]; then
    echo -e "\n${GREEN}🎉 所有测试通过！${NC}"
    exit 0
else
    echo -e "\n${RED}❌ 有 ${failed_tests} 个测试失败${NC}"
    exit 1
fi
