#!/bin/bash

# Makefile集成测试脚本
# 作者: Alex
# 邮箱: <EMAIL>

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${GREEN}🔧 GreenLand Makefile集成测试${NC}"
echo -e "${GREEN}==============================${NC}"

# 确保在正确的目录
cd /home/<USER>/Workspace/GreenLandV01

echo -e "${BLUE}📁 当前项目目录: $(pwd)${NC}"
echo ""

# 测试1: 显示帮助信息
echo -e "${CYAN}=== 1. 显示帮助信息 ===${NC}"
echo -e "${YELLOW}命令: make help${NC}"
make help
echo ""

# 测试2: 只编译主程序
echo -e "${CYAN}=== 2. 只编译主程序 ===${NC}"
echo -e "${YELLOW}命令: make clean && make${NC}"
make clean
make
echo ""

# 检查主程序编译结果
echo -e "${BLUE}📊 主程序编译结果:${NC}"
if [ -f "bin/GreenLand" ]; then
    echo -e "${GREEN}✅ 主程序编译成功: bin/GreenLand${NC}"
    ls -lh bin/GreenLand
else
    echo -e "${RED}❌ 主程序编译失败${NC}"
fi
echo ""

# 测试3: 只编译测试程序
echo -e "${CYAN}=== 3. 只编译测试程序 ===${NC}"
echo -e "${YELLOW}命令: make tests${NC}"
make tests
echo ""

# 检查测试程序编译结果
echo -e "${BLUE}📊 测试程序编译结果:${NC}"
if [ -d "bin/Tests" ]; then
    test_count=$(ls bin/Tests/ 2>/dev/null | wc -l)
    echo -e "${GREEN}✅ 测试目录存在，包含 $test_count 个文件${NC}"
    if [ $test_count -gt 0 ]; then
        echo -e "${CYAN}测试程序列表:${NC}"
        ls -la bin/Tests/
    fi
else
    echo -e "${RED}❌ 测试目录不存在${NC}"
fi
echo ""

# 测试4: 编译主程序和测试程序
echo -e "${CYAN}=== 4. 编译主程序和测试程序 ===${NC}"
echo -e "${YELLOW}命令: make clean && make with-tests${NC}"
make clean
make with-tests
echo ""

# 检查完整编译结果
echo -e "${BLUE}📊 完整编译结果:${NC}"
echo -e "${CYAN}主程序:${NC}"
if [ -f "bin/GreenLand" ]; then
    echo -e "${GREEN}✅ bin/GreenLand${NC}"
    ls -lh bin/GreenLand
else
    echo -e "${RED}❌ 主程序编译失败${NC}"
fi

echo -e "${CYAN}测试程序:${NC}"
if [ -d "bin/Tests" ]; then
    test_count=$(ls bin/Tests/ 2>/dev/null | wc -l)
    echo -e "${GREEN}✅ bin/Tests/ ($test_count 个文件)${NC}"
    if [ $test_count -gt 0 ]; then
        for test in bin/Tests/*; do
            if [ -f "$test" ]; then
                echo -e "${YELLOW}  $test${NC}"
            fi
        done
    fi
else
    echo -e "${RED}❌ 测试目录不存在${NC}"
fi
echo ""

# 测试5: 测试tests目录的独立Makefile
echo -e "${CYAN}=== 5. 测试tests目录的独立Makefile ===${NC}"
echo -e "${YELLOW}命令: cd tests && make info${NC}"
cd tests
make info
echo ""

echo -e "${YELLOW}命令: make list${NC}"
make list
echo ""

echo -e "${YELLOW}命令: make clean && make${NC}"
make clean
make
echo ""

cd ..

# 测试6: 清理测试
echo -e "${CYAN}=== 6. 清理测试 ===${NC}"
echo -e "${YELLOW}命令: make clean${NC}"
make clean
echo ""

echo -e "${BLUE}📊 清理后状态:${NC}"
echo -e "${CYAN}build目录:${NC} $([ -d "build" ] && echo "存在" || echo "已删除")"
echo -e "${CYAN}bin/GreenLand:${NC} $([ -f "bin/GreenLand" ] && echo "存在" || echo "已删除")"
echo -e "${CYAN}bin/Tests:${NC} $([ -d "bin/Tests" ] && echo "存在" || echo "已删除")"
echo ""

echo -e "${GREEN}🎉 Makefile集成测试完成！${NC}"
echo ""
echo -e "${CYAN}总结:${NC}"
echo -e "${GREEN}✅ make           - 只编译主程序${NC}"
echo -e "${GREEN}✅ make tests     - 只编译测试程序${NC}"
echo -e "${GREEN}✅ make with-tests - 编译主程序和测试程序${NC}"
echo -e "${GREEN}✅ make clean     - 清理所有构建文件${NC}"
echo -e "${GREEN}✅ tests/Makefile - 独立的测试编译系统${NC}"
echo ""
echo -e "${YELLOW}推荐使用方法:${NC}"
echo -e "${CYAN}1. 日常开发: make (只编译主程序)${NC}"
echo -e "${CYAN}2. 完整测试: make with-tests (编译所有)${NC}"
echo -e "${CYAN}3. 只测试: make tests (只编译测试)${NC}"
echo -e "${CYAN}4. 独立测试开发: cd tests && make${NC}"
