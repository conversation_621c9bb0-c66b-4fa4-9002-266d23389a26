#!/bin/bash

# 模块添加脚本 - 用于在项目中创建新的传感器模块

# 确保脚本被正确调用
if [ $# -ne 1 ]; then
    echo "用法: $0 <模块名称>"
    exit 1
fi

MODULE_NAME=$1
MODULE_DIR="src/modules/${MODULE_NAME}"

# 创建模块目录结构
mkdir -p ${MODULE_DIR}/src
mkdir -p ${MODULE_DIR}/include
mkdir -p tests/${MODULE_NAME}_test

# 创建基础源文件和头文件
cat > ${MODULE_DIR}/include/${MODULE_NAME}.h << EOF
#ifndef ${MODULE_NAME^^}_H
#define ${MODULE_NAME^^}_H

#ifdef __cplusplus
extern "C" {
#endif

// ${MODULE_NAME} 模块公共接口
int ${MODULE_NAME}_init(void);
void ${MODULE_NAME}_deinit(void);
int ${MODULE_NAME}_read_data(void* data, size_t size);

#ifdef __cplusplus
}
#endif

#endif // ${MODULE_NAME^^}_H
EOF

cat > ${MODULE_DIR}/src/${MODULE_NAME}.c << EOF
#include <stdio.h>
#include <stdlib.h>
#include "${MODULE_NAME}.h"

// ${MODULE_NAME} 模块实现

int ${MODULE_NAME}_init(void) {
    printf("初始化 ${MODULE_NAME} 模块\n");
    // 初始化代码
    return 0;
}

void ${MODULE_NAME}_deinit(void) {
    printf("释放 ${MODULE_NAME} 模块\n");
    // 释放资源代码
}

int ${MODULE_NAME}_read_data(void* data, size_t size) {
    printf("读取 ${MODULE_NAME} 数据\n");
    // 读取数据代码
    return 0;
}
EOF

cat > tests/${MODULE_NAME}_test/${MODULE_NAME}_test.c << EOF
#include <stdio.h>
#include <stdlib.h>
#include <assert.h>
#include "${MODULE_NAME}.h"

int main(void) {
    printf("开始测试 ${MODULE_NAME} 模块\n");
    
    int result = ${MODULE_NAME}_init();
    assert(result == 0);
    
    // 测试读取数据
    char data[100];
    result = ${MODULE_NAME}_read_data(data, sizeof(data));
    assert(result == 0);
    
    ${MODULE_NAME}_deinit();
    
    printf("${MODULE_NAME} 模块测试成功\n");
    return 0;
}
EOF

# 更新Makefile配置
if [ -f "Makefile" ]; then
    # 添加模块到MODULES列表
    sed -i "/^MODULES :=/ s/$/ ${MODULE_NAME}/" Makefile
    
    # 添加模块编译规则
    cat >> Makefile << EOF

# ${MODULE_NAME} 模块规则
LIB_OBJS_${MODULE_NAME} := \$(addprefix \$(BUILD_DIR)/${MODULE_NAME}/, \$(notdir \$(wildcard ${MODULE_DIR}/src/*.c)))

\$(BUILD_DIR)/lib${MODULE_NAME}.so: \$(LIB_OBJS_${MODULE_NAME})
	\$(CC) \$(CFLAGS) -shared -o \$@ \$^ \$(LDFLAGS)
	@cp \$@ \$(LIB_DIR)/

\$(BUILD_DIR)/${MODULE_NAME}/%.o: ${MODULE_DIR}/src/%.c | \$(BUILD_DIR)/${MODULE_NAME}
	\$(CC) \$(CFLAGS) -fPIC -c -o \$@ \$<

\$(BUILD_DIR)/${MODULE_NAME}:
	mkdir -p \$@
EOF
fi

echo "模块 ${MODULE_NAME} 创建成功!"
echo "模块目录: ${MODULE_DIR}"
echo "测试目录: tests/${MODULE_NAME}_test"    