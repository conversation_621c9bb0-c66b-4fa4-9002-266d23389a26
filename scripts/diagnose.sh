#!/bin/bash

# GreenLand 系统诊断脚本

# 获取脚本所在目录的父目录（项目根目录）
PROJECT_ROOT=$(dirname $(dirname $(realpath $0)))

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== GreenLand 系统诊断工具 ===${NC}"
echo "诊断时间: $(date)"
echo "项目路径: $PROJECT_ROOT"
echo ""

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 1. 系统基本信息
echo -e "${PURPLE}1. 系统基本信息${NC}"
echo "----------------------------------------"
echo "操作系统: $(uname -s) $(uname -r)"
echo "架构: $(uname -m)"
echo "主机名: $(hostname)"

if [ -f /etc/os-release ]; then
    echo "发行版: $(grep PRETTY_NAME /etc/os-release | cut -d'"' -f2)"
fi

echo "当前用户: $(whoami)"
echo "用户组: $(groups)"
echo ""

# 2. 硬件信息
echo -e "${PURPLE}2. 硬件信息${NC}"
echo "----------------------------------------"

# CPU信息
if [ -f /proc/cpuinfo ]; then
    echo "CPU: $(grep "model name" /proc/cpuinfo | head -1 | cut -d: -f2 | xargs)"
    echo "CPU核心数: $(nproc)"
fi

# 内存信息
if [ -f /proc/meminfo ]; then
    echo "总内存: $(grep MemTotal /proc/meminfo | awk '{print $2/1024 " MB"}')"
    echo "可用内存: $(grep MemAvailable /proc/meminfo | awk '{print $2/1024 " MB"}')"
fi

# 温度信息
if command -v vcgencmd >/dev/null 2>&1; then
    echo "CPU温度: $(vcgencmd measure_temp 2>/dev/null || echo '无法获取')"
fi

echo ""

# 3. I2C系统检查
echo -e "${PURPLE}3. I2C系统检查${NC}"
echo "----------------------------------------"

# 检查I2C设备文件
echo "I2C设备文件:"
if ls /dev/i2c-* >/dev/null 2>&1; then
    ls -l /dev/i2c-* | while read line; do
        echo "  $line"
    done
else
    echo -e "  ${RED}❌ 未找到I2C设备文件${NC}"
fi

# 检查I2C模块
echo ""
echo "I2C内核模块:"
if lsmod | grep -q i2c; then
    lsmod | grep i2c | while read line; do
        echo -e "  ${GREEN}✅ $line${NC}"
    done
else
    echo -e "  ${RED}❌ I2C模块未加载${NC}"
fi

# 检查I2C权限
echo ""
echo "I2C设备权限:"
if [ -c /dev/i2c-2 ]; then
    perm=$(ls -l /dev/i2c-2 | awk '{print $1}')
    owner=$(ls -l /dev/i2c-2 | awk '{print $3":"$4}')
    echo -e "  ${GREEN}✅ /dev/i2c-2 存在${NC}"
    echo "  权限: $perm"
    echo "  所有者: $owner"
    
    # 检查当前用户是否可以访问
    if [ -r /dev/i2c-2 ] && [ -w /dev/i2c-2 ]; then
        echo -e "  ${GREEN}✅ 当前用户可以访问${NC}"
    else
        echo -e "  ${YELLOW}⚠️  当前用户无法访问，需要sudo权限${NC}"
    fi
else
    echo -e "  ${RED}❌ /dev/i2c-2 不存在${NC}"
fi

echo ""

# 4. 传感器连接检查
echo -e "${PURPLE}4. 传感器连接检查${NC}"
echo "----------------------------------------"

if command -v i2cdetect >/dev/null 2>&1; then
    if [ -c /dev/i2c-2 ]; then
        echo "I2C总线扫描 (需要sudo权限):"
        if sudo i2cdetect -y 2 >/dev/null 2>&1; then
            echo ""
            sudo i2cdetect -y 2
            echo ""
            
            # 检查特定传感器
            if sudo i2cdetect -y 2 | grep -q "38"; then
                echo -e "${GREEN}✅ AHT20 (0x38) 已检测到${NC}"
            else
                echo -e "${RED}❌ AHT20 (0x38) 未检测到${NC}"
            fi
            
            if sudo i2cdetect -y 2 | grep -q "23"; then
                echo -e "${GREEN}✅ BH1750 (0x23) 已检测到${NC}"
            else
                echo -e "${RED}❌ BH1750 (0x23) 未检测到${NC}"
            fi
        else
            echo -e "${RED}❌ I2C扫描失败，请检查权限和硬件连接${NC}"
        fi
    else
        echo -e "${RED}❌ I2C设备不可用${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  i2c-tools 未安装，无法扫描I2C设备${NC}"
    echo "安装命令: sudo apt install i2c-tools"
fi

echo ""

# 5. 编译环境检查
echo -e "${PURPLE}5. 编译环境检查${NC}"
echo "----------------------------------------"

# 检查编译器
if command -v gcc >/dev/null 2>&1; then
    echo -e "${GREEN}✅ GCC: $(gcc --version | head -1)${NC}"
else
    echo -e "${RED}❌ GCC 未安装${NC}"
fi

if command -v g++ >/dev/null 2>&1; then
    echo -e "${GREEN}✅ G++: $(g++ --version | head -1)${NC}"
else
    echo -e "${RED}❌ G++ 未安装${NC}"
fi

if command -v make >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Make: $(make --version | head -1)${NC}"
else
    echo -e "${RED}❌ Make 未安装${NC}"
fi

echo ""

# 6. 项目文件检查
echo -e "${PURPLE}6. 项目文件检查${NC}"
echo "----------------------------------------"

# 检查关键文件
files_to_check=(
    "Makefile"
    "src/main.cpp"
    "src/modules/aht20/include/aht20.h"
    "src/modules/bh1750/include/bh1750.h"
)

for file in "${files_to_check[@]}"; do
    if [ -f "$file" ]; then
        echo -e "${GREEN}✅ $file${NC}"
    else
        echo -e "${RED}❌ $file 缺失${NC}"
    fi
done

# 检查构建产物
echo ""
echo "构建产物:"
if [ -d "bin" ]; then
    echo -e "${GREEN}✅ bin/ 目录存在${NC}"
    if [ -f "bin/GreenLand" ]; then
        echo -e "${GREEN}✅ 主程序已编译${NC}"
    else
        echo -e "${YELLOW}⚠️  主程序未编译${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  bin/ 目录不存在${NC}"
fi

if [ -d "lib" ]; then
    echo -e "${GREEN}✅ lib/ 目录存在${NC}"
    lib_count=$(ls lib/*.so 2>/dev/null | wc -l)
    echo "  库文件数量: $lib_count"
else
    echo -e "${YELLOW}⚠️  lib/ 目录不存在${NC}"
fi

echo ""

# 7. 运行时检查
echo -e "${PURPLE}7. 运行时检查${NC}"
echo "----------------------------------------"

# 检查是否有GreenLand进程在运行
if pgrep -f GreenLand >/dev/null; then
    echo -e "${GREEN}✅ GreenLand进程正在运行${NC}"
    ps aux | grep -E "GreenLand" | grep -v grep | while read line; do
        echo "  $line"
    done
else
    echo -e "${YELLOW}⚠️  GreenLand进程未运行${NC}"
fi

# 检查日志目录
if [ -d "Log" ]; then
    echo -e "${GREEN}✅ Log/ 目录存在${NC}"
    log_count=$(ls Log/*.log 2>/dev/null | wc -l)
    echo "  日志文件数量: $log_count"
    
    if [ $log_count -gt 0 ]; then
        echo "  最新日志文件:"
        ls -lt Log/*.log 2>/dev/null | head -3 | while read line; do
            echo "    $line"
        done
    fi
else
    echo -e "${YELLOW}⚠️  Log/ 目录不存在${NC}"
fi

echo ""

# 8. 建议和总结
echo -e "${PURPLE}8. 诊断总结和建议${NC}"
echo "----------------------------------------"

# 统计问题
error_count=0
warning_count=0

# 这里可以根据上面的检查结果来统计错误和警告
# 简化版本，实际应该根据具体检查结果

echo "诊断完成！"
echo ""

if [ ! -c /dev/i2c-2 ]; then
    echo -e "${RED}❌ 关键问题: I2C设备不可用${NC}"
    echo "   解决方案: 检查I2C配置，确保在 /boot/armbianEnv.txt 中启用了 i2c2"
    ((error_count++))
fi

if ! command -v gcc >/dev/null 2>&1; then
    echo -e "${RED}❌ 关键问题: 编译环境不完整${NC}"
    echo "   解决方案: sudo apt install build-essential"
    ((error_count++))
fi

if [ ! -f "bin/GreenLand" ]; then
    echo -e "${YELLOW}⚠️  建议: 编译项目${NC}"
    echo "   命令: make clean && make all"
    ((warning_count++))
fi

echo ""
echo "总结:"
echo "  错误: $error_count 个"
echo "  警告: $warning_count 个"

if [ $error_count -eq 0 ]; then
    echo -e "${GREEN}🎉 系统状态良好！${NC}"
else
    echo -e "${RED}⚠️  发现 $error_count 个需要解决的问题${NC}"
fi

echo ""
echo "如需更多帮助，请参考:"
echo "  - 故障排除指南: doc/Troubleshooting.md"
echo "  - 开发指南: doc/Development_Guide.md"
echo "  - API参考: doc/API_Reference.md"
