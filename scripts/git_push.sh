#!/bin/bash

# GreenLand 一键Git推送脚本
# 作者: 刘旭
# 邮箱: <EMAIL>

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Git配置
GIT_USER_EMAIL="<EMAIL>"
GIT_USER_NAME="刘旭"
GIT_REMOTE_URL="https://gitee.com/lvjing_nmg/hardware-fundamentals.git"
GIT_TOKEN="47058b370b7139bf6697d35629bcf25c"

# 函数：打印彩色消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 函数：检查Git状态
check_git_status() {
    print_message $BLUE "🔍 检查Git状态..."
    
    # 检查是否在Git仓库中
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        print_message $RED "❌ 当前目录不是Git仓库"
        exit 1
    fi
    
    # 检查当前分支
    current_branch=$(git branch --show-current)
    print_message $YELLOW "📍 当前分支: $current_branch"
    
    # 检查是否有未提交的更改
    if ! git diff-index --quiet HEAD --; then
        print_message $YELLOW "⚠️ 发现未提交的更改"
        git status --short
        return 1
    fi
    
    print_message $GREEN "✅ Git状态检查通过"
    return 0
}

# 函数：配置Git凭据
setup_git_credentials() {
    print_message $BLUE "🔧 配置Git凭据..."
    
    # 设置用户信息
    git config user.name "$GIT_USER_NAME"
    git config user.email "$GIT_USER_EMAIL"
    
    # 设置远程URL（包含token）
    git remote set-url origin "https://${GIT_USER_EMAIL}:${GIT_TOKEN}@gitee.com/lvjing_nmg/hardware-fundamentals.git"
    
    print_message $GREEN "✅ Git凭据配置完成"
}

# 函数：添加和提交更改
commit_changes() {
    local commit_message="$1"
    
    print_message $BLUE "📝 添加和提交更改..."
    
    # 添加所有更改
    git add .
    
    # 检查是否有更改需要提交
    if git diff-index --quiet HEAD --; then
        print_message $YELLOW "ℹ️ 没有新的更改需要提交"
        return 0
    fi
    
    # 提交更改
    if [ -z "$commit_message" ]; then
        # 生成默认提交消息
        timestamp=$(date '+%Y-%m-%d %H:%M:%S')
        commit_message="🔄 自动提交 - $timestamp

📝 更新内容:
- 代码更新和优化
- 文档完善
- 功能改进

👤 作者: $GIT_USER_NAME
📧 邮箱: $GIT_USER_EMAIL"
    fi
    
    git commit -m "$commit_message"
    print_message $GREEN "✅ 更改已提交"
}

# 函数：推送到远程仓库
push_to_remote() {
    local branch="$1"
    
    if [ -z "$branch" ]; then
        branch=$(git branch --show-current)
    fi
    
    print_message $BLUE "🚀 推送到远程仓库 ($branch 分支)..."
    
    # 推送到远程
    git push origin "$branch"
    
    print_message $GREEN "✅ 推送完成"
    print_message $BLUE "🔗 查看仓库: https://gitee.com/lvjing_nmg/hardware-fundamentals"
}

# 函数：显示帮助信息
show_help() {
    echo "GreenLand 一键Git推送脚本"
    echo ""
    echo "用法:"
    echo "  $0 [选项] [提交消息]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -s, --status   只检查状态，不推送"
    echo "  -f, --force    强制推送（跳过状态检查）"
    echo "  -b, --branch   指定推送分支"
    echo ""
    echo "示例:"
    echo "  $0                           # 自动提交并推送"
    echo "  $0 \"修复bug\"                # 使用自定义提交消息"
    echo "  $0 -b develop \"新功能\"      # 推送到指定分支"
    echo "  $0 -s                        # 只检查状态"
}

# 主函数
main() {
    local commit_message=""
    local target_branch=""
    local force_push=false
    local status_only=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -s|--status)
                status_only=true
                shift
                ;;
            -f|--force)
                force_push=true
                shift
                ;;
            -b|--branch)
                target_branch="$2"
                shift 2
                ;;
            *)
                if [ -z "$commit_message" ]; then
                    commit_message="$1"
                fi
                shift
                ;;
        esac
    done
    
    print_message $GREEN "🌱 GreenLand 一键Git推送脚本"
    print_message $GREEN "================================"
    
    # 配置Git凭据
    setup_git_credentials
    
    # 检查Git状态
    if ! check_git_status && [ "$force_push" = false ]; then
        if [ "$status_only" = true ]; then
            exit 0
        fi
        
        print_message $YELLOW "❓ 是否继续提交和推送? (y/N)"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            print_message $YELLOW "🚫 操作已取消"
            exit 0
        fi
    fi
    
    if [ "$status_only" = true ]; then
        print_message $GREEN "✅ 状态检查完成"
        exit 0
    fi
    
    # 提交更改
    commit_changes "$commit_message"
    
    # 推送到远程
    push_to_remote "$target_branch"
    
    print_message $GREEN "🎉 推送完成！"
    print_message $BLUE "📱 您可以在Gitee上查看更新"
}

# 运行主函数
main "$@"
