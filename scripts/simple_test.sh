#!/bin/bash

# 简单测试脚本
echo "🧪 测试 tests/Makefile"
echo "===================="

cd /home/<USER>/Workspace/GreenLandV01

echo "📁 当前目录: $(pwd)"
echo "📋 tests目录内容:"
ls -la tests/

echo ""
echo "🔧 进入tests目录并编译..."
cd tests

echo "📊 显示项目信息:"
make info

echo ""
echo "📋 列出测试文件:"
make list

echo ""
echo "🔧 编译测试程序:"
make

echo ""
echo "📁 检查结果:"
ls -la ../bin/Tests/ 2>/dev/null || echo "bin/Tests目录不存在"

echo ""
echo "✅ 测试完成"
