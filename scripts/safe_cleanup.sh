#!/bin/bash

# 安全清理脚本
# 作者: Alex
# 邮箱: <EMAIL>

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${GREEN}🧹 GreenLand 安全清理脚本${NC}"
echo -e "${GREEN}=========================${NC}"

# 检查权限
if [ "$EUID" -eq 0 ]; then
    echo -e "${YELLOW}⚠️ 检测到root权限，将安全清理文件${NC}"
else
    echo -e "${BLUE}ℹ️ 普通用户权限，某些文件可能需要sudo${NC}"
fi

echo ""

# 显示清理前状态
echo -e "${BLUE}📊 清理前状态:${NC}"

# 检查Log目录
if [ -d "Log" ]; then
    log_files=$(find Log -name "*.log" 2>/dev/null)
    if [ -n "$log_files" ]; then
        echo -e "${YELLOW}日志文件:${NC}"
        echo "$log_files"
    else
        echo -e "${GREEN}✅ Log目录已清空${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ Log目录不存在${NC}"
fi

# 检查照片目录
if [ -d "media/photos" ]; then
    photo_files=$(find media/photos -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" 2>/dev/null)
    if [ -n "$photo_files" ]; then
        echo -e "${YELLOW}照片文件:${NC}"
        echo "$photo_files"
    else
        echo -e "${GREEN}✅ 照片目录已清空${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ media/photos目录不存在${NC}"
fi

# 检查视频目录
if [ -d "media/videos" ]; then
    video_files=$(find media/videos -name "*.mp4" -o -name "*.avi" -o -name "*.mov" 2>/dev/null)
    if [ -n "$video_files" ]; then
        echo -e "${YELLOW}视频文件:${NC}"
        echo "$video_files"
    else
        echo -e "${GREEN}✅ 视频目录已清空${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ media/videos目录不存在${NC}"
fi

# 检查bin目录
if [ -d "bin" ]; then
    bin_files=$(find bin -type f 2>/dev/null)
    if [ -n "$bin_files" ]; then
        echo -e "${YELLOW}bin目录文件:${NC}"
        echo "$bin_files"
    else
        echo -e "${GREEN}✅ bin目录已清空${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ bin目录不存在${NC}"
fi

echo ""

# 开始清理
echo -e "${BLUE}🧹 开始清理...${NC}"

# 清理日志文件
echo -e "${BLUE}📝 清理日志文件...${NC}"
if [ -d "Log" ]; then
    find Log -name "*.log" -type f -exec rm -f {} \; 2>/dev/null
    echo -e "${GREEN}✅ 日志文件清理完成${NC}"
fi

# 清理照片文件
echo -e "${BLUE}📸 清理照片文件...${NC}"
if [ -d "media/photos" ]; then
    # 尝试普通删除
    find media/photos -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" | while read file; do
        if rm -f "$file" 2>/dev/null; then
            echo "  删除: $file"
        else
            echo "  需要sudo权限删除: $file"
            sudo rm -f "$file" 2>/dev/null && echo "  已删除: $file"
        fi
    done
    echo -e "${GREEN}✅ 照片文件清理完成${NC}"
fi

# 清理视频文件
echo -e "${BLUE}🎥 清理视频文件...${NC}"
if [ -d "media/videos" ]; then
    find media/videos -name "*.mp4" -o -name "*.avi" -o -name "*.mov" | while read file; do
        if rm -f "$file" 2>/dev/null; then
            echo "  删除: $file"
        else
            echo "  需要sudo权限删除: $file"
            sudo rm -f "$file" 2>/dev/null && echo "  已删除: $file"
        fi
    done
    echo -e "${GREEN}✅ 视频文件清理完成${NC}"
fi

# 清理bin目录
echo -e "${BLUE}🗂️ 清理bin目录...${NC}"
if [ -d "bin" ]; then
    # 保留bin目录结构，只删除文件
    find bin -type f | while read file; do
        if rm -f "$file" 2>/dev/null; then
            echo "  删除: $file"
        else
            echo "  需要sudo权限删除: $file"
            sudo rm -f "$file" 2>/dev/null && echo "  已删除: $file"
        fi
    done
    
    # 删除空的子目录（除了bin本身）
    find bin -type d -empty -not -path "bin" -delete 2>/dev/null
    echo -e "${GREEN}✅ bin目录清理完成${NC}"
fi

echo ""
echo -e "${GREEN}🎉 清理完成！${NC}"

# 显示清理后状态
echo -e "${BLUE}📊 清理后状态:${NC}"
echo -e "${GREEN}✅ Log目录: $(find Log -name "*.log" 2>/dev/null | wc -l) 个日志文件${NC}"
echo -e "${GREEN}✅ 照片目录: $(find media/photos -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" 2>/dev/null | wc -l) 个照片文件${NC}"
echo -e "${GREEN}✅ 视频目录: $(find media/videos -name "*.mp4" -o -name "*.avi" -o -name "*.mov" 2>/dev/null | wc -l) 个视频文件${NC}"
echo -e "${GREEN}✅ bin目录: $(find bin -type f 2>/dev/null | wc -l) 个文件${NC}"

echo ""
echo -e "${CYAN}💡 提示:${NC}"
echo -e "${YELLOW}- 重新编译程序: make${NC}"
echo -e "${YELLOW}- 运行程序会重新生成日志和媒体文件${NC}"
echo -e "${YELLOW}- 目录结构已保留，程序可正常运行${NC}"
