#!/bin/bash

# 示例代码编译脚本

# 获取脚本所在目录的父目录（项目根目录）
PROJECT_ROOT=$(dirname $(dirname $(realpath $0)))

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== GreenLand 示例代码编译脚本 ===${NC}"
echo "项目根目录: $PROJECT_ROOT"

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 检查库文件是否存在
if [ ! -f "lib/libaht20.so" ] || [ ! -f "lib/libbh1750.so" ]; then
    echo -e "${YELLOW}库文件不存在，正在编译模块...${NC}"
    ./scripts/build.sh --clean
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 模块编译失败${NC}"
        exit 1
    fi
fi

# 创建示例输出目录
mkdir -p bin/examples

echo -e "${BLUE}开始编译示例代码...${NC}"

# 编译AHT20示例
echo -e "${YELLOW}编译AHT20示例...${NC}"

# AHT20基础示例
gcc -Wall -O2 -I./src/modules/aht20/include \
    examples/aht20/basic_usage.c \
    -L./lib -Wl,-rpath,$PWD/lib -laht20 -lpthread \
    -o bin/examples/aht20_basic_example

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ AHT20基础示例编译成功${NC}"
else
    echo -e "${RED}❌ AHT20基础示例编译失败${NC}"
fi

# AHT20高级示例
gcc -Wall -O2 -I./src/modules/aht20/include \
    examples/aht20/advanced_usage.c \
    -L./lib -Wl,-rpath,$PWD/lib -laht20 -lpthread -lm \
    -o bin/examples/aht20_advanced_example

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ AHT20高级示例编译成功${NC}"
else
    echo -e "${RED}❌ AHT20高级示例编译失败${NC}"
fi

# 编译BH1750示例
echo -e "${YELLOW}编译BH1750示例...${NC}"

# BH1750基础示例
gcc -Wall -O2 -I./src/modules/bh1750/include \
    examples/bh1750/basic_usage.c \
    -L./lib -Wl,-rpath,$PWD/lib -lbh1750 -lpthread \
    -o bin/examples/bh1750_basic_example

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ BH1750基础示例编译成功${NC}"
else
    echo -e "${RED}❌ BH1750基础示例编译失败${NC}"
fi

# BH1750高级示例
gcc -Wall -O2 -I./src/modules/bh1750/include \
    examples/bh1750/advanced_usage.c \
    -L./lib -Wl,-rpath,$PWD/lib -lbh1750 -lpthread \
    -o bin/examples/bh1750_advanced_example

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ BH1750高级示例编译成功${NC}"
else
    echo -e "${RED}❌ BH1750高级示例编译失败${NC}"
fi

# 编译组合示例
echo -e "${YELLOW}编译组合示例...${NC}"

# 环境监控示例
g++ -Wall -O2 -I./src/modules/aht20/include -I./src/modules/bh1750/include \
    examples/combined/environmental_monitor.c \
    -L./lib -Wl,-rpath,$PWD/lib -laht20 -lbh1750 -lpthread -lm \
    -o bin/examples/environmental_monitor

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 环境监控示例编译成功${NC}"
else
    echo -e "${RED}❌ 环境监控示例编译失败${NC}"
fi

# 显示编译结果
echo -e "${BLUE}=== 编译完成 ===${NC}"
echo -e "${GREEN}生成的示例程序:${NC}"
ls -la bin/examples/ 2>/dev/null || echo "无示例程序"

echo -e "${BLUE}=== 使用方法 ===${NC}"
echo -e "${YELLOW}AHT20示例:${NC}"
echo "  sudo ./bin/examples/aht20_basic_example"
echo "  sudo ./bin/examples/aht20_advanced_example"
echo ""
echo -e "${YELLOW}BH1750示例:${NC}"
echo "  sudo ./bin/examples/bh1750_basic_example"
echo "  sudo ./bin/examples/bh1750_advanced_example"
echo ""
echo -e "${YELLOW}组合示例:${NC}"
echo "  sudo ./bin/examples/environmental_monitor"

echo -e "${GREEN}🎉 示例编译完成!${NC}"
