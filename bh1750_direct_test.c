#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <linux/i2c-dev.h>
#include <string.h>
#include <errno.h>

int main() {
    printf("=== 直接测试BH1750 ===\n");
    
    // 打开I2C设备
    int fd = open("/dev/i2c-2", O_RDWR);
    if (fd < 0) {
        printf("无法打开I2C设备: %s\n", strerror(errno));
        return 1;
    }
    printf("✅ I2C设备打开成功\n");
    
    // 设置I2C地址
    if (ioctl(fd, I2C_SLAVE, 0x23) < 0) {
        printf("无法设置I2C地址: %s\n", strerror(errno));
        close(fd);
        return 1;
    }
    printf("✅ I2C地址设置成功\n");
    
    // 发送上电命令
    unsigned char power_on = 0x01;
    if (write(fd, &power_on, 1) != 1) {
        printf("上电命令失败: %s\n", strerror(errno));
        close(fd);
        return 1;
    }
    printf("✅ 上电命令发送成功\n");
    usleep(10000);
    
    // 发送连续高分辨率测量命令
    unsigned char measure_cmd = 0x10;
    if (write(fd, &measure_cmd, 1) != 1) {
        printf("测量命令失败: %s\n", strerror(errno));
        close(fd);
        return 1;
    }
    printf("✅ 测量命令发送成功\n");
    
    // 等待测量完成
    printf("等待测量完成...\n");
    sleep(1);
    
    // 连续读取5次
    for (int i = 0; i < 5; i++) {
        printf("\n--- 第 %d 次读取 ---\n", i + 1);
        
        unsigned char data[2];
        if (read(fd, data, 2) != 2) {
            printf("读取数据失败: %s\n", strerror(errno));
        } else {
            unsigned short raw = (data[0] << 8) | data[1];
            float lux = raw / 1.2;
            
            printf("原始数据: 0x%02X 0x%02X (%d)\n", data[0], data[1], raw);
            printf("光照强度: %.2f lx\n", lux);
        }
        
        sleep(1);
    }
    
    close(fd);
    printf("\n✅ 测试完成\n");
    return 0;
}
