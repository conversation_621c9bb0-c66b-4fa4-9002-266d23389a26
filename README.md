# 🌱 GreenLand 智能农业监控系统

**作者**: Alex
**版本**: 2.0.0 (优化版)
**平台**: Orange Pi Zero 2W

## 📋 项目概述

GreenLand是一个基于Orange Pi Zero 2W的智能农业监控系统，集成多种传感器和摄像头，用于实时监控农业环境。

### 🎯 主要功能

- 🌊 **水位检测** - HC-SR04超声波传感器
- 🌡️ **温湿度监测** - AHT20高精度传感器
- ☀️ **光照强度检测** - BH1750光照传感器
- �� **图像采集** - USB摄像头 (500万像素)
- 📊 **数据记录和分析**
- ⚙️ **统一配置管理** - 配置文件支持
- 📈 **性能监控** - 系统性能实时监控
- 🛡️ **错误处理和恢复** - 智能错误恢复
- 🔧 **模块化架构** - 可扩展模块系统
- 🌐 **Web界面监控** (计划中)

### 🔧 硬件要求

- Orange Pi Zero 2W 开发板
- HC-SR04 超声波传感器
- AHT20 温湿度传感器
- BH1750 光照传感器
- USB 摄像头
- MicroSD 卡 (16GB+)

## 🚀 开发状态

**当前状态**: ✅ **2.0.0 优化版发布**

本项目已完成重大架构优化，具备企业级智能农业监控系统的所有核心特性。

### ✅ 已完成功能

- [x] 传感器模块开发 (HC-SR04, AHT20, BH1750)
- [x] 摄像头模块 (拍照、录像、旋转、500万像素)
- [x] 日志系统
- [x] 模块化架构重构
- [x] API文档编写
- [x] **统一配置管理系统** 🆕
- [x] **性能监控系统** 🆕
- [x] **错误处理和恢复机制** 🆕
- [x] **核心系统架构** 🆕
- [x] **动态模块检测和编译** 🆕

### 🔄 正在开发

- [ ] Web界面监控系统
- [ ] 数据存储和分析
- [ ] 移动端应用
- [ ] 云端数据同步

### 📅 计划功能

- [ ] MQTT数据上传
- [ ] 移动端APP
- [ ] 自动化控制
- [ ] 机器学习分析

## 🌿 分支说明

- **master** - 稳定发布分支 (当前为开发预览)
- **develop** - 主要开发分支 (活跃开发)

## 📚 文档

详细的API文档和使用说明请查看 \`develop\` 分支：

\`\`\`bash
git checkout develop
\`\`\`

每个模块都有完整的中文API使用说明：
- 📝 摄像头模块API使用说明.md
- 📝 日志模块API使用说明.md
- 📝 HC-SR04模块API使用说明.md
- 📝 AHT20模块API使用说明.md
- 📝 BH1750模块API使用说明.md

## 🚀 快速开始

### 环境准备

\`\`\`bash
# 安装依赖
sudo apt-get update
sudo apt-get install build-essential cmake
sudo apt-get install libopencv-dev
sudo apt-get install wiringpi

# 启用I2C
sudo orangepi-config
# 选择 System -> Hardware -> i2c1 -> Enable
\`\`\`

### 编译项目

\`\`\`bash
# 克隆项目
git clone <repository-url>
cd GreenLandV01

# 切换到开发分支
git checkout develop

# 编译
make clean
make
\`\`\`

### 运行测试

\`\`\`bash
# 运行传感器测试
./bin/Tests/hcsr04_test
./bin/Tests/aht20_test
./bin/Tests/bh1750_test
./bin/Tests/camera_test
\`\`\`

## 🤝 贡献

欢迎贡献代码！请遵循以下流程：

1. Fork 项目
2. 创建功能分支 (\`git checkout -b feature/AmazingFeature\`)
3. 提交更改 (\`git commit -m 'Add some AmazingFeature'\`)
4. 推送到分支 (\`git push origin feature/AmazingFeature\`)
5. 创建 Pull Request

## 📄 许可证

本项目为专有软件，保留所有权利 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

**作者**: aubuty
**邮箱**: <EMAIL>
**项目链接**: https://gitee.com/lvjing_nmg/hardware-fundamentals.git

---

⚠️ **注意**: 本项目仍在开发中，功能可能不完整或存在问题。生产环境使用请谨慎。

🌱 **愿景**: 让智能农业技术更加普及和易用！
