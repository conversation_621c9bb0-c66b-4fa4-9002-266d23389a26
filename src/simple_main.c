/**
 * @file simple_main.c
 * @brief GreenLand 简单主程序 - 直接运行传感器
 * <AUTHOR>
 * @date 2024
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <string.h>
#include <sys/stat.h>
#include <time.h>

// 包含传感器模块
#include "modules/logger/include/logger.h"
#include "modules/hcsr04/include/hcsr04.h"

#ifdef ENABLE_CAMERA
#include "modules/camera/include/camera.h"
#endif

static volatile int running = 1;
static logger_t main_logger = NULL;

void signal_handler(int sig) {
    printf("\n收到信号 %d，正在退出...\n", sig);
    running = 0;
}

// 创建必要的目录
int create_directories() {
    struct stat st;
    memset(&st, 0, sizeof(st));

    // 创建Log目录
    if (stat("Log", &st) == -1) {
        if (mkdir("Log", 0755) == -1) {
            printf("❌ 无法创建Log目录\n");
            return -1;
        }
    }

    // 创建media目录
    if (stat("media", &st) == -1) {
        if (mkdir("media", 0755) == -1) {
            printf("❌ 无法创建media目录\n");
            return -1;
        }
    }

    // 创建photos子目录
    if (stat("media/photos", &st) == -1) {
        if (mkdir("media/photos", 0755) == -1) {
            printf("❌ 无法创建media/photos目录\n");
            return -1;
        }
    }

    // 创建videos子目录
    if (stat("media/videos", &st) == -1) {
        if (mkdir("media/videos", 0755) == -1) {
            printf("❌ 无法创建media/videos目录\n");
            return -1;
        }
    }

    return 0;
}

// 初始化日志系统
int init_logging() {
    log_config_t config;
    logger_get_default_config(&config, "greenland");
    config.level = LOG_LEVEL_INFO;
    config.enable_console = true;

    main_logger = logger_create(&config);
    if (!main_logger) {
        printf("❌ 日志系统初始化失败\n");
        return -1;
    }

    logger_info(main_logger, "GreenLand 智能农业监控系统启动");
    return 0;
}

// 测试水位传感器
void test_water_sensor() {
    printf("\n🌊 测试水位传感器...\n");

    if (hcsr04_init() != HCSR04_OK) {
        printf("⚠️ 水位传感器初始化失败（可能无硬件）\n");
        logger_warn(main_logger, "水位传感器初始化失败");
        return;
    }

    printf("✅ 水位传感器初始化成功\n");
    logger_info(main_logger, "水位传感器初始化成功");

    // 设置水箱参数
    hcsr04_set_tank_height(35.0f);  // 35cm水箱
    hcsr04_set_sensor_offset(3.0f); // 3cm偏移

    for (int i = 0; i < 5 && running; i++) {
        float distance, water_level;

        if (hcsr04_read_distance(&distance) == HCSR04_OK &&
            hcsr04_read_water_level(&water_level) == HCSR04_OK) {

            printf("📏 测量 %d: 距离=%.2fcm, 水位=%.2fcm (%.1f%%)\n",
                   i+1, distance, water_level,
                   (water_level / 35.0) * 100);

            logger_info(main_logger, "水位测量: 距离=%.2fcm, 水位=%.2fcm",
                       distance, water_level);
        } else {
            printf("❌ 测量失败\n");
            logger_warn(main_logger, "水位测量失败");
        }

        sleep(2);
    }

    hcsr04_deinit();
    printf("✅ 水位传感器测试完成\n");
}

#ifdef ENABLE_CAMERA
// 测试摄像头
void test_camera() {
    printf("\n📷 测试摄像头...\n");

    // 扫描设备
    int devices[4];
    int device_count = camera_scan_devices(devices, 4);

    if (device_count == 0) {
        printf("⚠️ 未找到摄像头设备\n");
        logger_warn(main_logger, "未找到摄像头设备");
        return;
    }

    printf("✅ 找到 %d 个摄像头设备\n", device_count);

    // 创建摄像头
    camera_config_t config;
    camera_get_default_config(&config);
    config.device_id = devices[0];
    config.width = 1280;   // 降低分辨率以提高兼容性
    config.height = 720;

    camera_t camera = camera_create(&config);
    if (!camera) {
        printf("❌ 摄像头创建失败\n");
        logger_error(main_logger, "摄像头创建失败");
        return;
    }

    if (camera_init(camera) != CAMERA_OK) {
        printf("⚠️ 摄像头初始化失败\n");
        logger_warn(main_logger, "摄像头初始化失败");
        camera_destroy(camera);
        return;
    }

    printf("✅ 摄像头初始化成功\n");
    logger_info(main_logger, "摄像头初始化成功");

    // 拍照测试
    char filename[64];
    time_t now = time(NULL);
    struct tm *tm_info = localtime(&now);
    strftime(filename, sizeof(filename), "test_%Y%m%d_%H%M%S", tm_info);

    printf("📸 拍照测试: %s\n", filename);
    if (camera_quick_photo(camera, filename) == CAMERA_OK) {
        printf("✅ 拍照成功，保存到 media/photos/%s.jpg\n", filename);
        logger_info(main_logger, "拍照成功: %s", filename);
    } else {
        printf("❌ 拍照失败\n");
        logger_error(main_logger, "拍照失败");
    }

    // 短视频测试
    strftime(filename, sizeof(filename), "test_video_%Y%m%d_%H%M%S", tm_info);
    printf("🎥 录像测试: %s (5秒)\n", filename);

    if (camera_quick_video(camera, filename, 5) == CAMERA_OK) {
        printf("✅ 录像成功，保存到 media/videos/%s.mp4\n", filename);
        logger_info(main_logger, "录像成功: %s", filename);
    } else {
        printf("❌ 录像失败\n");
        logger_error(main_logger, "录像失败");
    }

    camera_destroy(camera);
    printf("✅ 摄像头测试完成\n");
}
#endif

int main(int argc, char* argv[]) {
    printf("🌱 GreenLand 智能农业监控系统\n");
    printf("==============================\n");
    printf("作者: 刘旭\n");
    printf("版本: 1.0.0\n");
    printf("平台: Orange Pi Zero 2W\n\n");

    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 创建目录
    if (create_directories() != 0) {
        return 1;
    }
    printf("✅ 目录结构创建完成\n");

    // 初始化日志
    if (init_logging() != 0) {
        return 1;
    }
    printf("✅ 日志系统初始化完成\n");

    // 显示帮助信息
    if (argc > 1 && strcmp(argv[1], "-h") == 0) {
        printf("\n用法: %s [选项]\n", argv[0]);
        printf("选项:\n");
        printf("  -h    显示帮助信息\n");
        printf("  -t    运行测试模式\n");
        printf("\n");
        return 0;
    }

    // 测试模式
    if (argc > 1 && strcmp(argv[1], "-t") == 0) {
        printf("\n🧪 运行测试模式\n");
        printf("================\n");

        test_water_sensor();

#ifdef ENABLE_CAMERA
        if (running) {
            test_camera();
        }
#endif

        printf("\n🎉 测试完成\n");
        logger_info(main_logger, "测试模式完成");
    } else {
        printf("\n🔄 运行监控模式\n");
        printf("================\n");
        printf("💡 按 Ctrl+C 退出程序\n");
        printf("💡 使用 -t 参数运行测试模式\n");
        printf("💡 使用 -h 参数查看帮助\n\n");

        // 简单的监控循环
        int loop_count = 0;
        while (running) {
            loop_count++;
            printf("📊 系统运行中... 循环 %d\n", loop_count);
            logger_info(main_logger, "系统监控循环: %d", loop_count);

            // 每10次循环显示状态
            if (loop_count % 10 == 0) {
                printf("💚 系统运行正常 - 已运行 %d 个周期\n", loop_count);
            }

            sleep(5);
        }
    }

    // 清理
    if (main_logger) {
        logger_info(main_logger, "GreenLand 系统正常退出");
        logger_destroy(main_logger);
    }

    printf("\n👋 GreenLand 系统已退出\n");
    return 0;
}
