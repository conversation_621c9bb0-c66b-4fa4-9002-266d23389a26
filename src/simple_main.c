/**
 * @file simple_main.c
 * @brief GreenLand 智能农业监控系统 - 多线程传感器监控
 * <AUTHOR>
 * @date 2024
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <string.h>
#include <sys/stat.h>
#include <time.h>
#include <pthread.h>

// 包含传感器模块
#include "modules/logger/include/logger.h"
#include "modules/hcsr04/include/hcsr04.h"

#ifdef ENABLE_CAMERA
#include "modules/camera/include/camera.h"
#endif

static volatile int running = 1;
static logger_t main_logger = NULL;

// 线程句柄
static pthread_t water_sensor_thread;
static pthread_t camera_thread;

// 传感器数据结构
typedef struct {
    float distance;
    float water_level;
    float water_percentage;
    time_t last_update;
    int valid;
} water_sensor_data_t;

static water_sensor_data_t g_water_data = {0};
static pthread_mutex_t water_data_mutex = PTHREAD_MUTEX_INITIALIZER;

#ifdef ENABLE_CAMERA
static camera_t g_camera = NULL;
#endif

void signal_handler(int sig) {
    printf("\n收到信号 %d，正在退出...\n", sig);
    running = 0;
}

// 创建必要的目录
int create_directories() {
    struct stat st;
    memset(&st, 0, sizeof(st));

    // 创建Log目录
    if (stat("Log", &st) == -1) {
        if (mkdir("Log", 0755) == -1) {
            printf("❌ 无法创建Log目录\n");
            return -1;
        }
    }

    // 创建media目录
    if (stat("media", &st) == -1) {
        if (mkdir("media", 0755) == -1) {
            printf("❌ 无法创建media目录\n");
            return -1;
        }
    }

    // 创建photos子目录
    if (stat("media/photos", &st) == -1) {
        if (mkdir("media/photos", 0755) == -1) {
            printf("❌ 无法创建media/photos目录\n");
            return -1;
        }
    }

    // 创建videos子目录
    if (stat("media/videos", &st) == -1) {
        if (mkdir("media/videos", 0755) == -1) {
            printf("❌ 无法创建media/videos目录\n");
            return -1;
        }
    }

    return 0;
}

// 水位传感器线程函数
void* water_sensor_thread_func(void* arg) {
    (void)arg; // 避免未使用参数警告

    logger_info(main_logger, "水位传感器线程启动");

    // 初始化传感器
    if (hcsr04_init() != HCSR04_OK) {
        logger_error(main_logger, "水位传感器初始化失败");
        return NULL;
    }

    // 设置水箱参数
    hcsr04_set_tank_height(35.0f);  // 35cm水箱
    hcsr04_set_sensor_offset(3.0f); // 3cm偏移

    logger_info(main_logger, "水位传感器初始化成功，开始监控");

    while (running) {
        float distance, water_level;

        // 读取传感器数据
        if (hcsr04_read_distance(&distance) == HCSR04_OK &&
            hcsr04_read_water_level(&water_level) == HCSR04_OK) {

            // 更新全局数据
            pthread_mutex_lock(&water_data_mutex);
            g_water_data.distance = distance;
            g_water_data.water_level = water_level;
            g_water_data.water_percentage = (water_level / 35.0f) * 100.0f;
            g_water_data.last_update = time(NULL);
            g_water_data.valid = 1;
            pthread_mutex_unlock(&water_data_mutex);

            logger_info(main_logger, "水位数据: 距离=%.2fcm, 水位=%.2fcm (%.1f%%)",
                       distance, water_level, g_water_data.water_percentage);

            // 水位报警
            if (water_level < 5.0) {
                logger_warn(main_logger, "⚠️ 水位过低: %.2fcm", water_level);
            } else if (water_level > 30.0) {
                logger_info(main_logger, "💧 水位充足: %.2fcm", water_level);
            }
        } else {
            pthread_mutex_lock(&water_data_mutex);
            g_water_data.valid = 0;
            pthread_mutex_unlock(&water_data_mutex);

            logger_warn(main_logger, "水位传感器读取失败");
        }

        sleep(5); // 5秒间隔
    }

    hcsr04_deinit();
    logger_info(main_logger, "水位传感器线程退出");
    return NULL;
}

#ifdef ENABLE_CAMERA
// 摄像头线程函数
void* camera_thread_func(void* arg) {
    (void)arg; // 避免未使用参数警告

    logger_info(main_logger, "摄像头线程启动");

    // 扫描摄像头设备
    int devices[4];
    int device_count = camera_scan_devices(devices, 4);

    if (device_count == 0) {
        logger_warn(main_logger, "未找到摄像头设备");
        return NULL;
    }

    // 创建摄像头
    camera_config_t config;
    camera_get_default_config(&config);
    config.device_id = devices[0];
    config.width = 1280;   // 降低分辨率提高兼容性
    config.height = 720;

    g_camera = camera_create(&config);
    if (!g_camera) {
        logger_error(main_logger, "摄像头创建失败");
        return NULL;
    }

    if (camera_init(g_camera) != CAMERA_OK) {
        logger_error(main_logger, "摄像头初始化失败");
        camera_destroy(g_camera);
        g_camera = NULL;
        return NULL;
    }

    logger_info(main_logger, "摄像头初始化成功，开始定时拍照 (60秒间隔)");

    int photo_count = 0;
    while (running) {
        // 等待60秒或程序退出
        for (int i = 0; i < 60 && running; i++) {
            sleep(1);
        }

        if (!running) break;

        // 拍照
        char filename[64];
        time_t now = time(NULL);
        struct tm *tm_info = localtime(&now);
        strftime(filename, sizeof(filename), "auto_%Y%m%d_%H%M%S", tm_info);

        if (camera_quick_photo(g_camera, filename) == CAMERA_OK) {
            photo_count++;
            logger_info(main_logger, "📸 自动拍照成功: %s (第%d张)", filename, photo_count);
        } else {
            logger_error(main_logger, "自动拍照失败");
        }
    }

    if (g_camera) {
        camera_destroy(g_camera);
        g_camera = NULL;
    }

    logger_info(main_logger, "摄像头线程退出，共拍摄 %d 张照片", photo_count);
    return NULL;
}
#endif

// 初始化日志系统
int init_logging() {
    log_config_t config;
    logger_get_default_config(&config, "greenland");
    config.level = LOG_LEVEL_INFO;
    config.enable_console = true;

    main_logger = logger_create(&config);
    if (!main_logger) {
        printf("❌ 日志系统初始化失败\n");
        return -1;
    }

    logger_info(main_logger, "GreenLand 智能农业监控系统启动");
    return 0;
}

// 测试水位传感器
void test_water_sensor() {
    printf("\n🌊 测试水位传感器...\n");

    if (hcsr04_init() != HCSR04_OK) {
        printf("⚠️ 水位传感器初始化失败（可能无硬件）\n");
        logger_warn(main_logger, "水位传感器初始化失败");
        return;
    }

    printf("✅ 水位传感器初始化成功\n");
    logger_info(main_logger, "水位传感器初始化成功");

    // 设置水箱参数
    hcsr04_set_tank_height(35.0f);  // 35cm水箱
    hcsr04_set_sensor_offset(3.0f); // 3cm偏移

    for (int i = 0; i < 5 && running; i++) {
        float distance, water_level;

        if (hcsr04_read_distance(&distance) == HCSR04_OK &&
            hcsr04_read_water_level(&water_level) == HCSR04_OK) {

            printf("📏 测量 %d: 距离=%.2fcm, 水位=%.2fcm (%.1f%%)\n",
                   i+1, distance, water_level,
                   (water_level / 35.0) * 100);

            logger_info(main_logger, "水位测量: 距离=%.2fcm, 水位=%.2fcm",
                       distance, water_level);
        } else {
            printf("❌ 测量失败\n");
            logger_warn(main_logger, "水位测量失败");
        }

        sleep(2);
    }

    hcsr04_deinit();
    printf("✅ 水位传感器测试完成\n");
}

#ifdef ENABLE_CAMERA
// 测试摄像头
void test_camera() {
    printf("\n📷 测试摄像头...\n");

    // 扫描设备
    int devices[4];
    int device_count = camera_scan_devices(devices, 4);

    if (device_count == 0) {
        printf("⚠️ 未找到摄像头设备\n");
        logger_warn(main_logger, "未找到摄像头设备");
        return;
    }

    printf("✅ 找到 %d 个摄像头设备\n", device_count);

    // 创建摄像头
    camera_config_t config;
    camera_get_default_config(&config);
    config.device_id = devices[0];
    config.width = 1280;   // 降低分辨率以提高兼容性
    config.height = 720;

    camera_t camera = camera_create(&config);
    if (!camera) {
        printf("❌ 摄像头创建失败\n");
        logger_error(main_logger, "摄像头创建失败");
        return;
    }

    if (camera_init(camera) != CAMERA_OK) {
        printf("⚠️ 摄像头初始化失败\n");
        logger_warn(main_logger, "摄像头初始化失败");
        camera_destroy(camera);
        return;
    }

    printf("✅ 摄像头初始化成功\n");
    logger_info(main_logger, "摄像头初始化成功");

    // 拍照测试
    char filename[64];
    time_t now = time(NULL);
    struct tm *tm_info = localtime(&now);
    strftime(filename, sizeof(filename), "test_%Y%m%d_%H%M%S", tm_info);

    printf("📸 拍照测试: %s\n", filename);
    if (camera_quick_photo(camera, filename) == CAMERA_OK) {
        printf("✅ 拍照成功，保存到 media/photos/%s.jpg\n", filename);
        logger_info(main_logger, "拍照成功: %s", filename);
    } else {
        printf("❌ 拍照失败\n");
        logger_error(main_logger, "拍照失败");
    }

    // 短视频测试
    strftime(filename, sizeof(filename), "test_video_%Y%m%d_%H%M%S", tm_info);
    printf("🎥 录像测试: %s (5秒)\n", filename);

    if (camera_quick_video(camera, filename, 5) == CAMERA_OK) {
        printf("✅ 录像成功，保存到 media/videos/%s.mp4\n", filename);
        logger_info(main_logger, "录像成功: %s", filename);
    } else {
        printf("❌ 录像失败\n");
        logger_error(main_logger, "录像失败");
    }

    camera_destroy(camera);
    printf("✅ 摄像头测试完成\n");
}
#endif

// 显示传感器状态
void show_sensor_status() {
    pthread_mutex_lock(&water_data_mutex);
    if (g_water_data.valid) {
        printf("💧 水位传感器: 距离=%.2fcm, 水位=%.2fcm (%.1f%%) [%s]\n",
               g_water_data.distance, g_water_data.water_level,
               g_water_data.water_percentage,
               ctime(&g_water_data.last_update));
    } else {
        printf("❌ 水位传感器: 数据无效\n");
    }
    pthread_mutex_unlock(&water_data_mutex);

#ifdef ENABLE_CAMERA
    if (g_camera) {
        printf("📷 摄像头: 运行中 (60秒间隔自动拍照)\n");
    } else {
        printf("❌ 摄像头: 未初始化\n");
    }
#else
    printf("📷 摄像头: 未启用\n");
#endif
}

int GreenLand(int argc, char* argv[]) {
    printf("🌱 GreenLand 智能农业监控系统\n");
    printf("==============================\n");
    printf("作者: aubuty\n");
    printf("版本: 1.0.0\n");
    printf("平台: Orange Pi Zero 2W\n\n");

    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 创建目录
    if (create_directories() != 0) {
        return 1;
    }
    printf("✅ 目录结构创建完成\n");

    // 初始化日志
    if (init_logging() != 0) {
        return 1;
    }
    printf("✅ 日志系统初始化完成\n");

    // 显示帮助信息
    if (argc > 1 && strcmp(argv[1], "-h") == 0) {
        printf("\n用法: %s [选项]\n", argv[0]);
        printf("选项:\n");
        printf("  -h    显示帮助信息\n");
        printf("  -t    运行测试模式\n");
        printf("\n");
        return 0;
    }

    // 测试模式
    if (argc > 1 && strcmp(argv[1], "-t") == 0) {
        printf("\n🧪 运行测试模式\n");
        printf("================\n");

        test_water_sensor();

#ifdef ENABLE_CAMERA
        if (running) {
            test_camera();
        }
#endif

        printf("\n🎉 测试完成\n");
        logger_info(main_logger, "测试模式完成");
    } else {
        printf("\n🔄 启动多线程监控模式\n");
        printf("========================\n");
        printf("💡 按 Ctrl+C 退出程序\n");
        printf("💡 使用 -t 参数运行测试模式\n");
        printf("💡 使用 -h 参数查看帮助\n\n");

        // 启动水位传感器线程
        printf("🌊 启动水位传感器线程...\n");
        if (pthread_create(&water_sensor_thread, NULL, water_sensor_thread_func, NULL) != 0) {
            logger_error(main_logger, "创建水位传感器线程失败");
            return 1;
        }

#ifdef ENABLE_CAMERA
        // 启动摄像头线程
        printf("📷 启动摄像头线程...\n");
        if (pthread_create(&camera_thread, NULL, camera_thread_func, NULL) != 0) {
            logger_error(main_logger, "创建摄像头线程失败");
            running = 0;
            pthread_join(water_sensor_thread, NULL);
            return 1;
        }
#endif

        printf("✅ 所有线程启动完成\n\n");
        logger_info(main_logger, "GreenLand 多线程监控系统启动完成");

        // 主监控循环 - 每30秒显示一次状态
        int status_count = 0;
        while (running) {
            sleep(30);
            if (!running) break;

            status_count++;
            printf("\n📊 系统状态报告 #%d (运行时间: %d分钟)\n", status_count, status_count * 30 / 60);
            printf("==========================================\n");
            show_sensor_status();
            printf("\n");

            logger_info(main_logger, "系统状态检查 #%d", status_count);
        }

        printf("\n🛑 正在停止所有线程...\n");

        // 等待线程结束
        pthread_join(water_sensor_thread, NULL);
        printf("✅ 水位传感器线程已停止\n");

#ifdef ENABLE_CAMERA
        pthread_join(camera_thread, NULL);
        printf("✅ 摄像头线程已停止\n");
#endif
    }

    // 清理
    if (main_logger) {
        logger_info(main_logger, "GreenLand 系统正常退出");
        logger_destroy(main_logger);
    }

    printf("\n👋 GreenLand 系统已退出\n");
    return 0;
}

// 标准main函数入口
int main(int argc, char* argv[]) {
    return GreenLand(argc, argv);
}
