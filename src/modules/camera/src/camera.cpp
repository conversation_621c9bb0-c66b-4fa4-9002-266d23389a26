/**
 * @file camera.cpp
 * @brief 企业级摄像头模块实现
 * <AUTHOR>
 * @date 2024
 */

#include "camera.h"
#include <opencv2/opencv.hpp>
#include <iostream>
#include <string>
#include <vector>
#include <thread>
#include <mutex>
#include <atomic>
#include <chrono>
#include <ctime>
#include <iomanip>
#include <sstream>
#include <sys/stat.h>
#include <unistd.h>

using namespace cv;
using namespace std;

// 摄像头句柄结构
struct camera_handle {
    camera_config_t config;
    VideoCapture cap;
    VideoWriter writer;
    camera_state_t state;
    camera_info_t info;

    // 线程安全
    mutex mtx;
    atomic<bool> is_recording;
    atomic<bool> stop_recording;
    thread recording_thread;

    // 统计信息
    chrono::steady_clock::time_point start_time;
    chrono::steady_clock::time_point last_capture_time;

    // 日志
    logger_t logger;
    bool logging_enabled;

    // 初始化标志
    bool initialized;

    camera_handle() : state(CAMERA_STATE_IDLE), is_recording(false),
                     stop_recording(false), logger(nullptr),
                     logging_enabled(true), initialized(false) {
        memset(&config, 0, sizeof(config));
        memset(&info, 0, sizeof(info));
        start_time = chrono::steady_clock::now();
    }
};

// 全局变量
static logger_t g_logger = nullptr;
static bool g_logging_enabled = true;

// 分辨率映射表
static const struct {
    camera_resolution_t res;
    int width;
    int height;
    const char* name;
} resolution_table[] = {
    {CAMERA_RES_QVGA, 320, 240, "QVGA (320x240)"},
    {CAMERA_RES_VGA, 640, 480, "VGA (640x480)"},
    {CAMERA_RES_HD, 1280, 720, "HD (1280x720)"},
    {CAMERA_RES_FHD, 1920, 1080, "FHD (1920x1080)"},
    {CAMERA_RES_2K, 2560, 1440, "2K (2560x1440)"},
    {CAMERA_RES_4K, 3840, 2160, "4K (3840x2160)"},
    {CAMERA_RES_5MP, 2592, 1944, "5MP (2592x1944)"},
    {CAMERA_RES_CUSTOM, 0, 0, "Custom"}
};

// 旋转角度字符串映射
static const char* rotation_strings[] = {
    "0°", "90°", "180°", "270°"
};

// 图像格式扩展名映射
static const char* image_extensions[] = {
    ".jpg",  // CAMERA_FORMAT_JPEG
    ".png",  // CAMERA_FORMAT_PNG
    ".bmp",  // CAMERA_FORMAT_BMP
    ".tiff"  // CAMERA_FORMAT_TIFF
};

// 视频格式扩展名映射
static const char* video_extensions[] = {
    ".mp4",  // CAMERA_VIDEO_MP4
    ".avi",  // CAMERA_VIDEO_AVI
    ".mov",  // CAMERA_VIDEO_MOV
    ".mkv"   // CAMERA_VIDEO_MKV
};

// 视频编码器映射
static const int video_codecs[] = {
    VideoWriter::fourcc('H','2','6','4'),  // MP4
    VideoWriter::fourcc('M','J','P','G'),  // AVI
    VideoWriter::fourcc('m','p','4','v'),  // MOV
    VideoWriter::fourcc('H','2','6','4')   // MKV
};

// 错误信息映射
static const char* error_strings[] = {
    "成功",
    "参数错误",
    "初始化失败",
    "设备未找到",
    "打开设备失败",
    "配置失败",
    "拍照失败",
    "录制失败",
    "保存失败",
    "内存不足",
    "操作超时",
    "未初始化",
    "设备忙碌",
    "IO错误",
    "格式不支持",
    "权限不足"
};

// 状态字符串映射
static const char* state_strings[] = {
    "空闲",
    "预览",
    "录制",
    "拍照",
    "错误"
};

/**
 * @brief 创建目录
 */
static int create_directory(const char* path) {
    struct stat st = {0};
    if (stat(path, &st) == -1) {
        if (mkdir(path, 0755) == -1) {
            return -1;
        }
    }
    return 0;
}

/**
 * @brief 获取当前时间字符串
 */
static string get_timestamp() {
    auto now = chrono::system_clock::now();
    auto time_t = chrono::system_clock::to_time_t(now);
    auto ms = chrono::duration_cast<chrono::milliseconds>(
        now.time_since_epoch()) % 1000;

    stringstream ss;
    ss << put_time(localtime(&time_t), "%Y%m%d_%H%M%S");
    ss << "_" << setfill('0') << setw(3) << ms.count();
    return ss.str();
}

/**
 * @brief 添加时间戳水印
 */
static void add_timestamp_watermark(Mat& image) {
    auto now = chrono::system_clock::now();
    auto time_t = chrono::system_clock::to_time_t(now);

    stringstream ss;
    ss << put_time(localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    string timestamp = ss.str();

    // 添加半透明背景
    int font_face = FONT_HERSHEY_SIMPLEX;
    double font_scale = 0.8;
    int thickness = 2;
    int baseline = 0;

    Size text_size = getTextSize(timestamp, font_face, font_scale, thickness, &baseline);
    Point text_org(image.cols - text_size.width - 10, image.rows - 10);

    // 绘制背景矩形
    rectangle(image,
              Point(text_org.x - 5, text_org.y - text_size.height - 5),
              Point(text_org.x + text_size.width + 5, text_org.y + baseline + 5),
              Scalar(0, 0, 0), FILLED);

    // 绘制文字
    putText(image, timestamp, text_org, font_face, font_scale,
            Scalar(255, 255, 255), thickness);
}

/**
 * @brief 添加水印
 */
static void add_watermark(Mat& image, const string& text = "GreenLand Camera") {
    int font_face = FONT_HERSHEY_SIMPLEX;
    double font_scale = 0.6;
    int thickness = 2;

    Size text_size = getTextSize(text, font_face, font_scale, thickness, nullptr);
    Point text_org(10, text_size.height + 10);

    // 绘制半透明背景
    rectangle(image,
              Point(text_org.x - 5, text_org.y - text_size.height - 5),
              Point(text_org.x + text_size.width + 5, text_org.y + 5),
              Scalar(0, 0, 0), FILLED);

    // 绘制文字
    putText(image, text, text_org, font_face, font_scale,
            Scalar(0, 255, 255), thickness);
}

/**
 * @brief 旋转图像
 */
static void rotate_image(Mat& image, camera_rotation_t rotation) {
    if (rotation == CAMERA_ROTATE_0) {
        return; // 不需要旋转
    }

    Point2f center(image.cols / 2.0, image.rows / 2.0);
    double angle = 0;

    switch (rotation) {
        case CAMERA_ROTATE_90:
            angle = 90;
            break;
        case CAMERA_ROTATE_180:
            angle = 180;
            break;
        case CAMERA_ROTATE_270:
            angle = 270;
            break;
        default:
            return;
    }

    // 对于90度和270度旋转，需要调整图像尺寸
    if (rotation == CAMERA_ROTATE_90 || rotation == CAMERA_ROTATE_270) {
        transpose(image, image);
        if (rotation == CAMERA_ROTATE_90) {
            flip(image, image, 1); // 水平翻转
        } else {
            flip(image, image, 0); // 垂直翻转
        }
    } else if (rotation == CAMERA_ROTATE_180) {
        flip(image, image, -1); // 水平和垂直翻转
    }
}

/**
 * @brief 获取完整文件路径
 */
static string get_full_path(camera_t camera, const char* filename,
                           camera_image_format_t img_format = CAMERA_FORMAT_JPEG,
                           camera_video_format_t vid_format = CAMERA_VIDEO_MP4,
                           bool is_video = false) {
    if (!camera || !filename) return "";

    string base_path = camera->config.save_path;
    if (base_path.empty()) {
        base_path = "Media";
    }

    // 创建基础目录
    create_directory(base_path.c_str());

    // 创建子目录
    string sub_folder = is_video ? camera->config.video_folder : camera->config.photo_folder;
    if (sub_folder.empty()) {
        sub_folder = is_video ? "Videos" : "Photos";
    }

    string full_dir = base_path + "/" + sub_folder;
    create_directory(full_dir.c_str());

    string full_path = full_dir + "/" + filename;

    // 添加扩展名
    string ext = full_path.substr(full_path.find_last_of(".") + 1);
    if (ext == full_path) { // 没有扩展名
        if (is_video) {
            full_path += video_extensions[vid_format];
        } else {
            full_path += image_extensions[img_format];
        }
    }

    return full_path;
}

/**
 * @brief 录像线程函数
 */
static void recording_thread_func(camera_t camera, camera_video_params_t params) {
    if (!camera || !camera->cap.isOpened()) {
        if (camera->logger && camera->logging_enabled) {
            logger_error(camera->logger, "录像线程: 摄像头未打开");
        }
        return;
    }

    string full_path = get_full_path(camera, params.filename,
                                   CAMERA_FORMAT_JPEG, params.format, true);

    // 获取视频参数
    int width = params.width > 0 ? params.width : camera->config.width;
    int height = params.height > 0 ? params.height : camera->config.height;
    int fps = params.fps > 0 ? params.fps : camera->config.fps;

    // 初始化视频写入器
    int fourcc = video_codecs[params.format];
    camera->writer.open(full_path, fourcc, fps, Size(width, height), true);

    if (!camera->writer.isOpened()) {
        if (camera->logger && camera->logging_enabled) {
            logger_error(camera->logger, "无法创建视频文件: %s", full_path.c_str());
        }
        camera->state = CAMERA_STATE_ERROR;
        return;
    }

    if (camera->logger && camera->logging_enabled) {
        logger_info(camera->logger, "开始录像: %s (%dx%d@%dfps)",
                   full_path.c_str(), width, height, fps);
    }

    Mat frame;
    auto start_time = chrono::steady_clock::now();
    auto target_duration = chrono::seconds(params.duration_seconds);

    while (camera->is_recording && !camera->stop_recording) {
        if (!camera->cap.read(frame)) {
            if (camera->logger && camera->logging_enabled) {
                logger_warn(camera->logger, "读取帧失败");
            }
            break;
        }

        // 调整分辨率
        if (frame.cols != width || frame.rows != height) {
            resize(frame, frame, Size(width, height));
        }

        // 旋转图像
        rotate_image(frame, camera->config.rotation);

        // 添加时间戳和水印
        if (params.add_timestamp) {
            add_timestamp_watermark(frame);
        }
        if (params.add_watermark) {
            add_watermark(frame);
        }

        camera->writer.write(frame);
        camera->info.frames_captured++;

        // 检查录制时长
        if (params.duration_seconds > 0) {
            auto elapsed = chrono::steady_clock::now() - start_time;
            if (elapsed >= target_duration) {
                break;
            }
            camera->info.recording_duration =
                chrono::duration<double>(elapsed).count();
        }

        // 控制帧率
        this_thread::sleep_for(chrono::milliseconds(1000 / fps));
    }

    camera->writer.release();
    camera->is_recording = false;
    camera->state = CAMERA_STATE_IDLE;
    camera->info.videos_recorded++;

    // 获取文件大小
    struct stat st;
    if (stat(full_path.c_str(), &st) == 0) {
        camera->info.total_file_size += st.st_size;
    }

    if (camera->logger && camera->logging_enabled) {
        logger_info(camera->logger, "录像完成: %s (%.2f秒)",
                   full_path.c_str(), camera->info.recording_duration);
    }
}

// ============================================================================
// 外部接口实现
// ============================================================================

extern "C" {

/**
 * @brief 获取默认摄像头配置
 */
void camera_get_default_config(camera_config_t* config) {
    if (!config) return;

    memset(config, 0, sizeof(camera_config_t));
    config->device_id = 0;
    config->resolution = CAMERA_RES_5MP;  // 默认使用500万像素
    config->width = 2592;   // 500万像素宽度
    config->height = 1944;  // 500万像素高度
    config->fps = CAMERA_DEFAULT_FPS;
    config->brightness = 0;
    config->contrast = 0;
    config->saturation = 0;
    config->exposure = 0;
    config->auto_focus = true;
    config->auto_exposure = true;
    config->rotation = CAMERA_ROTATE_0;  // 默认不旋转
    strcpy(config->save_path, "Media");
    strcpy(config->photo_folder, "Photos");
    strcpy(config->video_folder, "Videos");
    config->image_format = CAMERA_FORMAT_JPEG;
    config->video_format = CAMERA_VIDEO_MP4;
    config->jpeg_quality = 95;
    config->enable_timestamp = true;
    config->enable_watermark = false;
}

/**
 * @brief 扫描可用的摄像头设备
 */
int camera_scan_devices(int* devices, int max_devices) {
    if (!devices || max_devices <= 0) return 0;

    int count = 0;
    for (int i = 0; i < CAMERA_MAX_DEVICES && count < max_devices; i++) {
        VideoCapture cap(i);
        if (cap.isOpened()) {
            devices[count++] = i;
            cap.release();

            if (g_logger && g_logging_enabled) {
                logger_info(g_logger, "发现摄像头设备: /dev/video%d", i);
            }
        }
    }

    if (g_logger && g_logging_enabled) {
        logger_info(g_logger, "扫描完成，找到 %d 个摄像头设备", count);
    }

    return count;
}

/**
 * @brief 创建摄像头实例
 */
camera_t camera_create(const camera_config_t* config) {
    if (!config) {
        if (g_logger && g_logging_enabled) {
            logger_error(g_logger, "创建摄像头失败: 配置参数为空");
        }
        return nullptr;
    }

    camera_t camera = new camera_handle();
    if (!camera) {
        if (g_logger && g_logging_enabled) {
            logger_error(g_logger, "创建摄像头失败: 内存分配失败");
        }
        return nullptr;
    }

    // 复制配置
    memcpy(&camera->config, config, sizeof(camera_config_t));

    // 设置分辨率
    if (config->resolution != CAMERA_RES_CUSTOM) {
        for (const auto& res : resolution_table) {
            if (res.res == config->resolution) {
                camera->config.width = res.width;
                camera->config.height = res.height;
                break;
            }
        }
    }

    // 初始化信息结构
    camera->info.device_id = config->device_id;
    camera->info.width = camera->config.width;
    camera->info.height = camera->config.height;
    camera->info.fps = camera->config.fps;
    camera->info.state = CAMERA_STATE_IDLE;
    camera->info.is_connected = false;

    // 设置日志
    camera->logger = g_logger;
    camera->logging_enabled = g_logging_enabled;

    if (camera->logger && camera->logging_enabled) {
        logger_info(camera->logger, "摄像头实例创建成功 (设备ID: %d)",
                   config->device_id);
    }

    return camera;
}

/**
 * @brief 销毁摄像头实例
 */
void camera_destroy(camera_t camera) {
    if (!camera) return;

    // 停止录制
    if (camera->is_recording) {
        camera_stop_recording(camera);
    }

    // 反初始化
    if (camera->initialized) {
        camera_deinit(camera);
    }

    if (camera->logger && camera->logging_enabled) {
        logger_info(camera->logger, "摄像头实例已销毁 (设备ID: %d)",
                   camera->config.device_id);
    }

    delete camera;
}

/**
 * @brief 初始化摄像头
 */
int camera_init(camera_t camera) {
    if (!camera) {
        return CAMERA_ERROR_PARAM;
    }

    lock_guard<mutex> lock(camera->mtx);

    if (camera->initialized) {
        if (camera->logger && camera->logging_enabled) {
            logger_warn(camera->logger, "摄像头已经初始化");
        }
        return CAMERA_OK;
    }

    // 打开摄像头设备
    camera->cap.open(camera->config.device_id);
    if (!camera->cap.isOpened()) {
        if (camera->logger && camera->logging_enabled) {
            logger_error(camera->logger, "无法打开摄像头设备 %d",
                        camera->config.device_id);
        }
        camera->state = CAMERA_STATE_ERROR;
        return CAMERA_ERROR_OPEN;
    }

    // 设置摄像头参数
    camera->cap.set(CAP_PROP_FRAME_WIDTH, camera->config.width);
    camera->cap.set(CAP_PROP_FRAME_HEIGHT, camera->config.height);
    camera->cap.set(CAP_PROP_FPS, camera->config.fps);

    // 设置图像参数
    if (camera->config.brightness != 0) {
        camera->cap.set(CAP_PROP_BRIGHTNESS, camera->config.brightness / 100.0);
    }
    if (camera->config.contrast != 0) {
        camera->cap.set(CAP_PROP_CONTRAST, camera->config.contrast / 100.0);
    }
    if (camera->config.saturation != 0) {
        camera->cap.set(CAP_PROP_SATURATION, camera->config.saturation / 100.0);
    }
    if (camera->config.exposure != 0) {
        camera->cap.set(CAP_PROP_EXPOSURE, camera->config.exposure / 100.0);
    }

    // 设置自动功能
    camera->cap.set(CAP_PROP_AUTOFOCUS, camera->config.auto_focus ? 1 : 0);
    camera->cap.set(CAP_PROP_AUTO_EXPOSURE, camera->config.auto_exposure ? 1 : 0);

    // 获取实际参数
    camera->info.width = (int)camera->cap.get(CAP_PROP_FRAME_WIDTH);
    camera->info.height = (int)camera->cap.get(CAP_PROP_FRAME_HEIGHT);
    camera->info.fps = (int)camera->cap.get(CAP_PROP_FPS);
    camera->info.is_connected = true;

    // 创建保存目录
    create_directory(camera->config.save_path);

    camera->initialized = true;
    camera->state = CAMERA_STATE_IDLE;

    if (camera->logger && camera->logging_enabled) {
        logger_info(camera->logger, "摄像头初始化成功");
        logger_info(camera->logger, "  设备ID: %d", camera->config.device_id);
        logger_info(camera->logger, "  分辨率: %dx%d", camera->info.width, camera->info.height);
        logger_info(camera->logger, "  帧率: %d fps", camera->info.fps);
        logger_info(camera->logger, "  保存路径: %s", camera->config.save_path);
    }

    return CAMERA_OK;
}

/**
 * @brief 反初始化摄像头
 */
int camera_deinit(camera_t camera) {
    if (!camera) {
        return CAMERA_ERROR_PARAM;
    }

    lock_guard<mutex> lock(camera->mtx);

    if (!camera->initialized) {
        return CAMERA_OK;
    }

    // 停止录制
    if (camera->is_recording) {
        camera->stop_recording = true;
        if (camera->recording_thread.joinable()) {
            camera->recording_thread.join();
        }
    }

    // 释放摄像头
    if (camera->cap.isOpened()) {
        camera->cap.release();
    }

    // 释放视频写入器
    if (camera->writer.isOpened()) {
        camera->writer.release();
    }

    camera->initialized = false;
    camera->state = CAMERA_STATE_IDLE;
    camera->info.is_connected = false;

    if (camera->logger && camera->logging_enabled) {
        logger_info(camera->logger, "摄像头反初始化完成");
    }

    return CAMERA_OK;
}

/**
 * @brief 开始预览
 */
int camera_start_preview(camera_t camera) {
    if (!camera) {
        return CAMERA_ERROR_PARAM;
    }

    if (!camera->initialized) {
        return CAMERA_ERROR_NOT_INIT;
    }

    if (camera->state == CAMERA_STATE_RECORDING) {
        return CAMERA_ERROR_BUSY;
    }

    camera->state = CAMERA_STATE_PREVIEW;

    if (camera->logger && camera->logging_enabled) {
        logger_info(camera->logger, "开始预览");
    }

    return CAMERA_OK;
}

/**
 * @brief 停止预览
 */
int camera_stop_preview(camera_t camera) {
    if (!camera) {
        return CAMERA_ERROR_PARAM;
    }

    if (camera->state == CAMERA_STATE_PREVIEW) {
        camera->state = CAMERA_STATE_IDLE;

        if (camera->logger && camera->logging_enabled) {
            logger_info(camera->logger, "停止预览");
        }
    }

    return CAMERA_OK;
}

/**
 * @brief 拍照
 */
int camera_take_photo(camera_t camera, const camera_photo_params_t* params) {
    if (!camera || !params) {
        return CAMERA_ERROR_PARAM;
    }

    if (!camera->initialized) {
        return CAMERA_ERROR_NOT_INIT;
    }

    if (camera->state == CAMERA_STATE_RECORDING) {
        return CAMERA_ERROR_BUSY;
    }

    lock_guard<mutex> lock(camera->mtx);

    camera->state = CAMERA_STATE_CAPTURING;

    // 读取帧
    Mat frame;
    if (!camera->cap.read(frame)) {
        if (camera->logger && camera->logging_enabled) {
            logger_error(camera->logger, "读取摄像头帧失败");
        }
        camera->state = CAMERA_STATE_ERROR;
        return CAMERA_ERROR_CAPTURE;
    }

    // 调整分辨率
    if (params->width > 0 && params->height > 0) {
        if (frame.cols != params->width || frame.rows != params->height) {
            resize(frame, frame, Size(params->width, params->height));
        }
    }

    // 旋转图像
    rotate_image(frame, camera->config.rotation);

    // 添加时间戳和水印
    if (params->add_timestamp) {
        add_timestamp_watermark(frame);
    }
    if (params->add_watermark) {
        add_watermark(frame);
    }

    // 生成文件路径
    string filename = params->filename;
    if (filename.empty()) {
        filename = "photo_" + get_timestamp();
    }

    string full_path = get_full_path(camera, filename.c_str(), params->format);

    // 设置保存参数
    vector<int> compression_params;
    switch (params->format) {
        case CAMERA_FORMAT_JPEG:
            compression_params.push_back(IMWRITE_JPEG_QUALITY);
            compression_params.push_back(params->quality);
            break;
        case CAMERA_FORMAT_PNG:
            compression_params.push_back(IMWRITE_PNG_COMPRESSION);
            compression_params.push_back(9 - (params->quality / 11)); // 转换为PNG压缩级别
            break;
        default:
            break;
    }

    // 保存图像
    bool success = imwrite(full_path, frame, compression_params);

    if (success) {
        camera->info.photos_taken++;
        camera->last_capture_time = chrono::steady_clock::now();

        // 获取文件大小
        struct stat st;
        if (stat(full_path.c_str(), &st) == 0) {
            camera->info.total_file_size += st.st_size;
        }

        if (camera->logger && camera->logging_enabled) {
            logger_info(camera->logger, "拍照成功: %s (%dx%d, 质量:%d)",
                       full_path.c_str(), frame.cols, frame.rows, params->quality);
        }
    } else {
        if (camera->logger && camera->logging_enabled) {
            logger_error(camera->logger, "保存照片失败: %s", full_path.c_str());
        }
        camera->state = CAMERA_STATE_ERROR;
        return CAMERA_ERROR_SAVE;
    }

    camera->state = CAMERA_STATE_IDLE;
    return CAMERA_OK;
}

/**
 * @brief 快速拍照
 */
int camera_quick_photo(camera_t camera, const char* filename) {
    if (!camera || !filename) {
        return CAMERA_ERROR_PARAM;
    }

    camera_photo_params_t params;
    strncpy(params.filename, filename, sizeof(params.filename) - 1);
    params.filename[sizeof(params.filename) - 1] = '\0';
    params.format = CAMERA_FORMAT_JPEG;
    params.quality = 95;
    params.add_timestamp = true;
    params.add_watermark = false;
    params.width = 0;
    params.height = 0;

    return camera_take_photo(camera, &params);
}

/**
 * @brief 开始录像
 */
int camera_start_recording(camera_t camera, const camera_video_params_t* params) {
    if (!camera || !params) {
        return CAMERA_ERROR_PARAM;
    }

    if (!camera->initialized) {
        return CAMERA_ERROR_NOT_INIT;
    }

    if (camera->is_recording) {
        return CAMERA_ERROR_BUSY;
    }

    lock_guard<mutex> lock(camera->mtx);

    camera->state = CAMERA_STATE_RECORDING;
    camera->is_recording = true;
    camera->stop_recording = false;
    camera->info.recording_duration = 0;

    // 启动录像线程
    camera_video_params_t thread_params = *params;
    camera->recording_thread = thread(recording_thread_func, camera, thread_params);

    if (camera->logger && camera->logging_enabled) {
        logger_info(camera->logger, "开始录像: %s", params->filename);
    }

    return CAMERA_OK;
}

/**
 * @brief 停止录像
 */
int camera_stop_recording(camera_t camera) {
    if (!camera) {
        return CAMERA_ERROR_PARAM;
    }

    if (!camera->is_recording) {
        return CAMERA_OK;
    }

    camera->stop_recording = true;

    // 等待录像线程结束
    if (camera->recording_thread.joinable()) {
        camera->recording_thread.join();
    }

    if (camera->logger && camera->logging_enabled) {
        logger_info(camera->logger, "录像已停止");
    }

    return CAMERA_OK;
}

/**
 * @brief 快速录像
 */
int camera_quick_video(camera_t camera, const char* filename, int duration_seconds) {
    if (!camera || !filename) {
        return CAMERA_ERROR_PARAM;
    }

    camera_video_params_t params;
    strncpy(params.filename, filename, sizeof(params.filename) - 1);
    params.filename[sizeof(params.filename) - 1] = '\0';
    params.format = CAMERA_VIDEO_MP4;
    params.duration_seconds = duration_seconds;
    params.fps = 0;
    params.width = 0;
    params.height = 0;
    params.add_timestamp = true;
    params.add_watermark = false;

    return camera_start_recording(camera, &params);
}

/**
 * @brief 获取摄像头信息
 */
int camera_get_info(camera_t camera, camera_info_t* info) {
    if (!camera || !info) {
        return CAMERA_ERROR_PARAM;
    }

    lock_guard<mutex> lock(camera->mtx);

    *info = camera->info;
    info->state = camera->state;

    return CAMERA_OK;
}

/**
 * @brief 获取摄像头状态
 */
camera_state_t camera_get_state(camera_t camera) {
    if (!camera) {
        return CAMERA_STATE_ERROR;
    }

    return camera->state;
}

/**
 * @brief 设置摄像头参数
 */
int camera_set_config(camera_t camera, const camera_config_t* config) {
    if (!camera || !config) {
        return CAMERA_ERROR_PARAM;
    }

    if (camera->is_recording) {
        return CAMERA_ERROR_BUSY;
    }

    lock_guard<mutex> lock(camera->mtx);

    // 保存旧配置
    camera_config_t old_config = camera->config;
    camera->config = *config;

    // 如果摄像头已初始化，需要重新配置
    if (camera->initialized) {
        // 设置新参数
        camera->cap.set(CAP_PROP_FRAME_WIDTH, config->width);
        camera->cap.set(CAP_PROP_FRAME_HEIGHT, config->height);
        camera->cap.set(CAP_PROP_FPS, config->fps);

        // 更新信息
        camera->info.width = (int)camera->cap.get(CAP_PROP_FRAME_WIDTH);
        camera->info.height = (int)camera->cap.get(CAP_PROP_FRAME_HEIGHT);
        camera->info.fps = (int)camera->cap.get(CAP_PROP_FPS);

        if (camera->logger && camera->logging_enabled) {
            logger_info(camera->logger, "摄像头配置已更新");
        }
    }

    return CAMERA_OK;
}

/**
 * @brief 获取摄像头配置
 */
int camera_get_config(camera_t camera, camera_config_t* config) {
    if (!camera || !config) {
        return CAMERA_ERROR_PARAM;
    }

    lock_guard<mutex> lock(camera->mtx);
    *config = camera->config;

    return CAMERA_OK;
}

/**
 * @brief 设置分辨率
 */
int camera_set_resolution(camera_t camera, int width, int height) {
    if (!camera || width <= 0 || height <= 0) {
        return CAMERA_ERROR_PARAM;
    }

    if (camera->is_recording) {
        return CAMERA_ERROR_BUSY;
    }

    lock_guard<mutex> lock(camera->mtx);

    camera->config.width = width;
    camera->config.height = height;
    camera->config.resolution = CAMERA_RES_CUSTOM;

    if (camera->initialized) {
        camera->cap.set(CAP_PROP_FRAME_WIDTH, width);
        camera->cap.set(CAP_PROP_FRAME_HEIGHT, height);

        camera->info.width = (int)camera->cap.get(CAP_PROP_FRAME_WIDTH);
        camera->info.height = (int)camera->cap.get(CAP_PROP_FRAME_HEIGHT);

        if (camera->logger && camera->logging_enabled) {
            logger_info(camera->logger, "分辨率已设置为: %dx%d",
                       camera->info.width, camera->info.height);
        }
    }

    return CAMERA_OK;
}

/**
 * @brief 设置帧率
 */
int camera_set_fps(camera_t camera, int fps) {
    if (!camera || fps <= 0) {
        return CAMERA_ERROR_PARAM;
    }

    if (camera->is_recording) {
        return CAMERA_ERROR_BUSY;
    }

    lock_guard<mutex> lock(camera->mtx);

    camera->config.fps = fps;

    if (camera->initialized) {
        camera->cap.set(CAP_PROP_FPS, fps);
        camera->info.fps = (int)camera->cap.get(CAP_PROP_FPS);

        if (camera->logger && camera->logging_enabled) {
            logger_info(camera->logger, "帧率已设置为: %d fps", camera->info.fps);
        }
    }

    return CAMERA_OK;
}

/**
 * @brief 设置图像参数
 */
int camera_set_image_params(camera_t camera, int brightness, int contrast,
                           int saturation, int exposure) {
    if (!camera) {
        return CAMERA_ERROR_PARAM;
    }

    if (camera->is_recording) {
        return CAMERA_ERROR_BUSY;
    }

    lock_guard<mutex> lock(camera->mtx);

    camera->config.brightness = brightness;
    camera->config.contrast = contrast;
    camera->config.saturation = saturation;
    camera->config.exposure = exposure;

    if (camera->initialized) {
        if (brightness != 0) {
            camera->cap.set(CAP_PROP_BRIGHTNESS, brightness / 100.0);
        }
        if (contrast != 0) {
            camera->cap.set(CAP_PROP_CONTRAST, contrast / 100.0);
        }
        if (saturation != 0) {
            camera->cap.set(CAP_PROP_SATURATION, saturation / 100.0);
        }
        if (exposure != 0) {
            camera->cap.set(CAP_PROP_EXPOSURE, exposure / 100.0);
        }

        if (camera->logger && camera->logging_enabled) {
            logger_info(camera->logger, "图像参数已更新: 亮度=%d, 对比度=%d, 饱和度=%d, 曝光=%d",
                       brightness, contrast, saturation, exposure);
        }
    }

    return CAMERA_OK;
}

/**
 * @brief 设置自动功能
 */
int camera_set_auto_features(camera_t camera, bool auto_focus, bool auto_exposure) {
    if (!camera) {
        return CAMERA_ERROR_PARAM;
    }

    if (camera->is_recording) {
        return CAMERA_ERROR_BUSY;
    }

    lock_guard<mutex> lock(camera->mtx);

    camera->config.auto_focus = auto_focus;
    camera->config.auto_exposure = auto_exposure;

    if (camera->initialized) {
        camera->cap.set(CAP_PROP_AUTOFOCUS, auto_focus ? 1 : 0);
        camera->cap.set(CAP_PROP_AUTO_EXPOSURE, auto_exposure ? 1 : 0);

        if (camera->logger && camera->logging_enabled) {
            logger_info(camera->logger, "自动功能已设置: 自动对焦=%s, 自动曝光=%s",
                       auto_focus ? "开启" : "关闭", auto_exposure ? "开启" : "关闭");
        }
    }

    return CAMERA_OK;
}

/**
 * @brief 设置图像旋转角度
 */
int camera_set_rotation(camera_t camera, camera_rotation_t rotation) {
    if (!camera) {
        return CAMERA_ERROR_PARAM;
    }

    if (rotation < CAMERA_ROTATE_0 || rotation > CAMERA_ROTATE_270) {
        return CAMERA_ERROR_PARAM;
    }

    if (camera->is_recording) {
        return CAMERA_ERROR_BUSY;
    }

    lock_guard<mutex> lock(camera->mtx);
    camera->config.rotation = rotation;

    if (camera->logger && camera->logging_enabled) {
        logger_info(camera->logger, "图像旋转角度已设置为: %s",
                   camera_rotation_to_string(rotation));
    }

    return CAMERA_OK;
}

/**
 * @brief 获取图像旋转角度
 */
camera_rotation_t camera_get_rotation(camera_t camera) {
    if (!camera) {
        return CAMERA_ROTATE_0;
    }

    return camera->config.rotation;
}

/**
 * @brief 获取统计信息
 */
int camera_get_stats(camera_t camera, char* stats, size_t size) {
    if (!camera || !stats || size == 0) {
        return CAMERA_ERROR_PARAM;
    }

    lock_guard<mutex> lock(camera->mtx);

    auto now = chrono::steady_clock::now();
    auto uptime = chrono::duration_cast<chrono::seconds>(now - camera->start_time).count();

    snprintf(stats, size,
        "摄像头统计信息:\n"
        "  设备ID: %d\n"
        "  状态: %s\n"
        "  运行时间: %ld 秒\n"
        "  分辨率: %dx%d\n"
        "  帧率: %d fps\n"
        "  已拍照片: %lu 张\n"
        "  已录视频: %lu 个\n"
        "  已捕获帧: %lu 帧\n"
        "  总文件大小: %.2f MB\n"
        "  录制时长: %.2f 秒\n"
        "  连接状态: %s",
        camera->info.device_id,
        camera_state_to_string(camera->state),
        uptime,
        camera->info.width,
        camera->info.height,
        camera->info.fps,
        camera->info.photos_taken,
        camera->info.videos_recorded,
        camera->info.frames_captured,
        camera->info.total_file_size / (1024.0 * 1024.0),
        camera->info.recording_duration,
        camera->info.is_connected ? "已连接" : "未连接"
    );

    return CAMERA_OK;
}

/**
 * @brief 重置统计信息
 */
int camera_reset_stats(camera_t camera) {
    if (!camera) {
        return CAMERA_ERROR_PARAM;
    }

    lock_guard<mutex> lock(camera->mtx);

    camera->info.frames_captured = 0;
    camera->info.photos_taken = 0;
    camera->info.videos_recorded = 0;
    camera->info.total_file_size = 0;
    camera->info.recording_duration = 0;
    camera->start_time = chrono::steady_clock::now();

    if (camera->logger && camera->logging_enabled) {
        logger_info(camera->logger, "统计信息已重置");
    }

    return CAMERA_OK;
}

/**
 * @brief 检查摄像头连接状态
 */
bool camera_is_connected(camera_t camera) {
    if (!camera) {
        return false;
    }

    return camera->info.is_connected && camera->cap.isOpened();
}

/**
 * @brief 将错误码转换为字符串
 */
const char* camera_error_to_string(camera_error_t error) {
    if (error < 0 || error >= sizeof(error_strings) / sizeof(error_strings[0])) {
        return "未知错误";
    }
    return error_strings[error];
}

/**
 * @brief 将状态转换为字符串
 */
const char* camera_state_to_string(camera_state_t state) {
    if (state < 0 || state >= sizeof(state_strings) / sizeof(state_strings[0])) {
        return "未知状态";
    }
    return state_strings[state];
}

/**
 * @brief 获取分辨率字符串
 */
const char* camera_resolution_to_string(camera_resolution_t resolution) {
    for (const auto& res : resolution_table) {
        if (res.res == resolution) {
            return res.name;
        }
    }
    return "未知分辨率";
}

/**
 * @brief 获取旋转角度字符串
 */
const char* camera_rotation_to_string(camera_rotation_t rotation) {
    if (rotation < CAMERA_ROTATE_0 || rotation > CAMERA_ROTATE_270) {
        return "未知角度";
    }
    return rotation_strings[rotation];
}

/**
 * @brief 设置日志记录器
 */
int camera_set_logger(logger_t logger) {
    g_logger = logger;
    return CAMERA_OK;
}

/**
 * @brief 获取日志记录器
 */
logger_t camera_get_logger(void) {
    return g_logger;
}

/**
 * @brief 启用/禁用日志记录
 */
int camera_enable_logging(bool enable) {
    g_logging_enabled = enable;
    return CAMERA_OK;
}

} // extern "C"