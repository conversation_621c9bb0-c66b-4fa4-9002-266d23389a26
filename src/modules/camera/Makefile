# Camera 模块 Makefile
# 企业级摄像头模块 - OpenCV USB摄像头控制

# 项目根目录
PROJECT_ROOT := ../../..

# 编译器和标志
CXX = g++
CXXFLAGS = -Wall -Wextra -O2 -fPIC -std=c++11
LDFLAGS = -shared
LDLIBS = -lpthread

# OpenCV 配置
OPENCV_CFLAGS := $(shell pkg-config --cflags opencv4 2>/dev/null || pkg-config --cflags opencv)
OPENCV_LIBS := $(shell pkg-config --libs opencv4 2>/dev/null || pkg-config --libs opencv)

# 检查 OpenCV 是否可用
ifeq ($(OPENCV_CFLAGS),)
$(error OpenCV 未找到。请安装 OpenCV 开发包: sudo apt-get install libopencv-dev)
endif

# 添加 OpenCV 标志
CXXFLAGS += $(OPENCV_CFLAGS)
LDLIBS += $(OPENCV_LIBS)

# 目录定义
SRC_DIR = src
INCLUDE_DIR = include
BUILD_DIR = $(PROJECT_ROOT)/build/modules/camera
LIB_DIR = $(PROJECT_ROOT)/lib

# 源文件和目标文件
SOURCES = $(wildcard $(SRC_DIR)/*.cpp)
OBJECTS = $(SOURCES:$(SRC_DIR)/%.cpp=$(BUILD_DIR)/%.o)
TARGET = $(LIB_DIR)/libcamera.so

# 默认目标
all: $(TARGET)

# 创建目录
$(BUILD_DIR):
	@mkdir -p $(BUILD_DIR)

$(LIB_DIR):
	@mkdir -p $(LIB_DIR)

# 编译目标文件
$(BUILD_DIR)/%.o: $(SRC_DIR)/%.cpp | $(BUILD_DIR)
	@echo "正在编译: $<"
	$(CXX) $(CXXFLAGS) -I$(INCLUDE_DIR) -I$(PROJECT_ROOT)/src/modules/logger/include -MMD -MP -c -o $@ $<

# 链接动态库
$(TARGET): $(OBJECTS) | $(LIB_DIR)
	@echo "正在链接动态库: $@"
	$(CXX) $(LDFLAGS) -o $@ $^ $(LDLIBS)
	@echo "Camera 模块构建完成"

# 清理
clean:
	@echo "清理 Camera 模块..."
	@rm -rf $(BUILD_DIR)
	@rm -f $(TARGET)

# 安装头文件
install-headers:
	@mkdir -p $(PROJECT_ROOT)/include
	@cp $(INCLUDE_DIR)/*.h $(PROJECT_ROOT)/include/

# 检查依赖
check-deps:
	@echo "检查 Camera 模块依赖:"
	@echo "OpenCV 版本: $(shell pkg-config --modversion opencv4 2>/dev/null || pkg-config --modversion opencv 2>/dev/null || echo '未找到')"
	@echo "OpenCV 编译标志: $(OPENCV_CFLAGS)"
	@echo "OpenCV 链接库: $(OPENCV_LIBS)"

# 显示帮助
help:
	@echo "Camera 模块构建选项:"
	@echo "  all            - 构建动态库"
	@echo "  clean          - 清理构建文件"
	@echo "  install-headers- 安装头文件"
	@echo "  check-deps     - 检查依赖"
	@echo "  help           - 显示此帮助"
	@echo ""
	@echo "依赖要求:"
	@echo "  - OpenCV 3.0+ (推荐 4.0+)"
	@echo "  - C++11 编译器"
	@echo "  - pthread 库"

# 包含依赖文件
-include $(OBJECTS:.o=.d)

.PHONY: all clean install-headers check-deps help
