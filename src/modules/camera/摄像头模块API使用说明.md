# 摄像头模块API使用说明

**作者**: 刘旭  
**版本**: 1.0.0  
**日期**: 2024

## 📷 模块概述

摄像头模块是一个企业级的USB摄像头控制解决方案，基于OpenCV实现，提供高质量的图像拍照和视频录制功能。

### 🎯 主要特性

- **多分辨率支持**: QVGA(320x240) 到 5MP(2592x1944)
- **图像旋转**: 支持0°、90°、180°、270°旋转
- **多格式支持**: JPEG/PNG/BMP/TIFF图片，MP4/AVI/MOV/MKV视频
- **智能功能**: 时间戳水印、自定义水印、自动对焦、自动曝光
- **文件管理**: 自动分类存储(Photos/Videos文件夹)
- **线程安全**: 支持异步录像和并发操作
- **性能监控**: 实时统计和状态监控

## 🔧 快速开始

### 1. 包含头文件

```c
#include "camera.h"
#include "logger.h"  // 可选，用于日志记录
```

### 2. 基本使用流程

```c
// 1. 获取默认配置
camera_config_t config;
camera_get_default_config(&config);

// 2. 创建摄像头实例
camera_t camera = camera_create(&config);

// 3. 初始化摄像头
int result = camera_init(camera);
if (result != CAMERA_OK) {
    printf("初始化失败: %s\n", camera_error_to_string(result));
    return -1;
}

// 4. 拍照
camera_quick_photo(camera, "my_photo");

// 5. 录像 (10秒)
camera_quick_video(camera, "my_video", 10);

// 6. 清理资源
camera_destroy(camera);
```

## 📊 API参考

### 配置管理

#### `camera_get_default_config()`
```c
void camera_get_default_config(camera_config_t* config);
```
获取默认摄像头配置，包含500万像素分辨率设置。

#### `camera_create()`
```c
camera_t camera_create(const camera_config_t* config);
```
创建摄像头实例。

#### `camera_destroy()`
```c
void camera_destroy(camera_t camera);
```
销毁摄像头实例并释放资源。

### 设备管理

#### `camera_scan_devices()`
```c
int camera_scan_devices(int* devices, int max_devices);
```
扫描可用的摄像头设备，返回设备数量。

#### `camera_init()`
```c
int camera_init(camera_t camera);
```
初始化摄像头设备。

#### `camera_deinit()`
```c
int camera_deinit(camera_t camera);
```
反初始化摄像头设备。

### 拍照功能

#### `camera_quick_photo()` - 快速拍照
```c
int camera_quick_photo(camera_t camera, const char* filename);
```
使用默认参数快速拍照。

**示例**:
```c
// 拍照并保存为 Media/Photos/photo.jpg
camera_quick_photo(camera, "photo");
```

#### `camera_take_photo()` - 高级拍照
```c
int camera_take_photo(camera_t camera, const camera_photo_params_t* params);
```
使用自定义参数拍照。

**示例**:
```c
camera_photo_params_t params = {
    .filename = "high_quality",
    .format = CAMERA_FORMAT_JPEG,
    .quality = 100,
    .add_timestamp = true,
    .add_watermark = true,
    .width = 2592,   // 500万像素宽度
    .height = 1944   // 500万像素高度
};
camera_take_photo(camera, &params);
```

### 录像功能

#### `camera_quick_video()` - 快速录像
```c
int camera_quick_video(camera_t camera, const char* filename, int duration_seconds);
```
使用默认参数录像指定时长。

**示例**:
```c
// 录制30秒视频并保存为 Media/Videos/video.mp4
camera_quick_video(camera, "video", 30);
```

#### `camera_start_recording()` - 开始录像
```c
int camera_start_recording(camera_t camera, const camera_video_params_t* params);
```
使用自定义参数开始录像。

#### `camera_stop_recording()` - 停止录像
```c
int camera_stop_recording(camera_t camera);
```
手动停止录像。

**示例**:
```c
camera_video_params_t params = {
    .filename = "surveillance",
    .format = CAMERA_VIDEO_MP4,
    .duration_seconds = 0,  // 0表示手动停止
    .fps = 30,
    .width = 1920,
    .height = 1080,
    .add_timestamp = true,
    .add_watermark = false
};

camera_start_recording(camera, &params);
// ... 执行其他操作
camera_stop_recording(camera);  // 手动停止
```

### 参数设置

#### `camera_set_resolution()` - 设置分辨率
```c
int camera_set_resolution(camera_t camera, int width, int height);
```

**示例**:
```c
// 设置为500万像素
camera_set_resolution(camera, 2592, 1944);

// 设置为1080P
camera_set_resolution(camera, 1920, 1080);
```

#### `camera_set_rotation()` - 设置旋转角度
```c
int camera_set_rotation(camera_t camera, camera_rotation_t rotation);
```

**示例**:
```c
// 旋转90度
camera_set_rotation(camera, CAMERA_ROTATE_90);

// 旋转180度
camera_set_rotation(camera, CAMERA_ROTATE_180);
```

#### `camera_set_image_params()` - 设置图像参数
```c
int camera_set_image_params(camera_t camera, int brightness, int contrast, 
                           int saturation, int exposure);
```

**示例**:
```c
// 调整图像参数 (范围: -100 到 100)
camera_set_image_params(camera, 10, 20, 0, -10);
```

### 状态查询

#### `camera_get_info()` - 获取摄像头信息
```c
int camera_get_info(camera_t camera, camera_info_t* info);
```

#### `camera_get_state()` - 获取摄像头状态
```c
camera_state_t camera_get_state(camera_t camera);
```

#### `camera_get_stats()` - 获取统计信息
```c
int camera_get_stats(camera_t camera, char* stats, size_t size);
```

## 🎛️ 配置选项

### 分辨率预设
```c
typedef enum {
    CAMERA_RES_QVGA,    // 320x240
    CAMERA_RES_VGA,     // 640x480
    CAMERA_RES_HD,      // 1280x720
    CAMERA_RES_FHD,     // 1920x1080
    CAMERA_RES_2K,      // 2560x1440
    CAMERA_RES_4K,      // 3840x2160
    CAMERA_RES_5MP,     // 2592x1944 (500万像素)
    CAMERA_RES_CUSTOM   // 自定义分辨率
} camera_resolution_t;
```

### 旋转角度
```c
typedef enum {
    CAMERA_ROTATE_0,    // 不旋转
    CAMERA_ROTATE_90,   // 顺时针90度
    CAMERA_ROTATE_180,  // 180度
    CAMERA_ROTATE_270   // 顺时针270度
} camera_rotation_t;
```

### 图像格式
```c
typedef enum {
    CAMERA_FORMAT_JPEG, // JPEG格式
    CAMERA_FORMAT_PNG,  // PNG格式
    CAMERA_FORMAT_BMP,  // BMP格式
    CAMERA_FORMAT_TIFF  // TIFF格式
} camera_image_format_t;
```

### 视频格式
```c
typedef enum {
    CAMERA_VIDEO_MP4,   // MP4格式
    CAMERA_VIDEO_AVI,   // AVI格式
    CAMERA_VIDEO_MOV,   // MOV格式
    CAMERA_VIDEO_MKV    // MKV格式
} camera_video_format_t;
```

## 📁 文件存储结构

```
Media/
├── Photos/          # 照片存储目录
│   ├── photo1.jpg
│   ├── photo2.png
│   └── ...
└── Videos/          # 视频存储目录
    ├── video1.mp4
    ├── video2.avi
    └── ...
```

## ⚠️ 注意事项

1. **设备权限**: 确保有访问摄像头设备的权限
2. **OpenCV依赖**: 需要安装OpenCV 4.x
3. **线程安全**: 录像期间不能修改某些参数
4. **内存管理**: 使用完毕后必须调用`camera_destroy()`
5. **错误处理**: 始终检查返回值并处理错误

## 🔧 编译要求

```makefile
# 需要链接的库
LDLIBS += -lcamera -llogger -lopencv_core -lopencv_imgproc -lopencv_imgcodecs -lopencv_videoio
```

## 📝 完整示例

参考文件:
- `tests/camera_test.c` - 完整功能测试
- `tests/camera_simple_test.c` - 快速验证测试  
- `examples/camera_basic_usage.c` - 基本使用示例

---

**技术支持**: 刘旭  
**更新日期**: 2024年
