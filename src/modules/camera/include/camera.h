/**
 * @file camera.h
 * @brief 企业级摄像头模块 - USB摄像头控制与图像采集
 * <AUTHOR>
 * @date 2024
 *
 * 功能特性:
 * - USB摄像头自动检测和初始化
 * - 高质量图像拍照 (JPEG/PNG格式)
 * - 视频录制 (MP4/AVI格式)
 * - 实时预览和流媒体
 * - 多分辨率支持
 * - 自动对焦和曝光控制
 * - 线程安全操作
 * - 错误处理和恢复
 * - 性能监控和统计
 */

#ifndef CAMERA_H
#define CAMERA_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>
#include "logger.h"

#ifdef __cplusplus
extern "C" {
#endif

// 摄像头配置常量
#define CAMERA_MAX_DEVICES          8       // 最大支持设备数
#define CAMERA_DEFAULT_WIDTH        1920    // 默认宽度
#define CAMERA_DEFAULT_HEIGHT       1080    // 默认高度
#define CAMERA_DEFAULT_FPS          30      // 默认帧率
#define CAMERA_MAX_FILENAME_LEN     256     // 最大文件名长度
#define CAMERA_BUFFER_SIZE          1024    // 缓冲区大小

// 摄像头分辨率预设
typedef enum {
    CAMERA_RES_QVGA = 0,    // 320x240
    CAMERA_RES_VGA,         // 640x480
    CAMERA_RES_HD,          // 1280x720
    CAMERA_RES_FHD,         // 1920x1080
    CAMERA_RES_2K,          // 2560x1440
    CAMERA_RES_4K,          // 3840x2160
    CAMERA_RES_5MP,         // 2592x1944 (500万像素)
    CAMERA_RES_CUSTOM       // 自定义分辨率
} camera_resolution_t;

// 图像旋转角度
typedef enum {
    CAMERA_ROTATE_0 = 0,    // 不旋转
    CAMERA_ROTATE_90,       // 顺时针90度
    CAMERA_ROTATE_180,      // 180度
    CAMERA_ROTATE_270       // 顺时针270度 (逆时针90度)
} camera_rotation_t;

// 图像格式
typedef enum {
    CAMERA_FORMAT_JPEG = 0, // JPEG格式
    CAMERA_FORMAT_PNG,      // PNG格式
    CAMERA_FORMAT_BMP,      // BMP格式
    CAMERA_FORMAT_TIFF      // TIFF格式
} camera_image_format_t;

// 视频格式
typedef enum {
    CAMERA_VIDEO_MP4 = 0,   // MP4格式
    CAMERA_VIDEO_AVI,       // AVI格式
    CAMERA_VIDEO_MOV,       // MOV格式
    CAMERA_VIDEO_MKV        // MKV格式
} camera_video_format_t;

// 摄像头状态
typedef enum {
    CAMERA_STATE_IDLE = 0,      // 空闲状态
    CAMERA_STATE_PREVIEW,       // 预览状态
    CAMERA_STATE_RECORDING,     // 录制状态
    CAMERA_STATE_CAPTURING,     // 拍照状态
    CAMERA_STATE_ERROR          // 错误状态
} camera_state_t;

// 错误码定义
typedef enum {
    CAMERA_OK = 0,              // 成功
    CAMERA_ERROR_PARAM,         // 参数错误
    CAMERA_ERROR_INIT,          // 初始化失败
    CAMERA_ERROR_NOT_FOUND,     // 设备未找到
    CAMERA_ERROR_OPEN,          // 打开设备失败
    CAMERA_ERROR_CONFIG,        // 配置失败
    CAMERA_ERROR_CAPTURE,       // 拍照失败
    CAMERA_ERROR_RECORD,        // 录制失败
    CAMERA_ERROR_SAVE,          // 保存失败
    CAMERA_ERROR_MEMORY,        // 内存不足
    CAMERA_ERROR_TIMEOUT,       // 操作超时
    CAMERA_ERROR_NOT_INIT,      // 未初始化
    CAMERA_ERROR_BUSY,          // 设备忙碌
    CAMERA_ERROR_IO,            // IO错误
    CAMERA_ERROR_FORMAT,        // 格式不支持
    CAMERA_ERROR_PERMISSION     // 权限不足
} camera_error_t;

// 摄像头配置结构
typedef struct {
    int device_id;                          // 设备ID (通常为0)
    camera_resolution_t resolution;         // 分辨率预设
    int width;                              // 自定义宽度
    int height;                             // 自定义高度
    int fps;                                // 帧率
    int brightness;                         // 亮度 (-100 到 100)
    int contrast;                           // 对比度 (-100 到 100)
    int saturation;                         // 饱和度 (-100 到 100)
    int exposure;                           // 曝光 (-100 到 100)
    bool auto_focus;                        // 自动对焦
    bool auto_exposure;                     // 自动曝光
    camera_rotation_t rotation;             // 图像旋转角度
    char save_path[CAMERA_MAX_FILENAME_LEN]; // 保存根路径
    char photo_folder[CAMERA_MAX_FILENAME_LEN]; // 照片文件夹名
    char video_folder[CAMERA_MAX_FILENAME_LEN]; // 视频文件夹名
    camera_image_format_t image_format;     // 图像格式
    camera_video_format_t video_format;     // 视频格式
    int jpeg_quality;                       // JPEG质量 (0-100)
    bool enable_timestamp;                  // 是否添加时间戳
    bool enable_watermark;                  // 是否添加水印
} camera_config_t;

// 摄像头信息结构
typedef struct {
    int device_id;                          // 设备ID
    char device_name[128];                  // 设备名称
    int width;                              // 当前宽度
    int height;                             // 当前高度
    int fps;                                // 当前帧率
    camera_state_t state;                   // 当前状态
    bool is_connected;                      // 是否连接
    uint64_t frames_captured;               // 已捕获帧数
    uint64_t photos_taken;                  // 已拍照数量
    uint64_t videos_recorded;               // 已录制视频数量
    double last_capture_time;               // 上次拍照时间
    double recording_duration;              // 录制时长(秒)
    size_t total_file_size;                 // 总文件大小
} camera_info_t;

// 拍照参数结构
typedef struct {
    char filename[CAMERA_MAX_FILENAME_LEN]; // 文件名
    camera_image_format_t format;           // 图像格式
    int quality;                            // 质量 (0-100)
    bool add_timestamp;                     // 添加时间戳
    bool add_watermark;                     // 添加水印
    int width;                              // 输出宽度 (0=使用默认)
    int height;                             // 输出高度 (0=使用默认)
} camera_photo_params_t;

// 录像参数结构
typedef struct {
    char filename[CAMERA_MAX_FILENAME_LEN]; // 文件名
    camera_video_format_t format;           // 视频格式
    int duration_seconds;                   // 录制时长(秒, 0=手动停止)
    int fps;                                // 帧率 (0=使用默认)
    int width;                              // 输出宽度 (0=使用默认)
    int height;                             // 输出高度 (0=使用默认)
    bool add_timestamp;                     // 添加时间戳
    bool add_watermark;                     // 添加水印
} camera_video_params_t;

// 摄像头句柄 (不透明指针)
typedef struct camera_handle* camera_t;

/**
 * @brief 获取默认摄像头配置
 * @param config 配置结构指针
 */
void camera_get_default_config(camera_config_t* config);

/**
 * @brief 扫描可用的摄像头设备
 * @param devices 设备ID数组
 * @param max_devices 最大设备数
 * @return 找到的设备数量
 */
int camera_scan_devices(int* devices, int max_devices);

/**
 * @brief 创建摄像头实例
 * @param config 摄像头配置
 * @return 摄像头句柄，失败返回NULL
 */
camera_t camera_create(const camera_config_t* config);

/**
 * @brief 销毁摄像头实例
 * @param camera 摄像头句柄
 */
void camera_destroy(camera_t camera);

/**
 * @brief 初始化摄像头
 * @param camera 摄像头句柄
 * @return 错误码
 */
int camera_init(camera_t camera);

/**
 * @brief 反初始化摄像头
 * @param camera 摄像头句柄
 * @return 错误码
 */
int camera_deinit(camera_t camera);

/**
 * @brief 开始预览
 * @param camera 摄像头句柄
 * @return 错误码
 */
int camera_start_preview(camera_t camera);

/**
 * @brief 停止预览
 * @param camera 摄像头句柄
 * @return 错误码
 */
int camera_stop_preview(camera_t camera);

/**
 * @brief 拍照
 * @param camera 摄像头句柄
 * @param params 拍照参数
 * @return 错误码
 */
int camera_take_photo(camera_t camera, const camera_photo_params_t* params);

/**
 * @brief 快速拍照 (使用默认参数)
 * @param camera 摄像头句柄
 * @param filename 文件名
 * @return 错误码
 */
int camera_quick_photo(camera_t camera, const char* filename);

/**
 * @brief 开始录像
 * @param camera 摄像头句柄
 * @param params 录像参数
 * @return 错误码
 */
int camera_start_recording(camera_t camera, const camera_video_params_t* params);

/**
 * @brief 停止录像
 * @param camera 摄像头句柄
 * @return 错误码
 */
int camera_stop_recording(camera_t camera);

/**
 * @brief 快速录像 (使用默认参数)
 * @param camera 摄像头句柄
 * @param filename 文件名
 * @param duration_seconds 录制时长(秒)
 * @return 错误码
 */
int camera_quick_video(camera_t camera, const char* filename, int duration_seconds);

/**
 * @brief 获取摄像头信息
 * @param camera 摄像头句柄
 * @param info 信息结构指针
 * @return 错误码
 */
int camera_get_info(camera_t camera, camera_info_t* info);

/**
 * @brief 获取摄像头状态
 * @param camera 摄像头句柄
 * @return 摄像头状态
 */
camera_state_t camera_get_state(camera_t camera);

/**
 * @brief 设置摄像头参数
 * @param camera 摄像头句柄
 * @param config 新配置
 * @return 错误码
 */
int camera_set_config(camera_t camera, const camera_config_t* config);

/**
 * @brief 获取摄像头配置
 * @param camera 摄像头句柄
 * @param config 配置结构指针
 * @return 错误码
 */
int camera_get_config(camera_t camera, camera_config_t* config);

/**
 * @brief 设置分辨率
 * @param camera 摄像头句柄
 * @param width 宽度
 * @param height 高度
 * @return 错误码
 */
int camera_set_resolution(camera_t camera, int width, int height);

/**
 * @brief 设置帧率
 * @param camera 摄像头句柄
 * @param fps 帧率
 * @return 错误码
 */
int camera_set_fps(camera_t camera, int fps);

/**
 * @brief 设置图像参数
 * @param camera 摄像头句柄
 * @param brightness 亮度 (-100 到 100)
 * @param contrast 对比度 (-100 到 100)
 * @param saturation 饱和度 (-100 到 100)
 * @param exposure 曝光 (-100 到 100)
 * @return 错误码
 */
int camera_set_image_params(camera_t camera, int brightness, int contrast,
                           int saturation, int exposure);

/**
 * @brief 设置自动功能
 * @param camera 摄像头句柄
 * @param auto_focus 自动对焦
 * @param auto_exposure 自动曝光
 * @return 错误码
 */
int camera_set_auto_features(camera_t camera, bool auto_focus, bool auto_exposure);

/**
 * @brief 设置图像旋转角度
 * @param camera 摄像头句柄
 * @param rotation 旋转角度
 * @return 错误码
 */
int camera_set_rotation(camera_t camera, camera_rotation_t rotation);

/**
 * @brief 获取图像旋转角度
 * @param camera 摄像头句柄
 * @return 当前旋转角度
 */
camera_rotation_t camera_get_rotation(camera_t camera);

/**
 * @brief 获取统计信息
 * @param camera 摄像头句柄
 * @param stats 统计信息缓冲区
 * @param size 缓冲区大小
 * @return 错误码
 */
int camera_get_stats(camera_t camera, char* stats, size_t size);

/**
 * @brief 重置统计信息
 * @param camera 摄像头句柄
 * @return 错误码
 */
int camera_reset_stats(camera_t camera);

/**
 * @brief 检查摄像头连接状态
 * @param camera 摄像头句柄
 * @return true=已连接, false=未连接
 */
bool camera_is_connected(camera_t camera);

/**
 * @brief 将错误码转换为字符串
 * @param error 错误码
 * @return 错误描述字符串
 */
const char* camera_error_to_string(camera_error_t error);

/**
 * @brief 将状态转换为字符串
 * @param state 状态
 * @return 状态描述字符串
 */
const char* camera_state_to_string(camera_state_t state);

/**
 * @brief 获取分辨率字符串
 * @param resolution 分辨率枚举
 * @return 分辨率描述字符串
 */
const char* camera_resolution_to_string(camera_resolution_t resolution);

/**
 * @brief 获取旋转角度字符串
 * @param rotation 旋转角度枚举
 * @return 旋转角度描述字符串
 */
const char* camera_rotation_to_string(camera_rotation_t rotation);

// 日志相关接口
int camera_set_logger(logger_t logger);
logger_t camera_get_logger(void);
int camera_enable_logging(bool enable);

// 便利宏定义
#define CAMERA_PHOTO_DEFAULT(filename) \
    { .filename = filename, .format = CAMERA_FORMAT_JPEG, .quality = 95, \
      .add_timestamp = true, .add_watermark = false, .width = 0, .height = 0 }

#define CAMERA_VIDEO_DEFAULT(filename, duration) \
    { .filename = filename, .format = CAMERA_VIDEO_MP4, .duration_seconds = duration, \
      .fps = 0, .width = 0, .height = 0, .add_timestamp = true, .add_watermark = false }

#ifdef __cplusplus
}
#endif

#endif // CAMERA_H
