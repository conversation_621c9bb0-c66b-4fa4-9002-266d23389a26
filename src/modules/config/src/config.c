/**
 * @file config.c
 * @brief GreenLand 统一配置管理系统实现
 * <AUTHOR>
 * @date 2024
 */

#define _DEFAULT_SOURCE
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <sys/stat.h>
#include <unistd.h>
#include "config.h"

// 配置管理器结构
struct config_manager {
    greenland_config_t config;
    bool initialized;
    char config_file_path[512];
};

// 全局配置管理器实例
static config_manager_t g_config_manager = NULL;

/**
 * @brief 创建目录
 */
static int create_directory(const char* path) {
    struct stat st = {0};
    if (stat(path, &st) == -1) {
        if (mkdir(path, 0755) != 0) {
            return CONFIG_ERROR_FILE;
        }
    }
    return CONFIG_OK;
}

/**
 * @brief 解析配置行
 */
static int parse_config_line(config_manager_t manager, const char* line) {
    if (!manager || !line) return CONFIG_ERROR_PARAM;

    // 跳过注释和空行
    if (line[0] == '#' || line[0] == '\n' || line[0] == '\0') {
        return CONFIG_OK;
    }

    char section[64] = {0};
    char key[64] = {0};
    char value[256] = {0};

    // 解析段名 [section]
    if (sscanf(line, "[%63[^]]]", section) == 1) {
        return CONFIG_OK;
    }

    // 解析键值对 key=value
    if (sscanf(line, "%63[^=]=%255[^\n]", key, value) == 2) {
        // 去除前后空格
        char* key_trim = key;
        char* value_trim = value;
        while (*key_trim == ' ') key_trim++;
        while (*value_trim == ' ') value_trim++;

        // 系统配置
        if (strcmp(key_trim, "system_name") == 0) {
            strncpy(manager->config.system_name, value_trim, sizeof(manager->config.system_name) - 1);
        } else if (strcmp(key_trim, "version") == 0) {
            strncpy(manager->config.version, value_trim, sizeof(manager->config.version) - 1);
        } else if (strcmp(key_trim, "author") == 0) {
            strncpy(manager->config.author, value_trim, sizeof(manager->config.author) - 1);
        } else if (strcmp(key_trim, "debug_mode") == 0) {
            manager->config.debug_mode = (strcmp(value_trim, "true") == 0);
        }
        // 日志配置
        else if (strcmp(key_trim, "log_dir") == 0) {
            strncpy(manager->config.logging.log_dir, value_trim, sizeof(manager->config.logging.log_dir) - 1);
        } else if (strcmp(key_trim, "log_level") == 0) {
            manager->config.logging.log_level = atoi(value_trim);
        } else if (strcmp(key_trim, "enable_console") == 0) {
            manager->config.logging.enable_console = (strcmp(value_trim, "true") == 0);
        } else if (strcmp(key_trim, "enable_file") == 0) {
            manager->config.logging.enable_file = (strcmp(value_trim, "true") == 0);
        }
        // 传感器配置
        else if (strcmp(key_trim, "water_sensor_enabled") == 0) {
            manager->config.sensors.water_sensor.enabled = (strcmp(value_trim, "true") == 0);
        } else if (strcmp(key_trim, "water_sensor_trig_pin") == 0) {
            manager->config.sensors.water_sensor.trig_pin = atoi(value_trim);
        } else if (strcmp(key_trim, "water_sensor_echo_pin") == 0) {
            manager->config.sensors.water_sensor.echo_pin = atoi(value_trim);
        } else if (strcmp(key_trim, "water_sensor_max_level") == 0) {
            manager->config.sensors.water_sensor.max_water_level = atof(value_trim);
        } else if (strcmp(key_trim, "water_sensor_interval") == 0) {
            manager->config.sensors.water_sensor.read_interval = atoi(value_trim);
        }
        // 摄像头配置
        else if (strcmp(key_trim, "camera_enabled") == 0) {
            manager->config.camera.enabled = (strcmp(value_trim, "true") == 0);
        } else if (strcmp(key_trim, "camera_device_id") == 0) {
            manager->config.camera.device_id = atoi(value_trim);
        } else if (strcmp(key_trim, "camera_width") == 0) {
            manager->config.camera.width = atoi(value_trim);
        } else if (strcmp(key_trim, "camera_height") == 0) {
            manager->config.camera.height = atoi(value_trim);
        } else if (strcmp(key_trim, "camera_photo_interval") == 0) {
            manager->config.camera.photo_interval = atoi(value_trim);
        }
    }

    return CONFIG_OK;
}

/**
 * @brief 创建配置管理器
 */
config_manager_t config_manager_create(void) {
    if (g_config_manager) {
        return g_config_manager;
    }

    config_manager_t manager = malloc(sizeof(struct config_manager));
    if (!manager) {
        return NULL;
    }

    memset(manager, 0, sizeof(struct config_manager));

    // 设置默认配置文件路径
    strncpy(manager->config_file_path, CONFIG_FILE_PATH, sizeof(manager->config_file_path) - 1);

    // 获取默认配置
    config_get_default(&manager->config);

    manager->initialized = true;
    g_config_manager = manager;

    return manager;
}

/**
 * @brief 销毁配置管理器
 */
void config_manager_destroy(config_manager_t manager) {
    if (manager) {
        manager->initialized = false;
        free(manager);
        if (g_config_manager == manager) {
            g_config_manager = NULL;
        }
    }
}

/**
 * @brief 加载配置文件
 */
int config_load(config_manager_t manager, const char* config_file) {
    if (!manager) return CONFIG_ERROR_PARAM;

    const char* file_path = config_file ? config_file : manager->config_file_path;

    FILE* fp = fopen(file_path, "r");
    if (!fp) {
        // 如果文件不存在，创建默认配置文件
        return config_create_default_file(file_path);
    }

    char line[512];
    while (fgets(line, sizeof(line), fp)) {
        parse_config_line(manager, line);
    }

    fclose(fp);
    return CONFIG_OK;
}

/**
 * @brief 获取默认配置
 */
void config_get_default(greenland_config_t* config) {
    if (!config) return;

    memset(config, 0, sizeof(greenland_config_t));

    // 系统配置
    strncpy(config->system_name, "GreenLand", sizeof(config->system_name) - 1);
    strncpy(config->version, "1.1.0", sizeof(config->version) - 1);
    strncpy(config->author, "Alex", sizeof(config->author) - 1);
    config->debug_mode = false;

    // 日志配置
    strncpy(config->logging.log_dir, "Log", sizeof(config->logging.log_dir) - 1);
    config->logging.log_level = 1; // INFO
    config->logging.enable_console = true;
    config->logging.enable_file = true;
    config->logging.max_file_size = 10 * 1024 * 1024; // 10MB
    config->logging.max_file_count = 5;

    // 水位传感器配置
    config->sensors.water_sensor.enabled = true;
    config->sensors.water_sensor.trig_pin = 22;
    config->sensors.water_sensor.echo_pin = 23;
    config->sensors.water_sensor.max_water_level = 35.0;
    config->sensors.water_sensor.read_interval = 10;

    // 温湿度传感器配置
    config->sensors.temperature_sensor.enabled = true;
    strncpy(config->sensors.temperature_sensor.i2c_bus, "/dev/i2c-2",
            sizeof(config->sensors.temperature_sensor.i2c_bus) - 1);
    config->sensors.temperature_sensor.i2c_addr = 0x38;
    config->sensors.temperature_sensor.read_interval = 10;

    // 光照传感器配置
    config->sensors.light_sensor.enabled = true;
    strncpy(config->sensors.light_sensor.i2c_bus, "/dev/i2c-2",
            sizeof(config->sensors.light_sensor.i2c_bus) - 1);
    config->sensors.light_sensor.i2c_addr = 0x23;
    config->sensors.light_sensor.read_interval = 15;

    // 摄像头配置
    config->camera.enabled = true;
    config->camera.device_id = 0;
    config->camera.width = 2592;  // 500万像素
    config->camera.height = 1944;
    config->camera.fps = 30;
    strncpy(config->camera.save_path, "media", sizeof(config->camera.save_path) - 1);
    config->camera.photo_interval = 60;
    config->camera.rotation_angle = 0;

    // 监控配置
    config->monitoring.status_interval = 30;
    config->monitoring.auto_start = true;
    config->monitoring.enable_alerts = true;

    // 网络配置
    config->network.enabled = false;
    config->network.port = 8080;
    strncpy(config->network.bind_address, "0.0.0.0", sizeof(config->network.bind_address) - 1);
}

/**
 * @brief 获取配置
 */
const greenland_config_t* config_get(config_manager_t manager) {
    if (!manager || !manager->initialized) {
        return NULL;
    }
    return &manager->config;
}

/**
 * @brief 保存配置文件
 */
int config_save(config_manager_t manager, const char* config_file) {
    if (!manager) return CONFIG_ERROR_PARAM;

    const char* file_path = config_file ? config_file : manager->config_file_path;

    // 创建配置目录
    create_directory(CONFIG_DIR_PATH);

    FILE* fp = fopen(file_path, "w");
    if (!fp) {
        return CONFIG_ERROR_FILE;
    }

    const greenland_config_t* config = &manager->config;

    fprintf(fp, "# GreenLand 智能农业监控系统配置文件\n");
    fprintf(fp, "# 作者: %s\n", config->author);
    fprintf(fp, "# 版本: %s\n\n", config->version);

    // 系统配置
    fprintf(fp, "[system]\n");
    fprintf(fp, "system_name=%s\n", config->system_name);
    fprintf(fp, "version=%s\n", config->version);
    fprintf(fp, "author=%s\n", config->author);
    fprintf(fp, "debug_mode=%s\n\n", config->debug_mode ? "true" : "false");

    // 日志配置
    fprintf(fp, "[logging]\n");
    fprintf(fp, "log_dir=%s\n", config->logging.log_dir);
    fprintf(fp, "log_level=%d\n", config->logging.log_level);
    fprintf(fp, "enable_console=%s\n", config->logging.enable_console ? "true" : "false");
    fprintf(fp, "enable_file=%s\n", config->logging.enable_file ? "true" : "false");
    fprintf(fp, "max_file_size=%zu\n", config->logging.max_file_size);
    fprintf(fp, "max_file_count=%d\n\n", config->logging.max_file_count);

    // 传感器配置
    fprintf(fp, "[sensors]\n");
    fprintf(fp, "water_sensor_enabled=%s\n", config->sensors.water_sensor.enabled ? "true" : "false");
    fprintf(fp, "water_sensor_trig_pin=%d\n", config->sensors.water_sensor.trig_pin);
    fprintf(fp, "water_sensor_echo_pin=%d\n", config->sensors.water_sensor.echo_pin);
    fprintf(fp, "water_sensor_max_level=%.2f\n", config->sensors.water_sensor.max_water_level);
    fprintf(fp, "water_sensor_interval=%d\n", config->sensors.water_sensor.read_interval);

    fprintf(fp, "temperature_sensor_enabled=%s\n", config->sensors.temperature_sensor.enabled ? "true" : "false");
    fprintf(fp, "temperature_sensor_i2c_bus=%s\n", config->sensors.temperature_sensor.i2c_bus);
    fprintf(fp, "temperature_sensor_i2c_addr=0x%02X\n", config->sensors.temperature_sensor.i2c_addr);
    fprintf(fp, "temperature_sensor_interval=%d\n", config->sensors.temperature_sensor.read_interval);

    fprintf(fp, "light_sensor_enabled=%s\n", config->sensors.light_sensor.enabled ? "true" : "false");
    fprintf(fp, "light_sensor_i2c_bus=%s\n", config->sensors.light_sensor.i2c_bus);
    fprintf(fp, "light_sensor_i2c_addr=0x%02X\n", config->sensors.light_sensor.i2c_addr);
    fprintf(fp, "light_sensor_interval=%d\n\n", config->sensors.light_sensor.read_interval);

    // 摄像头配置
    fprintf(fp, "[camera]\n");
    fprintf(fp, "camera_enabled=%s\n", config->camera.enabled ? "true" : "false");
    fprintf(fp, "camera_device_id=%d\n", config->camera.device_id);
    fprintf(fp, "camera_width=%d\n", config->camera.width);
    fprintf(fp, "camera_height=%d\n", config->camera.height);
    fprintf(fp, "camera_fps=%d\n", config->camera.fps);
    fprintf(fp, "camera_save_path=%s\n", config->camera.save_path);
    fprintf(fp, "camera_photo_interval=%d\n", config->camera.photo_interval);
    fprintf(fp, "camera_rotation_angle=%d\n\n", config->camera.rotation_angle);

    // 监控配置
    fprintf(fp, "[monitoring]\n");
    fprintf(fp, "status_interval=%d\n", config->monitoring.status_interval);
    fprintf(fp, "auto_start=%s\n", config->monitoring.auto_start ? "true" : "false");
    fprintf(fp, "enable_alerts=%s\n\n", config->monitoring.enable_alerts ? "true" : "false");

    // 网络配置
    fprintf(fp, "[network]\n");
    fprintf(fp, "network_enabled=%s\n", config->network.enabled ? "true" : "false");
    fprintf(fp, "network_port=%d\n", config->network.port);
    fprintf(fp, "network_bind_address=%s\n", config->network.bind_address);

    fclose(fp);
    return CONFIG_OK;
}

/**
 * @brief 创建默认配置文件
 */
int config_create_default_file(const char* config_file) {
    config_manager_t temp_manager = malloc(sizeof(struct config_manager));
    if (!temp_manager) {
        return CONFIG_ERROR_MEMORY;
    }

    memset(temp_manager, 0, sizeof(struct config_manager));
    config_get_default(&temp_manager->config);

    int result = config_save(temp_manager, config_file);
    free(temp_manager);

    return result;
}

/**
 * @brief 验证配置有效性
 */
int config_validate(const greenland_config_t* config) {
    if (!config) return CONFIG_ERROR_PARAM;

    // 验证系统配置
    if (strlen(config->system_name) == 0) return CONFIG_ERROR_PARAM;
    if (strlen(config->version) == 0) return CONFIG_ERROR_PARAM;

    // 验证传感器配置
    if (config->sensors.water_sensor.enabled) {
        if (config->sensors.water_sensor.trig_pin < 0 || config->sensors.water_sensor.echo_pin < 0) {
            return CONFIG_ERROR_PARAM;
        }
        if (config->sensors.water_sensor.max_water_level <= 0) {
            return CONFIG_ERROR_PARAM;
        }
    }

    // 验证摄像头配置
    if (config->camera.enabled) {
        if (config->camera.width <= 0 || config->camera.height <= 0) {
            return CONFIG_ERROR_PARAM;
        }
        if (config->camera.photo_interval <= 0) {
            return CONFIG_ERROR_PARAM;
        }
    }

    return CONFIG_OK;
}

/**
 * @brief 设置配置
 */
int config_set(config_manager_t manager, const greenland_config_t* config) {
    if (!manager || !config) return CONFIG_ERROR_PARAM;

    int result = config_validate(config);
    if (result != CONFIG_OK) return result;

    memcpy(&manager->config, config, sizeof(greenland_config_t));
    return CONFIG_OK;
}

/**
 * @brief 错误码转字符串
 */
const char* config_error_to_string(config_error_t error) {
    switch (error) {
        case CONFIG_OK: return "成功";
        case CONFIG_ERROR_PARAM: return "参数错误";
        case CONFIG_ERROR_FILE: return "文件错误";
        case CONFIG_ERROR_PARSE: return "解析错误";
        case CONFIG_ERROR_MEMORY: return "内存错误";
        case CONFIG_ERROR_NOT_FOUND: return "未找到";
        default: return "未知错误";
    }
}
