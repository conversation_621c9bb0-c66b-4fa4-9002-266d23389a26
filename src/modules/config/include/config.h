/**
 * @file config.h
 * @brief GreenLand 统一配置管理系统
 * <AUTHOR>
 * @date 2024
 */

#ifndef CONFIG_H
#define CONFIG_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

// 配置文件路径
#define CONFIG_FILE_PATH "config/greenland.conf"
#define CONFIG_DIR_PATH "config"

// 配置错误码
typedef enum {
    CONFIG_OK = 0,
    CONFIG_ERROR_PARAM,
    CONFIG_ERROR_FILE,
    CONFIG_ERROR_PARSE,
    CONFIG_ERROR_MEMORY,
    CONFIG_ERROR_NOT_FOUND
} config_error_t;

// 系统配置结构
typedef struct {
    // 系统基本配置
    char system_name[64];
    char version[16];
    char author[64];
    bool debug_mode;
    
    // 日志配置
    struct {
        char log_dir[256];
        int log_level;
        bool enable_console;
        bool enable_file;
        size_t max_file_size;
        int max_file_count;
    } logging;
    
    // 传感器配置
    struct {
        // 水位传感器
        struct {
            bool enabled;
            int trig_pin;
            int echo_pin;
            float max_water_level;
            int read_interval;
        } water_sensor;
        
        // 温湿度传感器
        struct {
            bool enabled;
            char i2c_bus[32];
            uint8_t i2c_addr;
            int read_interval;
        } temperature_sensor;
        
        // 光照传感器
        struct {
            bool enabled;
            char i2c_bus[32];
            uint8_t i2c_addr;
            int read_interval;
        } light_sensor;
    } sensors;
    
    // 摄像头配置
    struct {
        bool enabled;
        int device_id;
        int width;
        int height;
        int fps;
        char save_path[256];
        int photo_interval;
        int rotation_angle;
    } camera;
    
    // 监控配置
    struct {
        int status_interval;
        bool auto_start;
        bool enable_alerts;
    } monitoring;
    
    // 网络配置 (预留)
    struct {
        bool enabled;
        int port;
        char bind_address[64];
    } network;
    
} greenland_config_t;

// 配置管理句柄
typedef struct config_manager* config_manager_t;

/**
 * @brief 创建配置管理器
 * @return 配置管理器句柄，失败返回NULL
 */
config_manager_t config_manager_create(void);

/**
 * @brief 销毁配置管理器
 * @param manager 配置管理器句柄
 */
void config_manager_destroy(config_manager_t manager);

/**
 * @brief 加载配置文件
 * @param manager 配置管理器句柄
 * @param config_file 配置文件路径，NULL使用默认路径
 * @return 错误码
 */
int config_load(config_manager_t manager, const char* config_file);

/**
 * @brief 保存配置文件
 * @param manager 配置管理器句柄
 * @param config_file 配置文件路径，NULL使用默认路径
 * @return 错误码
 */
int config_save(config_manager_t manager, const char* config_file);

/**
 * @brief 获取配置
 * @param manager 配置管理器句柄
 * @return 配置结构指针，失败返回NULL
 */
const greenland_config_t* config_get(config_manager_t manager);

/**
 * @brief 设置配置
 * @param manager 配置管理器句柄
 * @param config 配置结构指针
 * @return 错误码
 */
int config_set(config_manager_t manager, const greenland_config_t* config);

/**
 * @brief 获取默认配置
 * @param config 配置结构指针
 */
void config_get_default(greenland_config_t* config);

/**
 * @brief 验证配置有效性
 * @param config 配置结构指针
 * @return 错误码
 */
int config_validate(const greenland_config_t* config);

/**
 * @brief 创建默认配置文件
 * @param config_file 配置文件路径，NULL使用默认路径
 * @return 错误码
 */
int config_create_default_file(const char* config_file);

/**
 * @brief 获取配置项字符串值
 * @param manager 配置管理器句柄
 * @param section 配置段名
 * @param key 配置键名
 * @param default_value 默认值
 * @return 配置值字符串
 */
const char* config_get_string(config_manager_t manager, const char* section, 
                             const char* key, const char* default_value);

/**
 * @brief 获取配置项整数值
 * @param manager 配置管理器句柄
 * @param section 配置段名
 * @param key 配置键名
 * @param default_value 默认值
 * @return 配置值
 */
int config_get_int(config_manager_t manager, const char* section, 
                   const char* key, int default_value);

/**
 * @brief 获取配置项布尔值
 * @param manager 配置管理器句柄
 * @param section 配置段名
 * @param key 配置键名
 * @param default_value 默认值
 * @return 配置值
 */
bool config_get_bool(config_manager_t manager, const char* section, 
                     const char* key, bool default_value);

/**
 * @brief 获取配置项浮点值
 * @param manager 配置管理器句柄
 * @param section 配置段名
 * @param key 配置键名
 * @param default_value 默认值
 * @return 配置值
 */
float config_get_float(config_manager_t manager, const char* section, 
                       const char* key, float default_value);

/**
 * @brief 错误码转字符串
 * @param error 错误码
 * @return 错误描述字符串
 */
const char* config_error_to_string(config_error_t error);

#ifdef __cplusplus
}
#endif

#endif // CONFIG_H
