/**
 * @file error_handler.h
 * @brief GreenLand 统一错误处理和恢复模块
 * <AUTHOR>
 * @date 2024
 */

#ifndef ERROR_HANDLER_H
#define ERROR_HANDLER_H

#include <stdint.h>
#include <stdbool.h>
#include <time.h>

#ifdef __cplusplus
extern "C" {
#endif

// 错误级别
typedef enum {
    ERROR_LEVEL_DEBUG = 0,
    ERROR_LEVEL_INFO,
    ERROR_LEVEL_WARNING,
    ERROR_LEVEL_ERROR,
    ERROR_LEVEL_CRITICAL,
    ERROR_LEVEL_FATAL
} error_level_t;

// 错误类型
typedef enum {
    ERROR_TYPE_SYSTEM = 0,
    ERROR_TYPE_HARDWARE,
    ERROR_TYPE_SENSOR,
    ERROR_TYPE_CAMERA,
    ERROR_TYPE_NETWORK,
    ERROR_TYPE_CONFIG,
    ERROR_TYPE_MEMORY,
    ERROR_TYPE_FILE_IO,
    ERROR_TYPE_THREAD,
    ERROR_TYPE_USER
} error_type_t;

// 错误恢复策略
typedef enum {
    RECOVERY_NONE = 0,        // 不恢复
    RECOVERY_RETRY,           // 重试
    RECOVERY_RESTART_MODULE,  // 重启模块
    RECOVERY_RESTART_SYSTEM,  // 重启系统
    RECOVERY_CUSTOM          // 自定义恢复
} recovery_strategy_t;

// 错误信息结构
typedef struct {
    uint32_t error_id;              // 错误ID
    error_level_t level;            // 错误级别
    error_type_t type;              // 错误类型
    int error_code;                 // 错误码
    char module_name[64];           // 模块名称
    char function_name[64];         // 函数名称
    char description[256];          // 错误描述
    char details[512];              // 详细信息
    time_t timestamp;               // 时间戳
    int line_number;                // 行号
    char file_name[128];            // 文件名
    recovery_strategy_t recovery;   // 恢复策略
    int retry_count;                // 重试次数
    int max_retries;                // 最大重试次数
    bool resolved;                  // 是否已解决
} error_info_t;

// 错误处理器句柄
typedef struct error_handler* error_handler_t;

// 错误回调函数类型
typedef void (*error_callback_t)(const error_info_t* error, void* user_data);

// 恢复回调函数类型
typedef bool (*recovery_callback_t)(const error_info_t* error, void* user_data);

// 错误处理配置
typedef struct {
    bool enable_logging;            // 启用日志记录
    char log_file[256];            // 日志文件路径
    bool enable_console_output;     // 启用控制台输出
    bool enable_auto_recovery;      // 启用自动恢复
    int max_error_history;          // 最大错误历史记录数
    bool enable_statistics;         // 启用统计功能
} error_handler_config_t;

/**
 * @brief 创建错误处理器
 * @param config 配置参数，NULL使用默认配置
 * @return 错误处理器句柄，失败返回NULL
 */
error_handler_t error_handler_create(const error_handler_config_t* config);

/**
 * @brief 销毁错误处理器
 * @param handler 错误处理器句柄
 */
void error_handler_destroy(error_handler_t handler);

/**
 * @brief 报告错误
 * @param handler 错误处理器句柄
 * @param level 错误级别
 * @param type 错误类型
 * @param error_code 错误码
 * @param module_name 模块名称
 * @param function_name 函数名称
 * @param file_name 文件名
 * @param line_number 行号
 * @param description 错误描述
 * @param details 详细信息
 * @return 错误ID
 */
uint32_t error_handler_report(error_handler_t handler, error_level_t level, error_type_t type,
                             int error_code, const char* module_name, const char* function_name,
                             const char* file_name, int line_number, const char* description,
                             const char* details);

/**
 * @brief 报告错误（格式化版本）
 */
uint32_t error_handler_reportf(error_handler_t handler, error_level_t level, error_type_t type,
                              int error_code, const char* module_name, const char* function_name,
                              const char* file_name, int line_number, const char* description,
                              const char* format, ...);

/**
 * @brief 设置错误恢复策略
 * @param handler 错误处理器句柄
 * @param error_id 错误ID
 * @param strategy 恢复策略
 * @param max_retries 最大重试次数
 * @return 是否成功
 */
bool error_handler_set_recovery(error_handler_t handler, uint32_t error_id, 
                               recovery_strategy_t strategy, int max_retries);

/**
 * @brief 尝试恢复错误
 * @param handler 错误处理器句柄
 * @param error_id 错误ID
 * @return 是否成功恢复
 */
bool error_handler_recover(error_handler_t handler, uint32_t error_id);

/**
 * @brief 标记错误为已解决
 * @param handler 错误处理器句柄
 * @param error_id 错误ID
 * @return 是否成功
 */
bool error_handler_resolve(error_handler_t handler, uint32_t error_id);

/**
 * @brief 获取错误信息
 * @param handler 错误处理器句柄
 * @param error_id 错误ID
 * @return 错误信息指针，失败返回NULL
 */
const error_info_t* error_handler_get_error(error_handler_t handler, uint32_t error_id);

/**
 * @brief 获取错误历史
 * @param handler 错误处理器句柄
 * @param errors 错误信息数组
 * @param max_count 最大数量
 * @return 实际返回的错误数量
 */
int error_handler_get_history(error_handler_t handler, error_info_t* errors, int max_count);

/**
 * @brief 清除错误历史
 * @param handler 错误处理器句柄
 * @return 是否成功
 */
bool error_handler_clear_history(error_handler_t handler);

/**
 * @brief 设置错误回调函数
 * @param handler 错误处理器句柄
 * @param callback 回调函数
 * @param user_data 用户数据
 * @return 是否成功
 */
bool error_handler_set_callback(error_handler_t handler, error_callback_t callback, void* user_data);

/**
 * @brief 设置恢复回调函数
 * @param handler 错误处理器句柄
 * @param callback 回调函数
 * @param user_data 用户数据
 * @return 是否成功
 */
bool error_handler_set_recovery_callback(error_handler_t handler, recovery_callback_t callback, void* user_data);

/**
 * @brief 获取错误统计信息
 * @param handler 错误处理器句柄
 * @param total_errors 总错误数
 * @param resolved_errors 已解决错误数
 * @param critical_errors 严重错误数
 * @return 是否成功
 */
bool error_handler_get_statistics(error_handler_t handler, int* total_errors, 
                                 int* resolved_errors, int* critical_errors);

/**
 * @brief 获取默认配置
 * @param config 配置结构指针
 */
void error_handler_get_default_config(error_handler_config_t* config);

/**
 * @brief 错误级别转字符串
 * @param level 错误级别
 * @return 级别字符串
 */
const char* error_level_to_string(error_level_t level);

/**
 * @brief 错误类型转字符串
 * @param type 错误类型
 * @return 类型字符串
 */
const char* error_type_to_string(error_type_t type);

/**
 * @brief 恢复策略转字符串
 * @param strategy 恢复策略
 * @return 策略字符串
 */
const char* recovery_strategy_to_string(recovery_strategy_t strategy);

// 便利宏定义
#define ERROR_REPORT(handler, level, type, code, desc) \
    error_handler_report(handler, level, type, code, __func__, __func__, __FILE__, __LINE__, desc, "")

#define ERROR_REPORTF(handler, level, type, code, desc, ...) \
    error_handler_reportf(handler, level, type, code, __func__, __func__, __FILE__, __LINE__, desc, __VA_ARGS__)

#define ERROR_CRITICAL(handler, type, code, desc) \
    ERROR_REPORT(handler, ERROR_LEVEL_CRITICAL, type, code, desc)

#define ERROR_ERROR(handler, type, code, desc) \
    ERROR_REPORT(handler, ERROR_LEVEL_ERROR, type, code, desc)

#define ERROR_WARNING(handler, type, code, desc) \
    ERROR_REPORT(handler, ERROR_LEVEL_WARNING, type, code, desc)

#define ERROR_INFO(handler, type, code, desc) \
    ERROR_REPORT(handler, ERROR_LEVEL_INFO, type, code, desc)

#ifdef __cplusplus
}
#endif

#endif // ERROR_HANDLER_H
