# HC-SR04超声波传感器模块API使用说明

**作者**: 刘旭  
**版本**: 1.0.0  
**日期**: 2024

## 📡 模块概述

HC-SR04超声波传感器模块专门用于水位检测，基于WiringPi库实现，为Orange Pi Zero 2W优化。

### 🎯 主要特性

- **高精度测距**: 2cm - 400cm测量范围
- **水位检测**: 专门优化的水位测量算法
- **多次采样**: 提高测量精度和稳定性
- **温度补偿**: 可选的温度补偿功能
- **错误处理**: 完善的错误检测和处理
- **日志集成**: 支持详细的调试日志
- **线程安全**: 支持多线程环境

## 🔧 硬件连接

### Orange Pi Zero 2W 引脚配置
```
HC-SR04    Orange Pi Zero 2W
VCC    ->  5V (物理引脚2)
GND    ->  GND (物理引脚6)
Trig   ->  GPIO 268 (wPi 22, 物理引脚33)
Echo   ->  GPIO 258 (wPi 23, 物理引脚35)
```

### 水位检测安装
```
传感器安装位置: 水箱顶部，向下测量
最大水位: 35cm (可配置)
安装高度: 传感器到水箱底部的距离
```

## 🔧 快速开始

### 1. 包含头文件

```c
#include "hcsr04.h"
#include "logger.h"  // 可选，用于日志记录
```

### 2. 基本使用流程

```c
// 1. 获取默认配置
hcsr04_config_t config;
hcsr04_get_default_config(&config);

// 2. 创建传感器实例
hcsr04_t sensor = hcsr04_create(&config);

// 3. 初始化传感器
int result = hcsr04_init(sensor);
if (result != HCSR04_OK) {
    printf("初始化失败: %s\n", hcsr04_error_to_string(result));
    return -1;
}

// 4. 测量距离
float distance = hcsr04_read_distance(sensor);
printf("距离: %.2f cm\n", distance);

// 5. 测量水位
float water_level = hcsr04_read_water_level(sensor);
printf("水位: %.2f cm\n", water_level);

// 6. 清理资源
hcsr04_destroy(sensor);
```

## 📊 API参考

### 配置管理

#### `hcsr04_get_default_config()`
```c
void hcsr04_get_default_config(hcsr04_config_t* config);
```
获取默认传感器配置，已针对Orange Pi Zero 2W优化。

#### `hcsr04_create()`
```c
hcsr04_t hcsr04_create(const hcsr04_config_t* config);
```
创建传感器实例。

#### `hcsr04_destroy()`
```c
void hcsr04_destroy(hcsr04_t sensor);
```
销毁传感器实例并释放资源。

### 设备管理

#### `hcsr04_init()`
```c
int hcsr04_init(hcsr04_t sensor);
```
初始化传感器，配置GPIO引脚。

#### `hcsr04_deinit()`
```c
int hcsr04_deinit(hcsr04_t sensor);
```
反初始化传感器，释放GPIO资源。

### 测量功能

#### `hcsr04_read_distance()` - 测量距离
```c
float hcsr04_read_distance(hcsr04_t sensor);
```
测量传感器到目标的距离(cm)。

**返回值**:
- 成功: 距离值(2.0 - 400.0 cm)
- 失败: -1.0

**示例**:
```c
float distance = hcsr04_read_distance(sensor);
if (distance > 0) {
    printf("测量距离: %.2f cm\n", distance);
} else {
    printf("测量失败\n");
}
```

#### `hcsr04_read_water_level()` - 测量水位
```c
float hcsr04_read_water_level(hcsr04_t sensor);
```
测量当前水位高度(cm)。

**计算公式**: 水位 = 最大水位 - (测量距离 - 传感器安装高度)

**示例**:
```c
float water_level = hcsr04_read_water_level(sensor);
if (water_level >= 0) {
    printf("当前水位: %.2f cm\n", water_level);
    printf("水位百分比: %.1f%%\n", (water_level / 35.0) * 100);
} else {
    printf("水位测量失败或水箱为空\n");
}
```

#### `hcsr04_read_multiple()` - 多次测量
```c
int hcsr04_read_multiple(hcsr04_t sensor, float* distances, int count);
```
进行多次测量，提高精度。

**示例**:
```c
float distances[5];
int valid_count = hcsr04_read_multiple(sensor, distances, 5);

if (valid_count > 0) {
    float sum = 0;
    for (int i = 0; i < valid_count; i++) {
        sum += distances[i];
    }
    float average = sum / valid_count;
    printf("平均距离: %.2f cm (基于%d次测量)\n", average, valid_count);
}
```

### 配置设置

#### `hcsr04_set_max_water_level()`
```c
int hcsr04_set_max_water_level(hcsr04_t sensor, float max_level);
```
设置最大水位高度。

**示例**:
```c
// 设置水箱最大水位为40cm
hcsr04_set_max_water_level(sensor, 40.0);
```

#### `hcsr04_set_sensor_height()`
```c
int hcsr04_set_sensor_height(hcsr04_t sensor, float height);
```
设置传感器安装高度(传感器到水箱底部的距离)。

#### `hcsr04_set_temperature()`
```c
int hcsr04_set_temperature(hcsr04_t sensor, float temperature);
```
设置环境温度，用于声速补偿。

### 状态查询

#### `hcsr04_get_info()`
```c
int hcsr04_get_info(hcsr04_t sensor, hcsr04_info_t* info);
```
获取传感器信息和统计数据。

#### `hcsr04_is_connected()`
```c
bool hcsr04_is_connected(hcsr04_t sensor);
```
检查传感器连接状态。

## 🎛️ 配置选项

### 配置结构
```c
typedef struct {
    uint8_t trig_pin;           // 触发引脚 (wPi编号)
    uint8_t echo_pin;           // 回响引脚 (wPi编号)
    float max_water_level;      // 最大水位 (cm)
    float sensor_height;        // 传感器安装高度 (cm)
    int timeout_us;             // 超时时间 (微秒)
    int sample_count;           // 采样次数
    float temperature;          // 环境温度 (°C)
    bool enable_temperature_compensation; // 温度补偿
} hcsr04_config_t;
```

### 默认配置值
```c
trig_pin = 22;              // wPi 22 (GPIO 268)
echo_pin = 23;              // wPi 23 (GPIO 258)  
max_water_level = 35.0;     // 35cm最大水位
sensor_height = 40.0;       // 40cm安装高度
timeout_us = 30000;         // 30ms超时
sample_count = 3;           // 3次采样
temperature = 20.0;         // 20°C环境温度
enable_temperature_compensation = false;
```

## 💡 使用技巧

### 1. 水位监控
```c
void monitor_water_level(hcsr04_t sensor) {
    float water_level = hcsr04_read_water_level(sensor);
    
    if (water_level < 0) {
        printf("⚠️ 水箱为空或传感器故障\n");
    } else if (water_level < 5.0) {
        printf("🔴 水位过低: %.2f cm\n", water_level);
    } else if (water_level > 30.0) {
        printf("🔵 水位充足: %.2f cm\n", water_level);
    } else {
        printf("🟡 水位正常: %.2f cm\n", water_level);
    }
}
```

### 2. 精确测量
```c
float get_accurate_distance(hcsr04_t sensor) {
    float distances[10];
    int valid_count = hcsr04_read_multiple(sensor, distances, 10);
    
    if (valid_count < 3) {
        return -1.0; // 测量失败
    }
    
    // 排序并取中位数
    qsort(distances, valid_count, sizeof(float), compare_float);
    return distances[valid_count / 2];
}
```

### 3. 温度补偿
```c
void setup_temperature_compensation(hcsr04_t sensor, float temperature) {
    hcsr04_set_temperature(sensor, temperature);
    
    hcsr04_config_t config;
    hcsr04_get_config(sensor, &config);
    config.enable_temperature_compensation = true;
    hcsr04_set_config(sensor, &config);
}
```

### 4. 错误处理
```c
float safe_read_water_level(hcsr04_t sensor) {
    for (int retry = 0; retry < 3; retry++) {
        float level = hcsr04_read_water_level(sensor);
        if (level >= 0) {
            return level;
        }
        usleep(100000); // 等待100ms后重试
    }
    return -1.0; // 多次重试失败
}
```

## ⚠️ 注意事项

1. **GPIO权限**: 需要root权限或正确的GPIO权限设置
2. **WiringPi依赖**: 确保已安装WiringPi库
3. **硬件连接**: 检查引脚连接是否正确
4. **电源要求**: HC-SR04需要5V电源供电
5. **测量环境**: 避免在有强烈声音干扰的环境中使用
6. **安装位置**: 传感器应垂直向下安装，避免倾斜

## 🔧 编译要求

```makefile
# 需要链接的库
LDLIBS += -lhcsr04 -llogger -lwiringPi -lpthread
```

## 📝 完整示例

```c
#include "hcsr04.h"
#include "logger.h"
#include <unistd.h>

int main() {
    // 创建日志记录器
    log_config_t log_config;
    logger_get_default_config(&log_config, "water_monitor");
    logger_t logger = logger_create(&log_config);
    
    // 创建传感器配置
    hcsr04_config_t config;
    hcsr04_get_default_config(&config);
    config.max_water_level = 35.0;  // 35cm水箱
    
    // 创建传感器实例
    hcsr04_t sensor = hcsr04_create(&config);
    hcsr04_set_logger(sensor, logger);
    
    // 初始化传感器
    if (hcsr04_init(sensor) != HCSR04_OK) {
        logger_error(logger, "传感器初始化失败");
        return -1;
    }
    
    logger_info(logger, "开始水位监控...");
    
    // 持续监控水位
    for (int i = 0; i < 10; i++) {
        float distance = hcsr04_read_distance(sensor);
        float water_level = hcsr04_read_water_level(sensor);
        
        logger_info(logger, "测量 %d: 距离=%.2fcm, 水位=%.2fcm", 
                   i+1, distance, water_level);
        
        sleep(2);
    }
    
    // 清理资源
    hcsr04_destroy(sensor);
    logger_destroy(logger);
    
    return 0;
}
```

---

**技术支持**: 刘旭  
**更新日期**: 2024年
