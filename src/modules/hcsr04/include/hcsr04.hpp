/**
 * @file hcsr04.hpp
 * @brief HC-SR04超声波传感器模块 - C++面向对象版本
 * <AUTHOR>
 * @date 2024
 *
 * 用于测量水位高度的超声波传感器驱动
 * 支持Orange Pi Zero 2W平台
 */

#ifndef HCSR04_HPP
#define HCSR04_HPP

#include <string>
#include <memory>
#include <mutex>
#include <chrono>
#include "logger.hpp"

namespace greenland {

// 传感器配置结构
struct HCSR04Config {
    int trig_pin = 22;              // 触发引脚 (wPi编号)
    int echo_pin = 23;              // 回声引脚 (wPi编号)
    float max_distance = 400.0f;    // 最大测量距离 (cm)
    float max_water_level = 35.0f;  // 最大水位高度 (cm)
    int timeout_us = 30000;         // 超时时间 (微秒)
    int measurement_delay_ms = 100; // 测量间隔 (毫秒)
    bool enable_logging = true;     // 启用日志
};

// 测量结果结构
struct HCSR04Result {
    bool success = false;           // 测量是否成功
    float distance_cm = 0.0f;       // 距离 (厘米)
    float water_level_cm = 0.0f;    // 水位高度 (厘米)
    float water_level_percent = 0.0f; // 水位百分比
    std::chrono::system_clock::time_point timestamp; // 时间戳
    std::string error_message;      // 错误信息
};

// 错误码枚举
enum class HCSR04Error {
    OK = 0,
    INIT_ERROR,
    GPIO_ERROR,
    TIMEOUT_ERROR,
    PARAM_ERROR,
    NOT_INITIALIZED
};

/**
 * @brief HC-SR04超声波传感器类
 */
class HCSR04Sensor {
public:
    /**
     * @brief 构造函数
     * @param config 传感器配置
     * @param logger 日志器指针
     */
    explicit HCSR04Sensor(const HCSR04Config& config, 
                          std::shared_ptr<Logger> logger = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~HCSR04Sensor();
    
    /**
     * @brief 禁用拷贝构造和赋值
     */
    HCSR04Sensor(const HCSR04Sensor&) = delete;
    HCSR04Sensor& operator=(const HCSR04Sensor&) = delete;
    
    /**
     * @brief 初始化传感器
     * @return 错误码
     */
    HCSR04Error initialize();
    
    /**
     * @brief 清理资源
     */
    void cleanup();
    
    /**
     * @brief 测量距离
     * @return 测量结果
     */
    HCSR04Result measureDistance();
    
    /**
     * @brief 测量水位
     * @return 测量结果
     */
    HCSR04Result measureWaterLevel();
    
    /**
     * @brief 获取配置
     * @return 当前配置
     */
    const HCSR04Config& getConfig() const;
    
    /**
     * @brief 设置配置
     * @param config 新配置
     * @return 错误码
     */
    HCSR04Error setConfig(const HCSR04Config& config);
    
    /**
     * @brief 检查传感器是否已初始化
     * @return 是否已初始化
     */
    bool isInitialized() const;
    
    /**
     * @brief 自检测试
     * @return 测试结果
     */
    HCSR04Result selfTest();
    
    /**
     * @brief 校准传感器
     * @param known_distance 已知距离 (cm)
     * @return 错误码
     */
    HCSR04Error calibrate(float known_distance);
    
    /**
     * @brief 获取传感器状态信息
     * @return 状态字符串
     */
    std::string getStatusInfo() const;

private:
    /**
     * @brief 初始化GPIO
     * @return 错误码
     */
    HCSR04Error initializeGPIO();
    
    /**
     * @brief 发送触发脉冲
     */
    void sendTriggerPulse();
    
    /**
     * @brief 等待回声信号
     * @return 脉冲持续时间 (微秒)，-1表示超时
     */
    long waitForEcho();
    
    /**
     * @brief 计算距离
     * @param pulse_duration 脉冲持续时间 (微秒)
     * @return 距离 (厘米)
     */
    float calculateDistance(long pulse_duration);
    
    /**
     * @brief 计算水位
     * @param distance 距离 (厘米)
     * @return 水位高度 (厘米)
     */
    float calculateWaterLevel(float distance);
    
    /**
     * @brief 验证测量结果
     * @param distance 距离
     * @return 是否有效
     */
    bool validateMeasurement(float distance);
    
    /**
     * @brief 记录日志
     * @param level 日志级别
     * @param message 日志消息
     */
    void logMessage(LogLevel level, const std::string& message);

private:
    HCSR04Config config_;
    std::shared_ptr<Logger> logger_;
    mutable std::mutex mutex_;
    bool initialized_;
    bool gpio_initialized_;
    
    // 统计信息
    uint64_t total_measurements_;
    uint64_t successful_measurements_;
    uint64_t failed_measurements_;
    float calibration_offset_;
};

/**
 * @brief 工具函数：错误码转字符串
 * @param error 错误码
 * @return 错误描述字符串
 */
std::string hcsr04ErrorToString(HCSR04Error error);

/**
 * @brief 创建默认配置
 * @return 默认配置
 */
HCSR04Config createDefaultHCSR04Config();

/**
 * @brief 验证配置有效性
 * @param config 配置
 * @return 是否有效
 */
bool validateHCSR04Config(const HCSR04Config& config);

} // namespace greenland

#endif // HCSR04_HPP
