/**
 * @file hcsr04.h
 * @brief HC-SR04 超声波传感器驱动头文件
 * <AUTHOR> Team
 * @date 2024
 *
 * HC-SR04 超声波传感器用于水位检测
 * - 测量范围: 2cm - 400cm
 * - 精度: 3mm
 * - 工作电压: 5V
 * - 工作电流: 15mA
 * - 工作频率: 40Hz
 * - 触发信号: 10μs TTL脉冲
 * - 回响信号: 输出TTL电平信号，与距离成正比
 *
 * 引脚连接:
 * - VCC: 5V
 * - GND: GND
 * - Trig: GPIO 22 (物理引脚 15)
 * - Echo: GPIO 23 (物理引脚 16)
 */

#ifndef HCSR04_H
#define HCSR04_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

// GPIO 引脚定义 (Orange Pi Zero 2W)
#define HCSR04_TRIG_PIN         268     // 触发引脚 (wPi 22, 物理引脚 33)
#define HCSR04_ECHO_PIN         258     // 回响引脚 (wPi 23, 物理引脚 35)

// 测量参数
#define HCSR04_MAX_DISTANCE     400.0   // 最大测量距离 (cm)
#define HCSR04_MIN_DISTANCE     2.0     // 最小测量距离 (cm)
#define HCSR04_SOUND_SPEED      34300.0 // 声速 (cm/s) 在20°C
#define HCSR04_TIMEOUT_US       30000   // 超时时间 (μs)
#define HCSR04_TRIGGER_TIME_US  10      // 触发脉冲宽度 (μs)

// 水位检测参数 (根据用户实际水箱调整)
#define HCSR04_TANK_HEIGHT      35.0    // 水箱高度 (cm) - 用户实际35cm水箱
#define HCSR04_SENSOR_OFFSET    3.0     // 传感器安装偏移 (cm)

/**
 * @brief HC-SR04 错误码枚举
 */
typedef enum {
    HCSR04_OK = 0,                  // 操作成功
    HCSR04_ERROR_INIT = -1,         // 初始化错误
    HCSR04_ERROR_GPIO = -2,         // GPIO 操作错误
    HCSR04_ERROR_TIMEOUT = -3,      // 测量超时
    HCSR04_ERROR_RANGE = -4,        // 测量超出范围
    HCSR04_ERROR_PARAM = -5,        // 参数错误
    HCSR04_ERROR_NOT_INIT = -6      // 未初始化
} hcsr04_error_t;

/**
 * @brief HC-SR04 数据结构
 */
typedef struct {
    float distance_cm;              // 距离 (cm)
    float water_level_cm;           // 水位高度 (cm)
    float water_level_percent;      // 水位百分比 (%)
    uint32_t echo_time_us;          // 回响时间 (μs)
    bool valid;                     // 数据有效性
    uint32_t timestamp;             // 时间戳
} hcsr04_data_t;

/**
 * @brief HC-SR04 配置结构
 */
typedef struct {
    uint8_t trig_pin;               // 触发引脚
    uint8_t echo_pin;               // 回响引脚
    float tank_height_cm;           // 水箱高度 (cm)
    float sensor_offset_cm;         // 传感器偏移 (cm)
    uint32_t timeout_us;            // 超时时间 (μs)
    uint8_t sample_count;           // 采样次数 (用于平均)
} hcsr04_config_t;

// =============================================================================
// 核心 API 函数
// =============================================================================

/**
 * @brief 初始化 HC-SR04 传感器
 *
 * 使用默认配置初始化传感器:
 * - Trig: GPIO 268 (wPi 22, 物理引脚 33)
 * - Echo: GPIO 258 (wPi 23, 物理引脚 35)
 * - 水箱高度: 35cm (用户实际水箱)
 * - 传感器偏移: 3cm
 *
 * @return 错误码
 * @retval HCSR04_OK 初始化成功
 * @retval HCSR04_ERROR_GPIO GPIO 初始化失败
 * @retval HCSR04_ERROR_INIT 初始化失败
 */
int hcsr04_init(void);

/**
 * @brief 使用自定义配置初始化 HC-SR04 传感器
 *
 * @param config 传感器配置
 * @return 错误码
 * @retval HCSR04_OK 初始化成功
 * @retval HCSR04_ERROR_PARAM 参数错误
 * @retval HCSR04_ERROR_GPIO GPIO 初始化失败
 */
int hcsr04_init_with_config(const hcsr04_config_t* config);

/**
 * @brief 清理 HC-SR04 传感器资源
 */
void hcsr04_deinit(void);

/**
 * @brief 读取 HC-SR04 传感器数据
 *
 * @param data 指向数据结构的指针
 * @return 错误码
 * @retval HCSR04_OK 读取成功
 * @retval HCSR04_ERROR_PARAM 参数错误
 * @retval HCSR04_ERROR_NOT_INIT 传感器未初始化
 * @retval HCSR04_ERROR_TIMEOUT 测量超时
 * @retval HCSR04_ERROR_RANGE 测量超出范围
 */
int hcsr04_read_data(hcsr04_data_t* data);

/**
 * @brief 读取距离值 (简化接口)
 *
 * @param distance_cm 指向距离变量的指针 (cm)
 * @return 错误码
 * @retval HCSR04_OK 读取成功
 * @retval HCSR04_ERROR_PARAM 参数错误
 * @retval HCSR04_ERROR_NOT_INIT 传感器未初始化
 * @retval HCSR04_ERROR_TIMEOUT 测量超时
 */
int hcsr04_read_distance(float* distance_cm);

/**
 * @brief 读取水位高度 (简化接口)
 *
 * @param water_level_cm 指向水位高度变量的指针 (cm)
 * @return 错误码
 * @retval HCSR04_OK 读取成功
 * @retval HCSR04_ERROR_PARAM 参数错误
 * @retval HCSR04_ERROR_NOT_INIT 传感器未初始化
 * @retval HCSR04_ERROR_TIMEOUT 测量超时
 */
int hcsr04_read_water_level(float* water_level_cm);

// =============================================================================
// 配置和状态函数
// =============================================================================

/**
 * @brief 设置水箱高度
 *
 * @param height_cm 水箱高度 (cm)
 * @return 错误码
 * @retval HCSR04_OK 设置成功
 * @retval HCSR04_ERROR_PARAM 参数错误
 */
int hcsr04_set_tank_height(float height_cm);

/**
 * @brief 获取水箱高度
 *
 * @return 水箱高度 (cm)
 */
float hcsr04_get_tank_height(void);

/**
 * @brief 设置传感器偏移
 *
 * @param offset_cm 传感器偏移 (cm)
 * @return 错误码
 * @retval HCSR04_OK 设置成功
 * @retval HCSR04_ERROR_PARAM 参数错误
 */
int hcsr04_set_sensor_offset(float offset_cm);

/**
 * @brief 获取传感器偏移
 *
 * @return 传感器偏移 (cm)
 */
float hcsr04_get_sensor_offset(void);

/**
 * @brief 检查传感器是否已初始化
 *
 * @return 初始化状态
 * @retval true 已初始化
 * @retval false 未初始化
 */
bool hcsr04_is_initialized(void);

// =============================================================================
// 高级功能函数
// =============================================================================

/**
 * @brief 多次采样并取平均值
 *
 * @param data 指向数据结构的指针
 * @param sample_count 采样次数 (1-10)
 * @return 错误码
 * @retval HCSR04_OK 读取成功
 * @retval HCSR04_ERROR_PARAM 参数错误
 * @retval HCSR04_ERROR_NOT_INIT 传感器未初始化
 */
int hcsr04_read_averaged(hcsr04_data_t* data, uint8_t sample_count);

/**
 * @brief 校准传感器 (测量已知距离)
 *
 * @param known_distance_cm 已知距离 (cm)
 * @param measured_distance_cm 指向测量距离变量的指针
 * @return 错误码
 * @retval HCSR04_OK 校准成功
 * @retval HCSR04_ERROR_PARAM 参数错误
 * @retval HCSR04_ERROR_NOT_INIT 传感器未初始化
 */
int hcsr04_calibrate(float known_distance_cm, float* measured_distance_cm);

/**
 * @brief 获取传感器状态信息
 *
 * @param info 状态信息字符串缓冲区
 * @param size 缓冲区大小
 * @return 错误码
 * @retval HCSR04_OK 获取成功
 * @retval HCSR04_ERROR_PARAM 参数错误
 */
int hcsr04_get_status_info(char* info, size_t size);

// =============================================================================
// 工具函数
// =============================================================================

/**
 * @brief 将错误码转换为字符串
 *
 * @param error 错误码
 * @return 错误描述字符串
 */
const char* hcsr04_error_to_string(hcsr04_error_t error);

/**
 * @brief 获取默认配置
 *
 * @param config 指向配置结构的指针
 */
void hcsr04_get_default_config(hcsr04_config_t* config);

#ifdef __cplusplus
}
#endif

#endif // HCSR04_H
