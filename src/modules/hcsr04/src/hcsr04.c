/**
 * @file hcsr04.c
 * @brief HC-SR04 超声波传感器驱动实现
 * <AUTHOR> Team
 * @date 2024
 */

#include "hcsr04.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/time.h>
#include <time.h>
#include <math.h>
#include <errno.h>

// GPIO 系统文件路径
#define GPIO_PATH "/sys/class/gpio"
#define GPIO_EXPORT_PATH "/sys/class/gpio/export"
#define GPIO_UNEXPORT_PATH "/sys/class/gpio/unexport"

// 全局变量
static bool g_initialized = false;
static hcsr04_config_t g_config;
static int g_trig_fd = -1;
static int g_echo_fd = -1;

// 内部函数声明
static int gpio_export(int pin);
static int gpio_unexport(int pin);
static int gpio_set_direction(int pin, const char* direction);
static int gpio_write_value(int pin, int value);
static int gpio_read_value(int pin);
static int gpio_open_value_fd(int pin, int flags);
static uint64_t get_time_us(void);
static int measure_echo_time(uint32_t* echo_time_us);
static float calculate_distance(uint32_t echo_time_us);
static float calculate_water_level(float distance_cm);

/**
 * @brief 获取微秒级时间戳
 */
static uint64_t get_time_us(void) {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (uint64_t)tv.tv_sec * 1000000 + tv.tv_usec;
}

/**
 * @brief 导出 GPIO 引脚
 */
static int gpio_export(int pin) {
    int fd = open(GPIO_EXPORT_PATH, O_WRONLY);
    if (fd < 0) {
        printf("错误: 无法打开 GPIO export 文件: %s\n", strerror(errno));
        return HCSR04_ERROR_GPIO;
    }

    char pin_str[8];
    snprintf(pin_str, sizeof(pin_str), "%d", pin);

    if (write(fd, pin_str, strlen(pin_str)) < 0) {
        // 如果引脚已经导出，忽略错误
        if (errno != EBUSY) {
            printf("错误: 无法导出 GPIO %d: %s\n", pin, strerror(errno));
            close(fd);
            return HCSR04_ERROR_GPIO;
        }
    }

    close(fd);
    usleep(100000); // 等待 100ms 让系统创建文件
    return HCSR04_OK;
}

/**
 * @brief 取消导出 GPIO 引脚
 */
static int gpio_unexport(int pin) {
    int fd = open(GPIO_UNEXPORT_PATH, O_WRONLY);
    if (fd < 0) {
        return HCSR04_ERROR_GPIO;
    }

    char pin_str[8];
    snprintf(pin_str, sizeof(pin_str), "%d", pin);
    write(fd, pin_str, strlen(pin_str));
    close(fd);

    return HCSR04_OK;
}

/**
 * @brief 设置 GPIO 方向
 */
static int gpio_set_direction(int pin, const char* direction) {
    char path[64];
    snprintf(path, sizeof(path), "%s/gpio%d/direction", GPIO_PATH, pin);

    int fd = open(path, O_WRONLY);
    if (fd < 0) {
        printf("错误: 无法打开 GPIO %d direction 文件: %s\n", pin, strerror(errno));
        return HCSR04_ERROR_GPIO;
    }

    if (write(fd, direction, strlen(direction)) < 0) {
        printf("错误: 无法设置 GPIO %d 方向: %s\n", pin, strerror(errno));
        close(fd);
        return HCSR04_ERROR_GPIO;
    }

    close(fd);
    return HCSR04_OK;
}

/**
 * @brief 写入 GPIO 值
 */
static int gpio_write_value(int pin, int value) {
    char path[64];
    snprintf(path, sizeof(path), "%s/gpio%d/value", GPIO_PATH, pin);

    int fd = open(path, O_WRONLY);
    if (fd < 0) {
        return HCSR04_ERROR_GPIO;
    }

    char value_str[2] = {'0' + value, '\0'};
    int result = write(fd, value_str, 1);
    close(fd);

    return (result > 0) ? HCSR04_OK : HCSR04_ERROR_GPIO;
}

/**
 * @brief 读取 GPIO 值
 */
static int gpio_read_value(int pin) {
    char path[64];
    snprintf(path, sizeof(path), "%s/gpio%d/value", GPIO_PATH, pin);

    int fd = open(path, O_RDONLY);
    if (fd < 0) {
        return -1;
    }

    char value_str[2];
    int result = read(fd, value_str, 1);
    close(fd);

    if (result > 0) {
        return value_str[0] - '0';
    }

    return -1;
}

/**
 * @brief 打开 GPIO value 文件描述符
 */
static int gpio_open_value_fd(int pin, int flags) {
    char path[64];
    snprintf(path, sizeof(path), "%s/gpio%d/value", GPIO_PATH, pin);
    return open(path, flags);
}

/**
 * @brief 测量回响时间
 */
static int measure_echo_time(uint32_t* echo_time_us) {
    if (!echo_time_us) {
        return HCSR04_ERROR_PARAM;
    }

    // 发送触发脉冲
    gpio_write_value(g_config.trig_pin, 0);
    usleep(2);
    gpio_write_value(g_config.trig_pin, 1);
    usleep(HCSR04_TRIGGER_TIME_US);
    gpio_write_value(g_config.trig_pin, 0);

    // 等待回响信号开始
    uint64_t start_time = get_time_us();
    uint64_t timeout = start_time + g_config.timeout_us;

    while (gpio_read_value(g_config.echo_pin) == 0) {
        if (get_time_us() > timeout) {
            return HCSR04_ERROR_TIMEOUT;
        }
        usleep(1);
    }

    // 记录回响开始时间
    uint64_t echo_start = get_time_us();

    // 等待回响信号结束
    timeout = echo_start + g_config.timeout_us;
    while (gpio_read_value(g_config.echo_pin) == 1) {
        if (get_time_us() > timeout) {
            return HCSR04_ERROR_TIMEOUT;
        }
        usleep(1);
    }

    // 记录回响结束时间
    uint64_t echo_end = get_time_us();

    *echo_time_us = (uint32_t)(echo_end - echo_start);
    return HCSR04_OK;
}

/**
 * @brief 计算距离
 */
static float calculate_distance(uint32_t echo_time_us) {
    // 距离 = (回响时间 * 声速) / 2
    // 除以2是因为声波往返
    return (float)echo_time_us * HCSR04_SOUND_SPEED / 2000000.0f;
}

/**
 * @brief 计算水位高度
 */
static float calculate_water_level(float distance_cm) {
    // 水位 = 水箱高度 - 传感器到水面距离 + 传感器偏移
    float water_level = g_config.tank_height_cm - distance_cm + g_config.sensor_offset_cm;

    // 确保水位不为负数
    if (water_level < 0) {
        water_level = 0;
    }

    // 确保水位不超过水箱高度
    if (water_level > g_config.tank_height_cm) {
        water_level = g_config.tank_height_cm;
    }

    return water_level;
}

/**
 * @brief 获取默认配置
 */
void hcsr04_get_default_config(hcsr04_config_t* config) {
    if (!config) return;

    config->trig_pin = HCSR04_TRIG_PIN;
    config->echo_pin = HCSR04_ECHO_PIN;
    config->tank_height_cm = HCSR04_TANK_HEIGHT;
    config->sensor_offset_cm = HCSR04_SENSOR_OFFSET;
    config->timeout_us = HCSR04_TIMEOUT_US;
    config->sample_count = 3;
}

/**
 * @brief 初始化 HC-SR04 传感器
 */
int hcsr04_init(void) {
    hcsr04_config_t default_config;
    hcsr04_get_default_config(&default_config);
    return hcsr04_init_with_config(&default_config);
}

/**
 * @brief 使用自定义配置初始化 HC-SR04 传感器
 */
int hcsr04_init_with_config(const hcsr04_config_t* config) {
    if (!config) {
        return HCSR04_ERROR_PARAM;
    }

    if (g_initialized) {
        hcsr04_deinit();
    }

    // 复制配置
    memcpy(&g_config, config, sizeof(hcsr04_config_t));

    // 导出 GPIO 引脚
    if (gpio_export(g_config.trig_pin) != HCSR04_OK) {
        return HCSR04_ERROR_GPIO;
    }

    if (gpio_export(g_config.echo_pin) != HCSR04_OK) {
        gpio_unexport(g_config.trig_pin);
        return HCSR04_ERROR_GPIO;
    }

    // 设置引脚方向
    if (gpio_set_direction(g_config.trig_pin, "out") != HCSR04_OK) {
        gpio_unexport(g_config.trig_pin);
        gpio_unexport(g_config.echo_pin);
        return HCSR04_ERROR_GPIO;
    }

    if (gpio_set_direction(g_config.echo_pin, "in") != HCSR04_OK) {
        gpio_unexport(g_config.trig_pin);
        gpio_unexport(g_config.echo_pin);
        return HCSR04_ERROR_GPIO;
    }

    // 初始化触发引脚为低电平
    gpio_write_value(g_config.trig_pin, 0);

    g_initialized = true;

    printf("HC-SR04 传感器初始化成功\n");
    printf("  Trig 引脚: GPIO %d\n", g_config.trig_pin);
    printf("  Echo 引脚: GPIO %d\n", g_config.echo_pin);
    printf("  水箱高度: %.1f cm\n", g_config.tank_height_cm);
    printf("  传感器偏移: %.1f cm\n", g_config.sensor_offset_cm);

    return HCSR04_OK;
}

/**
 * @brief 清理 HC-SR04 传感器资源
 */
void hcsr04_deinit(void) {
    if (!g_initialized) {
        return;
    }

    // 关闭文件描述符
    if (g_trig_fd >= 0) {
        close(g_trig_fd);
        g_trig_fd = -1;
    }

    if (g_echo_fd >= 0) {
        close(g_echo_fd);
        g_echo_fd = -1;
    }

    // 取消导出 GPIO 引脚
    gpio_unexport(g_config.trig_pin);
    gpio_unexport(g_config.echo_pin);

    g_initialized = false;

    printf("HC-SR04 传感器资源已清理\n");
}

/**
 * @brief 读取 HC-SR04 传感器数据
 */
int hcsr04_read_data(hcsr04_data_t* data) {
    if (!data) {
        return HCSR04_ERROR_PARAM;
    }

    if (!g_initialized) {
        return HCSR04_ERROR_NOT_INIT;
    }

    // 初始化数据结构
    memset(data, 0, sizeof(hcsr04_data_t));
    data->timestamp = (uint32_t)time(NULL);

    // 测量回响时间
    uint32_t echo_time_us;
    int result = measure_echo_time(&echo_time_us);
    if (result != HCSR04_OK) {
        data->valid = false;
        return result;
    }

    // 计算距离
    float distance = calculate_distance(echo_time_us);

    // 检查距离范围
    if (distance < HCSR04_MIN_DISTANCE || distance > HCSR04_MAX_DISTANCE) {
        data->valid = false;
        return HCSR04_ERROR_RANGE;
    }

    // 填充数据
    data->echo_time_us = echo_time_us;
    data->distance_cm = distance;
    data->water_level_cm = calculate_water_level(distance);
    data->water_level_percent = (data->water_level_cm / g_config.tank_height_cm) * 100.0f;
    data->valid = true;

    return HCSR04_OK;
}

/**
 * @brief 读取距离值 (简化接口)
 */
int hcsr04_read_distance(float* distance_cm) {
    if (!distance_cm) {
        return HCSR04_ERROR_PARAM;
    }

    hcsr04_data_t data;
    int result = hcsr04_read_data(&data);
    if (result == HCSR04_OK) {
        *distance_cm = data.distance_cm;
    }

    return result;
}

/**
 * @brief 读取水位高度 (简化接口)
 */
int hcsr04_read_water_level(float* water_level_cm) {
    if (!water_level_cm) {
        return HCSR04_ERROR_PARAM;
    }

    hcsr04_data_t data;
    int result = hcsr04_read_data(&data);
    if (result == HCSR04_OK) {
        *water_level_cm = data.water_level_cm;
    }

    return result;
}

/**
 * @brief 设置水箱高度
 */
int hcsr04_set_tank_height(float height_cm) {
    if (height_cm <= 0 || height_cm > 1000.0f) {
        return HCSR04_ERROR_PARAM;
    }

    g_config.tank_height_cm = height_cm;
    return HCSR04_OK;
}

/**
 * @brief 获取水箱高度
 */
float hcsr04_get_tank_height(void) {
    return g_config.tank_height_cm;
}

/**
 * @brief 设置传感器偏移
 */
int hcsr04_set_sensor_offset(float offset_cm) {
    if (offset_cm < -50.0f || offset_cm > 50.0f) {
        return HCSR04_ERROR_PARAM;
    }

    g_config.sensor_offset_cm = offset_cm;
    return HCSR04_OK;
}

/**
 * @brief 获取传感器偏移
 */
float hcsr04_get_sensor_offset(void) {
    return g_config.sensor_offset_cm;
}

/**
 * @brief 检查传感器是否已初始化
 */
bool hcsr04_is_initialized(void) {
    return g_initialized;
}

/**
 * @brief 多次采样并取平均值
 */
int hcsr04_read_averaged(hcsr04_data_t* data, uint8_t sample_count) {
    if (!data || sample_count == 0 || sample_count > 10) {
        return HCSR04_ERROR_PARAM;
    }

    if (!g_initialized) {
        return HCSR04_ERROR_NOT_INIT;
    }

    float distance_sum = 0.0f;
    uint32_t echo_time_sum = 0;
    int valid_samples = 0;

    for (int i = 0; i < sample_count; i++) {
        hcsr04_data_t sample_data;
        int result = hcsr04_read_data(&sample_data);

        if (result == HCSR04_OK && sample_data.valid) {
            distance_sum += sample_data.distance_cm;
            echo_time_sum += sample_data.echo_time_us;
            valid_samples++;
        }

        // 采样间隔
        if (i < sample_count - 1) {
            usleep(60000); // 60ms 间隔
        }
    }

    if (valid_samples == 0) {
        data->valid = false;
        return HCSR04_ERROR_TIMEOUT;
    }

    // 计算平均值
    float avg_distance = distance_sum / valid_samples;
    uint32_t avg_echo_time = echo_time_sum / valid_samples;

    // 填充数据
    data->distance_cm = avg_distance;
    data->echo_time_us = avg_echo_time;
    data->water_level_cm = calculate_water_level(avg_distance);
    data->water_level_percent = (data->water_level_cm / g_config.tank_height_cm) * 100.0f;
    data->valid = true;
    data->timestamp = (uint32_t)time(NULL);

    return HCSR04_OK;
}

/**
 * @brief 校准传感器
 */
int hcsr04_calibrate(float known_distance_cm, float* measured_distance_cm) {
    if (!measured_distance_cm || known_distance_cm <= 0) {
        return HCSR04_ERROR_PARAM;
    }

    if (!g_initialized) {
        return HCSR04_ERROR_NOT_INIT;
    }

    // 多次测量取平均值
    hcsr04_data_t data;
    int result = hcsr04_read_averaged(&data, 5);
    if (result != HCSR04_OK) {
        return result;
    }

    *measured_distance_cm = data.distance_cm;

    printf("校准结果:\n");
    printf("  已知距离: %.2f cm\n", known_distance_cm);
    printf("  测量距离: %.2f cm\n", *measured_distance_cm);
    printf("  误差: %.2f cm (%.1f%%)\n",
           fabs(*measured_distance_cm - known_distance_cm),
           fabs(*measured_distance_cm - known_distance_cm) / known_distance_cm * 100.0f);

    return HCSR04_OK;
}

/**
 * @brief 获取传感器状态信息
 */
int hcsr04_get_status_info(char* info, size_t size) {
    if (!info || size == 0) {
        return HCSR04_ERROR_PARAM;
    }

    snprintf(info, size,
        "HC-SR04 状态信息:\n"
        "  初始化状态: %s\n"
        "  Trig 引脚: GPIO %d\n"
        "  Echo 引脚: GPIO %d\n"
        "  水箱高度: %.1f cm\n"
        "  传感器偏移: %.1f cm\n"
        "  超时时间: %u μs\n"
        "  测量范围: %.1f - %.1f cm\n",
        g_initialized ? "已初始化" : "未初始化",
        g_config.trig_pin,
        g_config.echo_pin,
        g_config.tank_height_cm,
        g_config.sensor_offset_cm,
        g_config.timeout_us,
        HCSR04_MIN_DISTANCE,
        HCSR04_MAX_DISTANCE
    );

    return HCSR04_OK;
}

/**
 * @brief 将错误码转换为字符串
 */
const char* hcsr04_error_to_string(hcsr04_error_t error) {
    switch (error) {
        case HCSR04_OK:
            return "操作成功";
        case HCSR04_ERROR_INIT:
            return "初始化错误";
        case HCSR04_ERROR_GPIO:
            return "GPIO 操作错误";
        case HCSR04_ERROR_TIMEOUT:
            return "测量超时";
        case HCSR04_ERROR_RANGE:
            return "测量超出范围";
        case HCSR04_ERROR_PARAM:
            return "参数错误";
        case HCSR04_ERROR_NOT_INIT:
            return "传感器未初始化";
        default:
            return "未知错误";
    }
}