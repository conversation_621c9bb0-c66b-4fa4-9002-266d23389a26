/**
 * @file hcsr04.cpp
 * @brief HC-SR04超声波传感器模块实现 - C++面向对象版本
 * <AUTHOR>
 * @date 2024
 */

#include "hcsr04.hpp"
#include <wiringPi.h>
#include <chrono>
#include <thread>
#include <sstream>
#include <iomanip>

namespace greenland {

HCSR04Sensor::HCSR04Sensor(const HCSR04Config& config, std::shared_ptr<Logger> logger)
    : config_(config), logger_(logger), initialized_(false), gpio_initialized_(false),
      total_measurements_(0), successful_measurements_(0), failed_measurements_(0),
      calibration_offset_(0.0f) {
    
    if (!logger_) {
        // 创建默认日志器
        LogConfig log_config = createDefaultConfig("HCSR04");
        logger_ = std::make_shared<Logger>(log_config);
    }
}

HCSR04Sensor::~HCSR04Sensor() {
    cleanup();
}

HCSR04Error HCSR04Sensor::initialize() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (initialized_) {
        return HCSR04Error::OK;
    }
    
    logMessage(LogLevel::INFO, "初始化HC-SR04超声波传感器...");
    
    // 验证配置
    if (!validateHCSR04Config(config_)) {
        logMessage(LogLevel::ERROR, "传感器配置无效");
        return HCSR04Error::PARAM_ERROR;
    }
    
    // 初始化GPIO
    HCSR04Error result = initializeGPIO();
    if (result != HCSR04Error::OK) {
        logMessage(LogLevel::ERROR, "GPIO初始化失败");
        return result;
    }
    
    // 重置统计信息
    total_measurements_ = 0;
    successful_measurements_ = 0;
    failed_measurements_ = 0;
    calibration_offset_ = 0.0f;
    
    initialized_ = true;
    
    std::ostringstream oss;
    oss << "HC-SR04传感器初始化成功 - Trig: " << config_.trig_pin 
        << ", Echo: " << config_.echo_pin;
    logMessage(LogLevel::INFO, oss.str());
    
    return HCSR04Error::OK;
}

HCSR04Error HCSR04Sensor::initializeGPIO() {
    if (gpio_initialized_) {
        return HCSR04Error::OK;
    }
    
    // 初始化wiringPi
    if (wiringPiSetup() == -1) {
        return HCSR04Error::GPIO_ERROR;
    }
    
    // 设置引脚模式
    pinMode(config_.trig_pin, OUTPUT);
    pinMode(config_.echo_pin, INPUT);
    
    // 初始化触发引脚为低电平
    digitalWrite(config_.trig_pin, LOW);
    
    gpio_initialized_ = true;
    return HCSR04Error::OK;
}

void HCSR04Sensor::cleanup() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (gpio_initialized_) {
        digitalWrite(config_.trig_pin, LOW);
        gpio_initialized_ = false;
    }
    
    initialized_ = false;
    logMessage(LogLevel::INFO, "HC-SR04传感器已清理");
}

HCSR04Result HCSR04Sensor::measureDistance() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    HCSR04Result result;
    result.timestamp = std::chrono::system_clock::now();
    
    if (!initialized_) {
        result.error_message = "传感器未初始化";
        logMessage(LogLevel::ERROR, result.error_message);
        failed_measurements_++;
        return result;
    }
    
    total_measurements_++;
    
    try {
        // 发送触发脉冲
        sendTriggerPulse();
        
        // 等待回声信号
        long pulse_duration = waitForEcho();
        
        if (pulse_duration < 0) {
            result.error_message = "测量超时";
            logMessage(LogLevel::WARN, result.error_message);
            failed_measurements_++;
            return result;
        }
        
        // 计算距离
        float distance = calculateDistance(pulse_duration);
        distance += calibration_offset_;  // 应用校准偏移
        
        if (!validateMeasurement(distance)) {
            result.error_message = "测量结果无效";
            logMessage(LogLevel::WARN, result.error_message);
            failed_measurements_++;
            return result;
        }
        
        result.success = true;
        result.distance_cm = distance;
        successful_measurements_++;
        
        if (config_.enable_logging) {
            std::ostringstream oss;
            oss << "距离测量: " << std::fixed << std::setprecision(2) 
                << distance << " cm";
            logMessage(LogLevel::DEBUG, oss.str());
        }
        
    } catch (const std::exception& e) {
        result.error_message = std::string("测量异常: ") + e.what();
        logMessage(LogLevel::ERROR, result.error_message);
        failed_measurements_++;
    }
    
    return result;
}

HCSR04Result HCSR04Sensor::measureWaterLevel() {
    HCSR04Result result = measureDistance();
    
    if (result.success) {
        result.water_level_cm = calculateWaterLevel(result.distance_cm);
        result.water_level_percent = (result.water_level_cm / config_.max_water_level) * 100.0f;
        
        // 限制百分比范围
        if (result.water_level_percent < 0.0f) result.water_level_percent = 0.0f;
        if (result.water_level_percent > 100.0f) result.water_level_percent = 100.0f;
        
        if (config_.enable_logging) {
            std::ostringstream oss;
            oss << "水位测量: " << std::fixed << std::setprecision(2) 
                << result.water_level_cm << " cm (" 
                << result.water_level_percent << "%)";
            logMessage(LogLevel::INFO, oss.str());
        }
    }
    
    return result;
}

void HCSR04Sensor::sendTriggerPulse() {
    // 确保触发引脚为低电平
    digitalWrite(config_.trig_pin, LOW);
    delayMicroseconds(2);
    
    // 发送10微秒高电平脉冲
    digitalWrite(config_.trig_pin, HIGH);
    delayMicroseconds(10);
    digitalWrite(config_.trig_pin, LOW);
}

long HCSR04Sensor::waitForEcho() {
    auto start_time = std::chrono::high_resolution_clock::now();
    auto timeout = std::chrono::microseconds(config_.timeout_us);
    
    // 等待回声引脚变为高电平
    while (digitalRead(config_.echo_pin) == LOW) {
        auto current_time = std::chrono::high_resolution_clock::now();
        if (current_time - start_time > timeout) {
            return -1;  // 超时
        }
    }
    
    auto pulse_start = std::chrono::high_resolution_clock::now();
    
    // 等待回声引脚变为低电平
    while (digitalRead(config_.echo_pin) == HIGH) {
        auto current_time = std::chrono::high_resolution_clock::now();
        if (current_time - start_time > timeout) {
            return -1;  // 超时
        }
    }
    
    auto pulse_end = std::chrono::high_resolution_clock::now();
    
    // 计算脉冲持续时间（微秒）
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
        pulse_end - pulse_start);
    
    return duration.count();
}

float HCSR04Sensor::calculateDistance(long pulse_duration) {
    // 声速约为343米/秒 = 0.0343厘米/微秒
    // 距离 = (脉冲时间 * 声速) / 2
    return (pulse_duration * 0.0343f) / 2.0f;
}

float HCSR04Sensor::calculateWaterLevel(float distance) {
    // 水位 = 最大水位 - 测量距离
    float water_level = config_.max_water_level - distance;
    return water_level > 0.0f ? water_level : 0.0f;
}

bool HCSR04Sensor::validateMeasurement(float distance) {
    return distance >= 2.0f && distance <= config_.max_distance;
}

HCSR04Result HCSR04Sensor::selfTest() {
    logMessage(LogLevel::INFO, "开始HC-SR04自检测试...");
    
    HCSR04Result result = measureDistance();
    
    if (result.success) {
        logMessage(LogLevel::INFO, "自检测试通过");
    } else {
        logMessage(LogLevel::ERROR, "自检测试失败: " + result.error_message);
    }
    
    return result;
}

HCSR04Error HCSR04Sensor::calibrate(float known_distance) {
    logMessage(LogLevel::INFO, "开始传感器校准...");
    
    HCSR04Result result = measureDistance();
    if (!result.success) {
        logMessage(LogLevel::ERROR, "校准失败: 无法获取测量值");
        return HCSR04Error::TIMEOUT_ERROR;
    }
    
    calibration_offset_ = known_distance - result.distance_cm;
    
    std::ostringstream oss;
    oss << "校准完成 - 偏移量: " << std::fixed << std::setprecision(2) 
        << calibration_offset_ << " cm";
    logMessage(LogLevel::INFO, oss.str());
    
    return HCSR04Error::OK;
}

const HCSR04Config& HCSR04Sensor::getConfig() const {
    return config_;
}

HCSR04Error HCSR04Sensor::setConfig(const HCSR04Config& config) {
    if (!validateHCSR04Config(config)) {
        return HCSR04Error::PARAM_ERROR;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    config_ = config;
    
    // 如果已初始化，需要重新初始化GPIO
    if (initialized_) {
        gpio_initialized_ = false;
        return initializeGPIO();
    }
    
    return HCSR04Error::OK;
}

bool HCSR04Sensor::isInitialized() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return initialized_;
}

std::string HCSR04Sensor::getStatusInfo() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    std::ostringstream oss;
    oss << "HC-SR04传感器状态:\n";
    oss << "  初始化状态: " << (initialized_ ? "已初始化" : "未初始化") << "\n";
    oss << "  触发引脚: " << config_.trig_pin << "\n";
    oss << "  回声引脚: " << config_.echo_pin << "\n";
    oss << "  最大距离: " << config_.max_distance << " cm\n";
    oss << "  最大水位: " << config_.max_water_level << " cm\n";
    oss << "  校准偏移: " << std::fixed << std::setprecision(2) 
        << calibration_offset_ << " cm\n";
    oss << "  总测量次数: " << total_measurements_ << "\n";
    oss << "  成功次数: " << successful_measurements_ << "\n";
    oss << "  失败次数: " << failed_measurements_ << "\n";
    
    if (total_measurements_ > 0) {
        float success_rate = (float)successful_measurements_ / total_measurements_ * 100.0f;
        oss << "  成功率: " << std::fixed << std::setprecision(1) 
            << success_rate << "%";
    }
    
    return oss.str();
}

void HCSR04Sensor::logMessage(LogLevel level, const std::string& message) {
    if (logger_) {
        logger_->log(level, message);
    }
}

// 工具函数实现
std::string hcsr04ErrorToString(HCSR04Error error) {
    switch (error) {
        case HCSR04Error::OK: return "成功";
        case HCSR04Error::INIT_ERROR: return "初始化错误";
        case HCSR04Error::GPIO_ERROR: return "GPIO错误";
        case HCSR04Error::TIMEOUT_ERROR: return "超时错误";
        case HCSR04Error::PARAM_ERROR: return "参数错误";
        case HCSR04Error::NOT_INITIALIZED: return "未初始化";
        default: return "未知错误";
    }
}

HCSR04Config createDefaultHCSR04Config() {
    return HCSR04Config{};
}

bool validateHCSR04Config(const HCSR04Config& config) {
    return config.trig_pin >= 0 && config.echo_pin >= 0 &&
           config.trig_pin != config.echo_pin &&
           config.max_distance > 0.0f &&
           config.max_water_level > 0.0f &&
           config.timeout_us > 0 &&
           config.measurement_delay_ms > 0;
}

} // namespace greenland
