# 日志模块API使用说明

**作者**: 刘旭  
**版本**: 1.0.0  
**日期**: 2024

## 📝 模块概述

日志模块是一个通用的日志记录解决方案，提供多级别日志、文件轮转、时间戳、线程安全等企业级特性。

### 🎯 主要特性

- **多级别日志**: DEBUG、INFO、WARN、ERROR、FATAL
- **多输出方式**: 控制台输出、文件输出、同时输出
- **文件轮转**: 按大小自动轮转日志文件
- **时间戳**: 自动添加精确时间戳
- **线程安全**: 支持多线程并发写入
- **格式化输出**: 支持printf风格的格式化
- **配置灵活**: 可配置日志级别、文件路径等

## 🔧 快速开始

### 1. 包含头文件

```c
#include "logger.h"
```

### 2. 基本使用流程

```c
// 1. 获取默认配置
log_config_t config;
logger_get_default_config(&config, "my_app");

// 2. 创建日志记录器
logger_t logger = logger_create(&config);

// 3. 记录日志
logger_info(logger, "应用程序启动");
logger_warn(logger, "这是一个警告信息");
logger_error(logger, "发生错误: %s", error_message);

// 4. 销毁日志记录器
logger_destroy(logger);
```

## 📊 API参考

### 配置管理

#### `logger_get_default_config()`
```c
void logger_get_default_config(log_config_t* config, const char* name);
```
获取默认日志配置。

**参数**:
- `config`: 配置结构体指针
- `name`: 日志记录器名称

**示例**:
```c
log_config_t config;
logger_get_default_config(&config, "camera_module");
```

#### `logger_create()`
```c
logger_t logger_create(const log_config_t* config);
```
创建日志记录器实例。

#### `logger_destroy()`
```c
void logger_destroy(logger_t logger);
```
销毁日志记录器并释放资源。

### 日志记录

#### 基本日志函数

```c
void logger_debug(logger_t logger, const char* format, ...);
void logger_info(logger_t logger, const char* format, ...);
void logger_warn(logger_t logger, const char* format, ...);
void logger_error(logger_t logger, const char* format, ...);
void logger_fatal(logger_t logger, const char* format, ...);
```

**示例**:
```c
logger_debug(logger, "调试信息: 变量值 = %d", value);
logger_info(logger, "用户 %s 登录成功", username);
logger_warn(logger, "内存使用率达到 %d%%", memory_usage);
logger_error(logger, "文件 %s 打开失败: %s", filename, strerror(errno));
logger_fatal(logger, "系统崩溃，错误代码: %d", error_code);
```

#### 通用日志函数

```c
void logger_log(logger_t logger, log_level_t level, const char* format, ...);
```

**示例**:
```c
logger_log(logger, LOG_LEVEL_INFO, "这是一条信息日志");
```

### 配置设置

#### `logger_set_level()`
```c
void logger_set_level(logger_t logger, log_level_t level);
```
设置日志级别，只有等于或高于此级别的日志才会被记录。

**示例**:
```c
// 只记录WARN及以上级别的日志
logger_set_level(logger, LOG_LEVEL_WARN);
```

#### `logger_enable_console()`
```c
void logger_enable_console(logger_t logger, bool enable);
```
启用或禁用控制台输出。

#### `logger_enable_file()`
```c
void logger_enable_file(logger_t logger, bool enable);
```
启用或禁用文件输出。

### 状态查询

#### `logger_get_level()`
```c
log_level_t logger_get_level(logger_t logger);
```
获取当前日志级别。

#### `logger_is_enabled()`
```c
bool logger_is_enabled(logger_t logger, log_level_t level);
```
检查指定级别的日志是否启用。

## 🎛️ 配置选项

### 日志级别
```c
typedef enum {
    LOG_LEVEL_DEBUG = 0,    // 调试信息
    LOG_LEVEL_INFO,         // 一般信息
    LOG_LEVEL_WARN,         // 警告信息
    LOG_LEVEL_ERROR,        // 错误信息
    LOG_LEVEL_FATAL         // 致命错误
} log_level_t;
```

### 配置结构
```c
typedef struct {
    char name[64];              // 日志记录器名称
    log_level_t level;          // 日志级别
    bool enable_console;        // 是否输出到控制台
    bool enable_file;           // 是否输出到文件
    char log_dir[256];          // 日志文件目录
    char log_file[256];         // 日志文件名
    size_t max_file_size;       // 最大文件大小(字节)
    int max_files;              // 最大文件数量
    bool enable_timestamp;      // 是否添加时间戳
    bool enable_thread_id;      // 是否添加线程ID
} log_config_t;
```

## 📁 日志文件结构

```
Log/
├── camera_module.log       # 当前日志文件
├── camera_module.log.1     # 轮转日志文件1
├── camera_module.log.2     # 轮转日志文件2
└── ...
```

## 🎨 日志格式

### 控制台输出格式
```
[2024-01-15 10:30:45.123] [INFO] [camera_module] 摄像头初始化成功
[2024-01-15 10:30:46.456] [WARN] [camera_module] 设备响应较慢
[2024-01-15 10:30:47.789] [ERROR] [camera_module] 拍照失败: 设备忙
```

### 文件输出格式
```
2024-01-15 10:30:45.123 [INFO] [camera_module] 摄像头初始化成功
2024-01-15 10:30:46.456 [WARN] [camera_module] 设备响应较慢  
2024-01-15 10:30:47.789 [ERROR] [camera_module] 拍照失败: 设备忙
```

## 💡 使用技巧

### 1. 条件日志记录
```c
// 只在DEBUG模式下记录详细信息
if (logger_is_enabled(logger, LOG_LEVEL_DEBUG)) {
    logger_debug(logger, "详细的调试信息: %s", complex_debug_info());
}
```

### 2. 错误处理日志
```c
int result = some_operation();
if (result != 0) {
    logger_error(logger, "操作失败: 错误代码=%d, 错误信息=%s", 
                result, get_error_string(result));
    return result;
}
logger_info(logger, "操作成功完成");
```

### 3. 性能监控日志
```c
clock_t start = clock();
perform_operation();
clock_t end = clock();

double duration = ((double)(end - start)) / CLOCKS_PER_SEC;
logger_info(logger, "操作耗时: %.3f 秒", duration);
```

### 4. 模块集成
```c
// 在模块中使用全局日志记录器
static logger_t g_logger = NULL;

void module_set_logger(logger_t logger) {
    g_logger = logger;
}

void module_function() {
    if (g_logger) {
        logger_info(g_logger, "模块函数被调用");
    }
}
```

## ⚠️ 注意事项

1. **线程安全**: 日志模块是线程安全的，可以在多线程环境中使用
2. **性能考虑**: 频繁的日志记录可能影响性能，建议在生产环境中适当调整日志级别
3. **磁盘空间**: 注意监控日志文件大小，避免占用过多磁盘空间
4. **文件权限**: 确保有写入日志目录的权限
5. **内存管理**: 使用完毕后必须调用`logger_destroy()`

## 🔧 编译要求

```makefile
# 需要链接的库
LDLIBS += -llogger -lpthread
```

## 📝 完整示例

```c
#include "logger.h"
#include <unistd.h>

int main() {
    // 创建日志配置
    log_config_t config;
    logger_get_default_config(&config, "test_app");
    config.level = LOG_LEVEL_DEBUG;
    config.enable_console = true;
    config.enable_file = true;
    
    // 创建日志记录器
    logger_t logger = logger_create(&config);
    
    // 记录不同级别的日志
    logger_debug(logger, "应用程序启动，PID: %d", getpid());
    logger_info(logger, "初始化完成");
    logger_warn(logger, "这是一个警告");
    logger_error(logger, "模拟错误: %s", "文件不存在");
    
    // 清理资源
    logger_destroy(logger);
    return 0;
}
```

---

**技术支持**: 刘旭  
**更新日期**: 2024年
