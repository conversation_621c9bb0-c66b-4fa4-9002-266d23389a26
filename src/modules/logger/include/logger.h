/**
 * @file logger.h
 * @brief 通用日志记录模块
 * <AUTHOR>
 * @date 2024
 *
 * 提供统一的日志记录功能，支持多级别日志、文件轮转、
 * 时间戳、线程安全等特性
 */

#ifndef LOGGER_H
#define LOGGER_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>
#include <stdio.h>

#ifdef __cplusplus
extern "C" {
#endif

// 日志级别定义
typedef enum {
    LOG_LEVEL_DEBUG = 0,    // 调试信息
    LOG_LEVEL_INFO,         // 一般信息
    LOG_LEVEL_WARN,         // 警告信息
    LOG_LEVEL_ERROR,        // 错误信息
    LOG_LEVEL_FATAL,        // 致命错误
    LOG_LEVEL_OFF           // 关闭日志
} log_level_t;

// 日志配置结构
typedef struct {
    char log_dir[256];          // 日志目录
    char module_name[64];       // 模块名称
    log_level_t level;          // 日志级别
    bool enable_console;        // 是否输出到控制台
    bool enable_file;           // 是否输出到文件
    bool enable_timestamp;      // 是否包含时间戳
    bool enable_thread_id;      // 是否包含线程ID
    size_t max_file_size;       // 最大文件大小 (字节)
    int max_file_count;         // 最大文件数量
} log_config_t;

// 日志句柄
typedef struct logger_handle* logger_t;

// 错误码
typedef enum {
    LOG_OK = 0,
    LOG_ERROR_PARAM,
    LOG_ERROR_INIT,
    LOG_ERROR_FILE,
    LOG_ERROR_MEMORY,
    LOG_ERROR_NOT_INIT
} log_error_t;

/**
 * @brief 获取默认日志配置
 * @param config 配置结构指针
 * @param module_name 模块名称
 */
void logger_get_default_config(log_config_t* config, const char* module_name);

/**
 * @brief 创建日志记录器
 * @param config 日志配置
 * @return 日志句柄，失败返回NULL
 */
logger_t logger_create(const log_config_t* config);

/**
 * @brief 销毁日志记录器
 * @param logger 日志句柄
 */
void logger_destroy(logger_t logger);

/**
 * @brief 设置日志级别
 * @param logger 日志句柄
 * @param level 日志级别
 * @return 错误码
 */
int logger_set_level(logger_t logger, log_level_t level);

/**
 * @brief 获取日志级别
 * @param logger 日志句柄
 * @return 当前日志级别
 */
log_level_t logger_get_level(logger_t logger);

/**
 * @brief 写入日志 (格式化)
 * @param logger 日志句柄
 * @param level 日志级别
 * @param format 格式字符串
 * @param ... 可变参数
 * @return 错误码
 */
int logger_log(logger_t logger, log_level_t level, const char* format, ...);

/**
 * @brief 写入调试日志
 * @param logger 日志句柄
 * @param format 格式字符串
 * @param ... 可变参数
 * @return 错误码
 */
int logger_debug(logger_t logger, const char* format, ...);

/**
 * @brief 写入信息日志
 * @param logger 日志句柄
 * @param format 格式字符串
 * @param ... 可变参数
 * @return 错误码
 */
int logger_info(logger_t logger, const char* format, ...);

/**
 * @brief 写入警告日志
 * @param logger 日志句柄
 * @param format 格式字符串
 * @param ... 可变参数
 * @return 错误码
 */
int logger_warn(logger_t logger, const char* format, ...);

/**
 * @brief 写入错误日志
 * @param logger 日志句柄
 * @param format 格式字符串
 * @param ... 可变参数
 * @return 错误码
 */
int logger_error(logger_t logger, const char* format, ...);

/**
 * @brief 写入致命错误日志
 * @param logger 日志句柄
 * @param format 格式字符串
 * @param ... 可变参数
 * @return 错误码
 */
int logger_fatal(logger_t logger, const char* format, ...);

/**
 * @brief 刷新日志缓冲区
 * @param logger 日志句柄
 * @return 错误码
 */
int logger_flush(logger_t logger);

/**
 * @brief 轮转日志文件
 * @param logger 日志句柄
 * @return 错误码
 */
int logger_rotate(logger_t logger);

/**
 * @brief 获取日志级别字符串
 * @param level 日志级别
 * @return 级别字符串
 */
const char* logger_level_to_string(log_level_t level);

/**
 * @brief 从字符串解析日志级别
 * @param level_str 级别字符串
 * @return 日志级别
 */
log_level_t logger_level_from_string(const char* level_str);

/**
 * @brief 获取日志统计信息
 * @param logger 日志句柄
 * @param info 统计信息缓冲区
 * @param size 缓冲区大小
 * @return 错误码
 */
int logger_get_stats(logger_t logger, char* info, size_t size);

// 便利宏定义
#define LOG_DEBUG(logger, ...) logger_debug(logger, __VA_ARGS__)
#define LOG_INFO(logger, ...)  logger_info(logger, __VA_ARGS__)
#define LOG_WARN(logger, ...)  logger_warn(logger, __VA_ARGS__)
#define LOG_ERROR(logger, ...) logger_error(logger, __VA_ARGS__)
#define LOG_FATAL(logger, ...) logger_fatal(logger, __VA_ARGS__)

#ifdef __cplusplus
}
#endif

#endif // LOGGER_H
