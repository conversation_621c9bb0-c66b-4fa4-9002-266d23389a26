/**
 * @file logger.h
 * @brief 通用日志记录模块 - C++面向对象版本
 * <AUTHOR>
 * @date 2024
 *
 * 提供统一的日志记录功能，支持多级别日志、文件轮转、
 * 时间戳、线程安全等特性
 */

#ifndef LOGGER_H
#define LOGGER_H

#include <string>
#include <memory>
#include <mutex>
#include <fstream>
#include <cstdint>

namespace greenland {

// 日志级别枚举
enum class LogLevel {
    DEBUG = 0,
    INFO,
    WARN,
    ERROR,
    FATAL,
    OFF
};

// 日志配置结构
struct LogConfig {
    std::string log_dir = "Log";
    std::string module_name = "default";
    LogLevel level = LogLevel::INFO;
    bool enable_console = true;
    bool enable_file = true;
    bool enable_timestamp = true;
    bool enable_thread_id = false;
    size_t max_file_size = 10 * 1024 * 1024;  // 10MB
    int max_file_count = 5;
};

// 错误码枚举
enum class LogError {
    OK = 0,
    PARAM_ERROR,
    INIT_ERROR,
    FILE_ERROR,
    MEMORY_ERROR,
    NOT_INITIALIZED
};

/**
 * @brief Logger类 - 线程安全的日志记录器
 */
class Logger {
public:
    /**
     * @brief 构造函数
     * @param config 日志配置
     */
    explicit Logger(const LogConfig& config);

    /**
     * @brief 析构函数
     */
    ~Logger();

    /**
     * @brief 禁用拷贝构造和赋值
     */
    Logger(const Logger&) = delete;
    Logger& operator=(const Logger&) = delete;

    /**
     * @brief 设置日志级别
     * @param level 日志级别
     * @return 错误码
     */
    LogError setLevel(LogLevel level);

    /**
     * @brief 获取日志级别
     * @return 当前日志级别
     */
    LogLevel getLevel() const;

    /**
     * @brief 写入调试日志
     * @param format 格式字符串
     * @param args 可变参数
     */
    template<typename... Args>
    void debug(const std::string& format, Args... args);

    /**
     * @brief 写入信息日志
     * @param format 格式字符串
     * @param args 可变参数
     */
    template<typename... Args>
    void info(const std::string& format, Args... args);

    /**
     * @brief 写入警告日志
     * @param format 格式字符串
     * @param args 可变参数
     */
    template<typename... Args>
    void warn(const std::string& format, Args... args);

    /**
     * @brief 写入错误日志
     * @param format 格式字符串
     * @param args 可变参数
     */
    template<typename... Args>
    void error(const std::string& format, Args... args);

    /**
     * @brief 写入致命错误日志
     * @param format 格式字符串
     * @param args 可变参数
     */
    template<typename... Args>
    void fatal(const std::string& format, Args... args);

    /**
     * @brief 写入日志
     * @param level 日志级别
     * @param message 日志消息
     * @return 错误码
     */
    LogError log(LogLevel level, const std::string& message);

    /**
     * @brief 刷新日志缓冲区
     */
    void flush();

    /**
     * @brief 获取模块名称
     * @return 模块名称
     */
    const std::string& getModuleName() const;

private:
    /**
     * @brief 初始化日志器
     * @return 错误码
     */
    LogError initialize();

    /**
     * @brief 写入日志到文件
     * @param level 日志级别
     * @param message 日志消息
     */
    void writeToFile(LogLevel level, const std::string& message);

    /**
     * @brief 写入日志到控制台
     * @param level 日志级别
     * @param message 日志消息
     */
    void writeToConsole(LogLevel level, const std::string& message);

    /**
     * @brief 格式化日志消息
     * @param level 日志级别
     * @param message 原始消息
     * @return 格式化后的消息
     */
    std::string formatMessage(LogLevel level, const std::string& message);

    /**
     * @brief 获取时间戳字符串
     * @return 时间戳字符串
     */
    std::string getTimestamp();

    /**
     * @brief 获取日志级别字符串
     * @param level 日志级别
     * @return 级别字符串
     */
    std::string getLevelString(LogLevel level);

    /**
     * @brief 检查并轮转日志文件
     */
    void rotateLogFile();

private:
    LogConfig config_;
    std::mutex mutex_;
    std::ofstream file_stream_;
    bool initialized_;
    std::string current_log_file_;
};

/**
 * @brief 写入调试日志
 * @param logger 日志句柄
 * @param format 格式字符串
 * @param ... 可变参数
 * @return 错误码
 */
int logger_debug(logger_t logger, const char* format, ...);

/**
 * @brief 写入信息日志
 * @param logger 日志句柄
 * @param format 格式字符串
 * @param ... 可变参数
 * @return 错误码
 */
int logger_info(logger_t logger, const char* format, ...);

/**
 * @brief 写入警告日志
 * @param logger 日志句柄
 * @param format 格式字符串
 * @param ... 可变参数
 * @return 错误码
 */
int logger_warn(logger_t logger, const char* format, ...);

/**
 * @brief 写入错误日志
 * @param logger 日志句柄
 * @param format 格式字符串
 * @param ... 可变参数
 * @return 错误码
 */
int logger_error(logger_t logger, const char* format, ...);

/**
 * @brief 写入致命错误日志
 * @param logger 日志句柄
 * @param format 格式字符串
 * @param ... 可变参数
 * @return 错误码
 */
int logger_fatal(logger_t logger, const char* format, ...);

/**
 * @brief 刷新日志缓冲区
 * @param logger 日志句柄
 * @return 错误码
 */
int logger_flush(logger_t logger);

/**
 * @brief 轮转日志文件
 * @param logger 日志句柄
 * @return 错误码
 */
int logger_rotate(logger_t logger);

/**
 * @brief 获取日志级别字符串
 * @param level 日志级别
 * @return 级别字符串
 */
const char* logger_level_to_string(log_level_t level);

/**
 * @brief 从字符串解析日志级别
 * @param level_str 级别字符串
 * @return 日志级别
 */
log_level_t logger_level_from_string(const char* level_str);

/**
 * @brief 获取日志统计信息
 * @param logger 日志句柄
 * @param info 统计信息缓冲区
 * @param size 缓冲区大小
 * @return 错误码
 */
int logger_get_stats(logger_t logger, char* info, size_t size);

// 便利宏定义
#define LOG_DEBUG(logger, ...) logger_debug(logger, __VA_ARGS__)
#define LOG_INFO(logger, ...)  logger_info(logger, __VA_ARGS__)
#define LOG_WARN(logger, ...)  logger_warn(logger, __VA_ARGS__)
#define LOG_ERROR(logger, ...) logger_error(logger, __VA_ARGS__)
#define LOG_FATAL(logger, ...) logger_fatal(logger, __VA_ARGS__)

#ifdef __cplusplus
}
#endif

#endif // LOGGER_H
