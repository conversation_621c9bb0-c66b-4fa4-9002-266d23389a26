/**
 * @file logger.h
 * @brief 通用日志记录模块 - C++面向对象版本，支持C/C++兼容
 * <AUTHOR>
 * @date 2024
 *
 * 提供统一的日志记录功能，支持多级别日志、文件轮转、
 * 时间戳、线程安全等特性
 */

#ifndef LOGGER_H
#define LOGGER_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

// 日志级别枚举
typedef enum {
    LOG_LEVEL_DEBUG = 0,
    LOG_LEVEL_INFO,
    LOG_LEVEL_WARN,
    LOG_LEVEL_ERROR,
    LOG_LEVEL_FATAL,
    LOG_LEVEL_OFF
} log_level_t;

// 日志配置结构
typedef struct {
    char log_dir[256];
    char module_name[64];
    log_level_t level;
    bool enable_console;
    bool enable_file;
    bool enable_timestamp;
    bool enable_thread_id;
    size_t max_file_size;
    int max_file_count;
} log_config_t;

// 错误码枚举
typedef enum {
    LOG_OK = 0,
    LOG_ERROR_PARAM,
    LOG_ERROR_INIT,
    LOG_ERROR_FILE,
    LOG_ERROR_MEMORY,
    LOG_ERROR_NOT_INIT
} log_error_t;

// 日志句柄
typedef struct logger_handle* logger_t;

/**
 * @brief 获取默认日志配置
 * @param config 配置结构指针
 * @param module_name 模块名称
 */
void logger_get_default_config(log_config_t* config, const char* module_name);

/**
 * @brief 创建日志记录器
 * @param config 日志配置
 * @return 日志句柄，失败返回NULL
 */
logger_t logger_create(const log_config_t* config);

/**
 * @brief 销毁日志记录器
 * @param logger 日志句柄
 */
void logger_destroy(logger_t logger);

/**
 * @brief 设置日志级别
 * @param logger 日志句柄
 * @param level 日志级别
 * @return 错误码
 */
int logger_set_level(logger_t logger, log_level_t level);

/**
 * @brief 获取日志级别
 * @param logger 日志句柄
 * @return 当前日志级别
 */
log_level_t logger_get_level(logger_t logger);

/**
 * @brief 写入日志 (格式化)
 * @param logger 日志句柄
 * @param level 日志级别
 * @param format 格式字符串
 * @param ... 可变参数
 * @return 错误码
 */
int logger_log(logger_t logger, log_level_t level, const char* format, ...);

/**
 * @brief 写入调试日志
 * @param logger 日志句柄
 * @param format 格式字符串
 * @param ... 可变参数
 * @return 错误码
 */
int logger_debug(logger_t logger, const char* format, ...);

/**
 * @brief 写入信息日志
 * @param logger 日志句柄
 * @param format 格式字符串
 * @param ... 可变参数
 * @return 错误码
 */
int logger_info(logger_t logger, const char* format, ...);

/**
 * @brief 写入警告日志
 * @param logger 日志句柄
 * @param format 格式字符串
 * @param ... 可变参数
 * @return 错误码
 */
int logger_warn(logger_t logger, const char* format, ...);

/**
 * @brief 写入错误日志
 * @param logger 日志句柄
 * @param format 格式字符串
 * @param ... 可变参数
 * @return 错误码
 */
int logger_error(logger_t logger, const char* format, ...);

/**
 * @brief 写入致命错误日志
 * @param logger 日志句柄
 * @param format 格式字符串
 * @param ... 可变参数
 * @return 错误码
 */
int logger_fatal(logger_t logger, const char* format, ...);

/**
 * @brief 刷新日志缓冲区
 * @param logger 日志句柄
 * @return 错误码
 */
int logger_flush(logger_t logger);

/**
 * @brief 获取模块名称
 * @param logger 日志句柄
 * @return 模块名称字符串，失败返回NULL
 */
const char* logger_get_module_name(logger_t logger);

/**
 * @brief 错误码转字符串
 * @param error 错误码
 * @return 错误描述字符串
 */
const char* logger_error_to_string(log_error_t error);

/**
 * @brief 日志级别转字符串
 * @param level 日志级别
 * @return 级别字符串
 */
const char* logger_level_to_string(log_level_t level);

// 便利宏定义
#define LOG_DEBUG(logger, format, ...) logger_debug(logger, format, ##__VA_ARGS__)
#define LOG_INFO(logger, format, ...)  logger_info(logger, format, ##__VA_ARGS__)
#define LOG_WARN(logger, format, ...)  logger_warn(logger, format, ##__VA_ARGS__)
#define LOG_ERROR(logger, format, ...) logger_error(logger, format, ##__VA_ARGS__)
#define LOG_FATAL(logger, format, ...) logger_fatal(logger, format, ##__VA_ARGS__)

#ifdef __cplusplus
}

// C++接口
#include <string>
#include <memory>

namespace greenland {

// C++包装类
class Logger {
public:
    explicit Logger(const std::string& module_name);
    explicit Logger(const log_config_t& config);
    ~Logger();
    
    // 禁用拷贝
    Logger(const Logger&) = delete;
    Logger& operator=(const Logger&) = delete;
    
    // 日志方法
    void debug(const std::string& message);
    void info(const std::string& message);
    void warn(const std::string& message);
    void error(const std::string& message);
    void fatal(const std::string& message);
    
    // 格式化日志方法
    void debugf(const char* format, ...);
    void infof(const char* format, ...);
    void warnf(const char* format, ...);
    void errorf(const char* format, ...);
    void fatalf(const char* format, ...);
    
    // 设置和获取
    void setLevel(log_level_t level);
    log_level_t getLevel() const;
    std::string getModuleName() const;
    
    // 获取C句柄（用于与C代码交互）
    logger_t getHandle() const { return logger_; }

private:
    logger_t logger_;
};

} // namespace greenland

#endif // __cplusplus

#endif // LOGGER_H
