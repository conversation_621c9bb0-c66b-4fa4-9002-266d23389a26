/**
 * @file logger.hpp
 * @brief 通用日志记录模块 - C++面向对象版本
 * <AUTHOR>
 * @date 2024
 *
 * 提供统一的日志记录功能，支持多级别日志、文件轮转、
 * 时间戳、线程安全等特性
 */

#ifndef LOGGER_HPP
#define LOGGER_HPP

#include <string>
#include <memory>
#include <mutex>
#include <fstream>
#include <cstdint>
#include <sstream>
#include <iomanip>

namespace greenland {

// 日志级别枚举
enum class LogLevel {
    DEBUG = 0,
    INFO,
    WARN,
    ERROR,
    FATAL,
    OFF
};

// 日志配置结构
struct LogConfig {
    std::string log_dir = "Log";
    std::string module_name = "default";
    LogLevel level = LogLevel::INFO;
    bool enable_console = true;
    bool enable_file = true;
    bool enable_timestamp = true;
    bool enable_thread_id = false;
    size_t max_file_size = 10 * 1024 * 1024;  // 10MB
    int max_file_count = 5;
};

// 错误码枚举
enum class LogError {
    OK = 0,
    PARAM_ERROR,
    INIT_ERROR,
    FILE_ERROR,
    MEMORY_ERROR,
    NOT_INITIALIZED
};

/**
 * @brief Logger类 - 线程安全的日志记录器
 */
class Logger {
public:
    /**
     * @brief 构造函数
     * @param config 日志配置
     */
    explicit Logger(const LogConfig& config);
    
    /**
     * @brief 析构函数
     */
    ~Logger();
    
    /**
     * @brief 禁用拷贝构造和赋值
     */
    Logger(const Logger&) = delete;
    Logger& operator=(const Logger&) = delete;
    
    /**
     * @brief 设置日志级别
     * @param level 日志级别
     * @return 错误码
     */
    LogError setLevel(LogLevel level);
    
    /**
     * @brief 获取日志级别
     * @return 当前日志级别
     */
    LogLevel getLevel() const;
    
    /**
     * @brief 写入调试日志
     * @param message 日志消息
     */
    void debug(const std::string& message);
    
    /**
     * @brief 写入信息日志
     * @param message 日志消息
     */
    void info(const std::string& message);
    
    /**
     * @brief 写入警告日志
     * @param message 日志消息
     */
    void warn(const std::string& message);
    
    /**
     * @brief 写入错误日志
     * @param message 日志消息
     */
    void error(const std::string& message);
    
    /**
     * @brief 写入致命错误日志
     * @param message 日志消息
     */
    void fatal(const std::string& message);
    
    /**
     * @brief 写入日志
     * @param level 日志级别
     * @param message 日志消息
     * @return 错误码
     */
    LogError log(LogLevel level, const std::string& message);
    
    /**
     * @brief 刷新日志缓冲区
     */
    void flush();
    
    /**
     * @brief 获取模块名称
     * @return 模块名称
     */
    const std::string& getModuleName() const;

private:
    /**
     * @brief 初始化日志器
     * @return 错误码
     */
    LogError initialize();
    
    /**
     * @brief 写入日志到文件
     * @param level 日志级别
     * @param message 日志消息
     */
    void writeToFile(LogLevel level, const std::string& message);
    
    /**
     * @brief 写入日志到控制台
     * @param level 日志级别
     * @param message 日志消息
     */
    void writeToConsole(LogLevel level, const std::string& message);
    
    /**
     * @brief 格式化日志消息
     * @param level 日志级别
     * @param message 原始消息
     * @return 格式化后的消息
     */
    std::string formatMessage(LogLevel level, const std::string& message);
    
    /**
     * @brief 获取时间戳字符串
     * @return 时间戳字符串
     */
    std::string getTimestamp();
    
    /**
     * @brief 获取日志级别字符串
     * @param level 日志级别
     * @return 级别字符串
     */
    std::string getLevelString(LogLevel level);
    
    /**
     * @brief 检查并轮转日志文件
     */
    void rotateLogFile();
    
    /**
     * @brief 创建日志目录
     */
    void createLogDirectory();

private:
    LogConfig config_;
    mutable std::mutex mutex_;
    std::ofstream file_stream_;
    bool initialized_;
    std::string current_log_file_;
};

/**
 * @brief 工具函数：错误码转字符串
 * @param error 错误码
 * @return 错误描述字符串
 */
std::string logErrorToString(LogError error);

/**
 * @brief 工具函数：日志级别转字符串
 * @param level 日志级别
 * @return 级别字符串
 */
std::string logLevelToString(LogLevel level);

/**
 * @brief 创建默认配置
 * @param module_name 模块名称
 * @return 默认配置
 */
LogConfig createDefaultConfig(const std::string& module_name);

// 便利宏定义
#define LOG_DEBUG(logger, message) (logger)->debug(message)
#define LOG_INFO(logger, message)  (logger)->info(message)
#define LOG_WARN(logger, message)  (logger)->warn(message)
#define LOG_ERROR(logger, message) (logger)->error(message)
#define LOG_FATAL(logger, message) (logger)->fatal(message)

} // namespace greenland

#endif // LOGGER_HPP
