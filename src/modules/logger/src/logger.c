/**
 * @file logger.c
 * @brief 通用日志记录模块实现
 * <AUTHOR>
 * @date 2024
 */

#include "logger.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <strings.h>  // for strcasecmp
#include <stdarg.h>
#include <time.h>
#include <sys/stat.h>
#include <unistd.h>
#include <pthread.h>
#include <errno.h>

// 日志句柄结构
struct logger_handle {
    log_config_t config;
    FILE* file;
    pthread_mutex_t mutex;
    uint64_t log_count;
    uint64_t error_count;
    size_t current_file_size;
    bool initialized;
};

// 日志级别字符串
static const char* level_strings[] = {
    "DEBUG", "INFO", "WARN", "ERROR", "FATAL", "OFF"
};

// 日志级别颜色 (ANSI)
static const char* level_colors[] = {
    "\033[36m",  // DEBUG - 青色
    "\033[32m",  // INFO  - 绿色
    "\033[33m",  // WARN  - 黄色
    "\033[31m",  // ERROR - 红色
    "\033[35m",  // FATAL - 紫色
    "\033[0m"    // OFF   - 无色
};

#define COLOR_RESET "\033[0m"

/**
 * @brief 创建目录
 */
static int create_directory(const char* path) {
    struct stat st = {0};
    if (stat(path, &st) == -1) {
        if (mkdir(path, 0755) == -1) {
            return -1;
        }
    }
    return 0;
}

/**
 * @brief 获取当前时间字符串
 */
static void get_timestamp(char* buffer, size_t size) {
    time_t now = time(NULL);
    struct tm* tm_info = localtime(&now);
    strftime(buffer, size, "%Y-%m-%d %H:%M:%S", tm_info);
}

/**
 * @brief 获取线程ID
 */
static unsigned long get_thread_id(void) {
    return (unsigned long)pthread_self();
}

/**
 * @brief 获取日志文件路径
 */
static void get_log_file_path(const logger_t logger, char* path, size_t size) {
    snprintf(path, size, "%s/%s.log",
             logger->config.log_dir, logger->config.module_name);
}

/**
 * @brief 轮转日志文件
 */
static int rotate_log_file(logger_t logger) {
    if (!logger || !logger->file) {
        return LOG_ERROR_PARAM;
    }

    fclose(logger->file);
    logger->file = NULL;

    char old_path[512], new_path[512];
    get_log_file_path(logger, old_path, sizeof(old_path));

    // 轮转现有文件
    for (int i = logger->config.max_file_count - 1; i > 0; i--) {
        snprintf(old_path, sizeof(old_path), "%s/%s.log.%d",
                 logger->config.log_dir, logger->config.module_name, i - 1);
        snprintf(new_path, sizeof(new_path), "%s/%s.log.%d",
                 logger->config.log_dir, logger->config.module_name, i);
        rename(old_path, new_path);
    }

    // 重命名当前文件
    get_log_file_path(logger, old_path, sizeof(old_path));
    snprintf(new_path, sizeof(new_path), "%s/%s.log.0",
             logger->config.log_dir, logger->config.module_name);
    rename(old_path, new_path);

    // 创建新文件
    logger->file = fopen(old_path, "w");
    if (!logger->file) {
        return LOG_ERROR_FILE;
    }

    logger->current_file_size = 0;
    return LOG_OK;
}

/**
 * @brief 获取默认日志配置
 */
void logger_get_default_config(log_config_t* config, const char* module_name) {
    if (!config || !module_name) return;

    strncpy(config->log_dir, "Log", sizeof(config->log_dir) - 1);
    strncpy(config->module_name, module_name, sizeof(config->module_name) - 1);
    config->level = LOG_LEVEL_INFO;
    config->enable_console = true;
    config->enable_file = true;
    config->enable_timestamp = true;
    config->enable_thread_id = false;
    config->max_file_size = 10 * 1024 * 1024;  // 10MB
    config->max_file_count = 5;
}

/**
 * @brief 创建日志记录器
 */
logger_t logger_create(const log_config_t* config) {
    if (!config) {
        return NULL;
    }

    logger_t logger = malloc(sizeof(struct logger_handle));
    if (!logger) {
        return NULL;
    }

    memset(logger, 0, sizeof(struct logger_handle));
    memcpy(&logger->config, config, sizeof(log_config_t));

    // 初始化互斥锁
    if (pthread_mutex_init(&logger->mutex, NULL) != 0) {
        free(logger);
        return NULL;
    }

    // 创建日志目录
    if (logger->config.enable_file) {
        if (create_directory(logger->config.log_dir) != 0) {
            pthread_mutex_destroy(&logger->mutex);
            free(logger);
            return NULL;
        }

        // 打开日志文件
        char log_path[512];
        get_log_file_path(logger, log_path, sizeof(log_path));
        logger->file = fopen(log_path, "a");
        if (!logger->file) {
            pthread_mutex_destroy(&logger->mutex);
            free(logger);
            return NULL;
        }

        // 获取当前文件大小
        fseek(logger->file, 0, SEEK_END);
        logger->current_file_size = ftell(logger->file);
    }

    logger->initialized = true;
    return logger;
}

/**
 * @brief 销毁日志记录器
 */
void logger_destroy(logger_t logger) {
    if (!logger) return;

    pthread_mutex_lock(&logger->mutex);

    if (logger->file) {
        fclose(logger->file);
        logger->file = NULL;
    }

    logger->initialized = false;
    pthread_mutex_unlock(&logger->mutex);
    pthread_mutex_destroy(&logger->mutex);

    free(logger);
}

/**
 * @brief 设置日志级别
 */
int logger_set_level(logger_t logger, log_level_t level) {
    if (!logger || !logger->initialized) {
        return LOG_ERROR_NOT_INIT;
    }

    if (level < LOG_LEVEL_DEBUG || level > LOG_LEVEL_OFF) {
        return LOG_ERROR_PARAM;
    }

    pthread_mutex_lock(&logger->mutex);
    logger->config.level = level;
    pthread_mutex_unlock(&logger->mutex);

    return LOG_OK;
}

/**
 * @brief 获取日志级别
 */
log_level_t logger_get_level(logger_t logger) {
    if (!logger || !logger->initialized) {
        return LOG_LEVEL_OFF;
    }

    pthread_mutex_lock(&logger->mutex);
    log_level_t level = logger->config.level;
    pthread_mutex_unlock(&logger->mutex);

    return level;
}

/**
 * @brief 写入日志
 */
int logger_log(logger_t logger, log_level_t level, const char* format, ...) {
    if (!logger || !logger->initialized || !format) {
        return LOG_ERROR_PARAM;
    }

    // 检查日志级别
    if (level < logger->config.level) {
        return LOG_OK;
    }

    pthread_mutex_lock(&logger->mutex);

    char timestamp[32] = {0};
    char message[1024];
    char full_message[1200];

    // 格式化消息
    va_list args;
    va_start(args, format);
    vsnprintf(message, sizeof(message), format, args);
    va_end(args);

    // 构建完整消息
    if (logger->config.enable_timestamp) {
        get_timestamp(timestamp, sizeof(timestamp));
    }

    if (logger->config.enable_thread_id) {
        snprintf(full_message, sizeof(full_message), "[%s] [%s] [%lu] %s\n",
                 timestamp, level_strings[level], get_thread_id(), message);
    } else {
        snprintf(full_message, sizeof(full_message), "[%s] [%s] %s\n",
                 timestamp, level_strings[level], message);
    }

    // 输出到控制台
    if (logger->config.enable_console) {
        printf("%s%s%s", level_colors[level], full_message, COLOR_RESET);
        fflush(stdout);
    }

    // 输出到文件
    if (logger->config.enable_file && logger->file) {
        size_t written = fprintf(logger->file, "%s", full_message);
        fflush(logger->file);

        logger->current_file_size += written;

        // 检查是否需要轮转
        if (logger->current_file_size >= logger->config.max_file_size) {
            rotate_log_file(logger);
        }
    }

    logger->log_count++;
    if (level >= LOG_LEVEL_ERROR) {
        logger->error_count++;
    }

    pthread_mutex_unlock(&logger->mutex);
    return LOG_OK;
}

/**
 * @brief 写入调试日志
 */
int logger_debug(logger_t logger, const char* format, ...) {
    va_list args;
    va_start(args, format);
    char message[1024];
    vsnprintf(message, sizeof(message), format, args);
    va_end(args);
    return logger_log(logger, LOG_LEVEL_DEBUG, "%s", message);
}

/**
 * @brief 写入信息日志
 */
int logger_info(logger_t logger, const char* format, ...) {
    va_list args;
    va_start(args, format);
    char message[1024];
    vsnprintf(message, sizeof(message), format, args);
    va_end(args);
    return logger_log(logger, LOG_LEVEL_INFO, "%s", message);
}

/**
 * @brief 写入警告日志
 */
int logger_warn(logger_t logger, const char* format, ...) {
    va_list args;
    va_start(args, format);
    char message[1024];
    vsnprintf(message, sizeof(message), format, args);
    va_end(args);
    return logger_log(logger, LOG_LEVEL_WARN, "%s", message);
}

/**
 * @brief 写入错误日志
 */
int logger_error(logger_t logger, const char* format, ...) {
    va_list args;
    va_start(args, format);
    char message[1024];
    vsnprintf(message, sizeof(message), format, args);
    va_end(args);
    return logger_log(logger, LOG_LEVEL_ERROR, "%s", message);
}

/**
 * @brief 写入致命错误日志
 */
int logger_fatal(logger_t logger, const char* format, ...) {
    va_list args;
    va_start(args, format);
    char message[1024];
    vsnprintf(message, sizeof(message), format, args);
    va_end(args);
    return logger_log(logger, LOG_LEVEL_FATAL, "%s", message);
}

/**
 * @brief 刷新日志缓冲区
 */
int logger_flush(logger_t logger) {
    if (!logger || !logger->initialized) {
        return LOG_ERROR_NOT_INIT;
    }

    pthread_mutex_lock(&logger->mutex);
    if (logger->file) {
        fflush(logger->file);
    }
    pthread_mutex_unlock(&logger->mutex);

    return LOG_OK;
}

/**
 * @brief 轮转日志文件
 */
int logger_rotate(logger_t logger) {
    if (!logger || !logger->initialized) {
        return LOG_ERROR_NOT_INIT;
    }

    pthread_mutex_lock(&logger->mutex);
    int result = rotate_log_file(logger);
    pthread_mutex_unlock(&logger->mutex);

    return result;
}

/**
 * @brief 获取日志级别字符串
 */
const char* logger_level_to_string(log_level_t level) {
    if (level < LOG_LEVEL_DEBUG || level > LOG_LEVEL_OFF) {
        return "UNKNOWN";
    }
    return level_strings[level];
}

/**
 * @brief 从字符串解析日志级别
 */
log_level_t logger_level_from_string(const char* level_str) {
    if (!level_str) return LOG_LEVEL_INFO;

    for (int i = 0; i <= LOG_LEVEL_OFF; i++) {
        if (strcasecmp(level_str, level_strings[i]) == 0) {
            return (log_level_t)i;
        }
    }
    return LOG_LEVEL_INFO;
}

/**
 * @brief 获取日志统计信息
 */
int logger_get_stats(logger_t logger, char* info, size_t size) {
    if (!logger || !logger->initialized || !info || size == 0) {
        return LOG_ERROR_PARAM;
    }

    pthread_mutex_lock(&logger->mutex);

    snprintf(info, size,
        "日志统计信息 [%s]:\n"
        "  模块名称: %s\n"
        "  日志级别: %s\n"
        "  总日志数: %lu\n"
        "  错误日志数: %lu\n"
        "  当前文件大小: %zu 字节\n"
        "  控制台输出: %s\n"
        "  文件输出: %s\n"
        "  日志目录: %s",
        logger->config.module_name,
        logger->config.module_name,
        level_strings[logger->config.level],
        logger->log_count,
        logger->error_count,
        logger->current_file_size,
        logger->config.enable_console ? "启用" : "禁用",
        logger->config.enable_file ? "启用" : "禁用",
        logger->config.log_dir
    );

    pthread_mutex_unlock(&logger->mutex);
    return LOG_OK;
}
