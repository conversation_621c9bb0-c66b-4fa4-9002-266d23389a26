/**
 * @file logger.cpp
 * @brief 通用日志记录模块实现 - C++面向对象版本
 * <AUTHOR>
 * @date 2024
 */

#include "logger.hpp"
#include <iostream>
#include <chrono>
#include <thread>
#include <filesystem>
#include <ctime>

namespace greenland {

Logger::Logger(const LogConfig& config) 
    : config_(config), initialized_(false) {
    initialize();
}

Logger::~Logger() {
    if (file_stream_.is_open()) {
        file_stream_.close();
    }
}

LogError Logger::initialize() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (config_.enable_file) {
        createLogDirectory();
        
        // 生成日志文件名
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto tm = *std::localtime(&time_t);
        
        std::ostringstream oss;
        oss << config_.log_dir << "/" << config_.module_name << "_"
            << std::put_time(&tm, "%Y%m%d_%H%M%S") << ".log";
        current_log_file_ = oss.str();
        
        file_stream_.open(current_log_file_, std::ios::app);
        if (!file_stream_.is_open()) {
            return LogError::FILE_ERROR;
        }
    }
    
    initialized_ = true;
    return LogError::OK;
}

void Logger::createLogDirectory() {
    try {
        std::filesystem::create_directories(config_.log_dir);
    } catch (const std::exception& e) {
        std::cerr << "Failed to create log directory: " << e.what() << std::endl;
    }
}

LogError Logger::setLevel(LogLevel level) {
    std::lock_guard<std::mutex> lock(mutex_);
    config_.level = level;
    return LogError::OK;
}

LogLevel Logger::getLevel() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return config_.level;
}

void Logger::debug(const std::string& message) {
    if (config_.level <= LogLevel::DEBUG) {
        log(LogLevel::DEBUG, message);
    }
}

void Logger::info(const std::string& message) {
    if (config_.level <= LogLevel::INFO) {
        log(LogLevel::INFO, message);
    }
}

void Logger::warn(const std::string& message) {
    if (config_.level <= LogLevel::WARN) {
        log(LogLevel::WARN, message);
    }
}

void Logger::error(const std::string& message) {
    if (config_.level <= LogLevel::ERROR) {
        log(LogLevel::ERROR, message);
    }
}

void Logger::fatal(const std::string& message) {
    if (config_.level <= LogLevel::FATAL) {
        log(LogLevel::FATAL, message);
    }
}

LogError Logger::log(LogLevel level, const std::string& message) {
    if (!initialized_) {
        return LogError::NOT_INITIALIZED;
    }
    
    if (level < config_.level) {
        return LogError::OK;
    }
    
    std::string formatted_message = formatMessage(level, message);
    
    if (config_.enable_console) {
        writeToConsole(level, formatted_message);
    }
    
    if (config_.enable_file) {
        writeToFile(level, formatted_message);
    }
    
    return LogError::OK;
}

void Logger::writeToConsole(LogLevel level, const std::string& message) {
    // 根据日志级别使用不同颜色
    std::string color_code;
    switch (level) {
        case LogLevel::DEBUG: color_code = "\033[36m"; break; // 青色
        case LogLevel::INFO:  color_code = "\033[32m"; break; // 绿色
        case LogLevel::WARN:  color_code = "\033[33m"; break; // 黄色
        case LogLevel::ERROR: color_code = "\033[31m"; break; // 红色
        case LogLevel::FATAL: color_code = "\033[35m"; break; // 紫色
        default: color_code = "\033[0m"; break;
    }
    
    std::cout << color_code << message << "\033[0m" << std::endl;
}

void Logger::writeToFile(LogLevel level, const std::string& message) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!file_stream_.is_open()) {
        return;
    }
    
    // 检查文件大小并轮转
    rotateLogFile();
    
    file_stream_ << message << std::endl;
    file_stream_.flush();
}

std::string Logger::formatMessage(LogLevel level, const std::string& message) {
    std::ostringstream oss;
    
    if (config_.enable_timestamp) {
        oss << "[" << getTimestamp() << "] ";
    }
    
    oss << "[" << getLevelString(level) << "] ";
    oss << "[" << config_.module_name << "] ";
    
    if (config_.enable_thread_id) {
        oss << "[" << std::this_thread::get_id() << "] ";
    }
    
    oss << message;
    
    return oss.str();
}

std::string Logger::getTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto tm = *std::localtime(&time_t);
    
    // 获取毫秒
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;
    
    std::ostringstream oss;
    oss << std::put_time(&tm, "%Y-%m-%d %H:%M:%S");
    oss << "." << std::setfill('0') << std::setw(3) << ms.count();
    
    return oss.str();
}

std::string Logger::getLevelString(LogLevel level) {
    switch (level) {
        case LogLevel::DEBUG: return "DEBUG";
        case LogLevel::INFO:  return "INFO ";
        case LogLevel::WARN:  return "WARN ";
        case LogLevel::ERROR: return "ERROR";
        case LogLevel::FATAL: return "FATAL";
        default: return "UNKNOWN";
    }
}

void Logger::rotateLogFile() {
    if (!file_stream_.is_open()) {
        return;
    }
    
    // 获取当前文件大小
    auto current_pos = file_stream_.tellp();
    if (current_pos < 0 || static_cast<size_t>(current_pos) < config_.max_file_size) {
        return;
    }
    
    // 关闭当前文件
    file_stream_.close();
    
    // 重新初始化（会创建新文件）
    initialize();
}

void Logger::flush() {
    std::lock_guard<std::mutex> lock(mutex_);
    if (file_stream_.is_open()) {
        file_stream_.flush();
    }
}

const std::string& Logger::getModuleName() const {
    return config_.module_name;
}

// 工具函数实现
std::string logErrorToString(LogError error) {
    switch (error) {
        case LogError::OK: return "成功";
        case LogError::PARAM_ERROR: return "参数错误";
        case LogError::INIT_ERROR: return "初始化错误";
        case LogError::FILE_ERROR: return "文件错误";
        case LogError::MEMORY_ERROR: return "内存错误";
        case LogError::NOT_INITIALIZED: return "未初始化";
        default: return "未知错误";
    }
}

std::string logLevelToString(LogLevel level) {
    switch (level) {
        case LogLevel::DEBUG: return "DEBUG";
        case LogLevel::INFO: return "INFO";
        case LogLevel::WARN: return "WARN";
        case LogLevel::ERROR: return "ERROR";
        case LogLevel::FATAL: return "FATAL";
        case LogLevel::OFF: return "OFF";
        default: return "UNKNOWN";
    }
}

LogConfig createDefaultConfig(const std::string& module_name) {
    LogConfig config;
    config.module_name = module_name;
    return config;
}

} // namespace greenland
