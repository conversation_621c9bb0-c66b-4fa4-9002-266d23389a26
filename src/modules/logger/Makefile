# Logger 模块 Makefile
# 通用日志记录模块

# 项目根目录
PROJECT_ROOT := ../../..

# 编译器和标志
CC = gcc
CFLAGS = -Wall -Wextra -O2 -fPIC -std=c99
LDFLAGS = -shared
LDLIBS = -lpthread

# 目录定义
SRC_DIR = src
INCLUDE_DIR = include
BUILD_DIR = $(PROJECT_ROOT)/build/modules/logger
LIB_DIR = $(PROJECT_ROOT)/lib

# 源文件和目标文件
SOURCES = $(wildcard $(SRC_DIR)/*.c)
OBJECTS = $(SOURCES:$(SRC_DIR)/%.c=$(BUILD_DIR)/%.o)
TARGET = $(LIB_DIR)/liblogger.so

# 默认目标
all: $(TARGET)

# 创建目录
$(BUILD_DIR):
	@mkdir -p $(BUILD_DIR)

$(LIB_DIR):
	@mkdir -p $(LIB_DIR)

# 编译目标文件
$(BUILD_DIR)/%.o: $(SRC_DIR)/%.c | $(BUILD_DIR)
	@echo "正在编译: $<"
	$(CC) $(CFLAGS) -I$(INCLUDE_DIR) -MMD -MP -c -o $@ $<

# 链接动态库
$(TARGET): $(OBJECTS) | $(LIB_DIR)
	@echo "正在链接动态库: $@"
	$(CC) $(LDFLAGS) -o $@ $^ $(LDLIBS)
	@echo "Logger 模块构建完成"

# 清理
clean:
	@echo "清理 Logger 模块..."
	@rm -rf $(BUILD_DIR)
	@rm -f $(TARGET)

# 安装头文件
install-headers:
	@mkdir -p $(PROJECT_ROOT)/include
	@cp $(INCLUDE_DIR)/*.h $(PROJECT_ROOT)/include/

# 显示帮助
help:
	@echo "Logger 模块构建选项:"
	@echo "  all            - 构建动态库"
	@echo "  clean          - 清理构建文件"
	@echo "  install-headers- 安装头文件"
	@echo "  help           - 显示此帮助"

# 包含依赖文件
-include $(OBJECTS:.o=.d)

.PHONY: all clean install-headers help
