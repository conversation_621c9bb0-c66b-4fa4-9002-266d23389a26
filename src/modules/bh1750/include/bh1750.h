#ifndef BH1750_H
#define BH1750_H

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

// BH1750 I2C地址
#define BH1750_I2C_ADDR          0x23    // ADDR引脚接地时的地址
#define BH1750_I2C_ADDR_ALT      0x5C    // ADDR引脚接VCC时的地址
#define BH1750_I2C_BUS           "/dev/i2c-2"

// BH1750 命令定义 (根据数据手册)
#define BH1750_CMD_POWER_DOWN    0x00    // 断电模式
#define BH1750_CMD_POWER_ON      0x01    // 通电模式
#define BH1750_CMD_RESET         0x07    // 重置数据寄存器

// 连续测量模式
#define BH1750_CMD_CONT_H_RES    0x10    // 连续高分辨率模式 (1lx分辨率, 120ms)
#define BH1750_CMD_CONT_H_RES2   0x11    // 连续高分辨率模式2 (0.5lx分辨率, 120ms)
#define BH1750_CMD_CONT_L_RES    0x13    // 连续低分辨率模式 (4lx分辨率, 16ms)

// 单次测量模式
#define BH1750_CMD_ONE_H_RES     0x20    // 单次高分辨率模式 (1lx分辨率, 120ms)
#define BH1750_CMD_ONE_H_RES2    0x21    // 单次高分辨率模式2 (0.5lx分辨率, 120ms)
#define BH1750_CMD_ONE_L_RES     0x23    // 单次低分辨率模式 (4lx分辨率, 16ms)

// 测量时间定义 (毫秒)
#define BH1750_MEAS_TIME_H_RES   120     // 高分辨率测量时间
#define BH1750_MEAS_TIME_H_RES2  120     // 高分辨率2测量时间
#define BH1750_MEAS_TIME_L_RES   16      // 低分辨率测量时间

// BH1750 测量模式
typedef enum {
    BH1750_MODE_CONT_H_RES = 0,    // 连续高分辨率模式
    BH1750_MODE_CONT_H_RES2,       // 连续高分辨率模式2
    BH1750_MODE_CONT_L_RES,        // 连续低分辨率模式
    BH1750_MODE_ONE_H_RES,         // 单次高分辨率模式
    BH1750_MODE_ONE_H_RES2,        // 单次高分辨率模式2
    BH1750_MODE_ONE_L_RES          // 单次低分辨率模式
} bh1750_mode_t;

// BH1750 数据结构
typedef struct {
    float lux;              // 光照强度 (lx)
    uint16_t raw_data;      // 原始数据
    bh1750_mode_t mode;     // 当前测量模式
} bh1750_data_t;

// BH1750 错误码
typedef enum {
    BH1750_OK = 0,
    BH1750_ERROR_INIT = -1,
    BH1750_ERROR_I2C = -2,
    BH1750_ERROR_TIMEOUT = -3,
    BH1750_ERROR_MODE = -4,
    BH1750_ERROR_DATA = -5
} bh1750_error_t;

// BH1750 公共接口
int bh1750_init(void);
int bh1750_init_with_addr(uint8_t i2c_addr);
void bh1750_deinit(void);
int bh1750_set_mode(bh1750_mode_t mode);
int bh1750_read_data(bh1750_data_t* data);
int bh1750_read_lux(float* lux);
int bh1750_power_down(void);
int bh1750_power_on(void);
int bh1750_reset(void);

// 兼容旧接口
int bh1750_read_data_legacy(void* data, size_t size);

#ifdef __cplusplus
}
#endif

#endif // BH1750_H
