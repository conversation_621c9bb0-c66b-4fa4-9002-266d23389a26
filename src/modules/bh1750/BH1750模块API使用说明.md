# BH1750光照传感器模块API使用说明

**作者**: 刘旭  
**版本**: 1.0.0  
**日期**: 2024

## ☀️ 模块概述

BH1750光照传感器模块提供高精度的环境光照强度测量，基于I2C通信协议，专为智能农业光照监控优化。

### 🎯 主要特性

- **高精度测量**: 1-65535 lux测量范围
- **宽动态范围**: 0.11 - 100000+ lux
- **I2C通信**: 标准I2C接口，地址0x23/0x5C
- **多种分辨率**: 0.5lx、1lx、4lx分辨率可选
- **低功耗**: 典型功耗120μA
- **快速响应**: 测量时间16-180ms
- **温度补偿**: 内置温度补偿
- **日志集成**: 详细的调试和监控日志

## 🔧 硬件连接

### Orange Pi Zero 2W I2C连接
```
BH1750     Orange Pi Zero 2W
VCC    ->  3.3V (物理引脚1)
GND    ->  GND (物理引脚6)
SDA    ->  SDA (物理引脚3, GPIO 264)
SCL    ->  SCL (物理引脚5, GPIO 263)
ADDR   ->  GND (地址0x23) 或 VCC (地址0x5C)
```

### I2C配置
```bash
# 启用I2C
sudo orangepi-config
# 选择 System -> Hardware -> i2c1 -> Enable

# 检查I2C设备
sudo i2cdetect -y 1
# 应该在地址0x23或0x5C看到BH1750设备
```

## 🔧 快速开始

### 1. 包含头文件

```c
#include "bh1750.h"
#include "logger.h"  // 可选，用于日志记录
```

### 2. 基本使用流程

```c
// 1. 获取默认配置
bh1750_config_t config;
bh1750_get_default_config(&config);

// 2. 创建传感器实例
bh1750_t sensor = bh1750_create(&config);

// 3. 初始化传感器
int result = bh1750_init(sensor);
if (result != BH1750_OK) {
    printf("初始化失败: %s\n", bh1750_error_to_string(result));
    return -1;
}

// 4. 读取光照强度
float lux = bh1750_read_lux(sensor);
if (lux >= 0) {
    printf("光照强度: %.2f lux\n", lux);
}

// 5. 清理资源
bh1750_destroy(sensor);
```

## 📊 API参考

### 配置管理

#### `bh1750_get_default_config()`
```c
void bh1750_get_default_config(bh1750_config_t* config);
```
获取默认传感器配置。

#### `bh1750_create()`
```c
bh1750_t bh1750_create(const bh1750_config_t* config);
```
创建传感器实例。

#### `bh1750_destroy()`
```c
void bh1750_destroy(bh1750_t sensor);
```
销毁传感器实例并释放资源。

### 设备管理

#### `bh1750_init()`
```c
int bh1750_init(bh1750_t sensor);
```
初始化传感器，配置I2C通信和测量模式。

#### `bh1750_deinit()`
```c
int bh1750_deinit(bh1750_t sensor);
```
反初始化传感器，释放I2C资源。

### 数据读取

#### `bh1750_read_lux()` - 读取光照强度
```c
float bh1750_read_lux(bh1750_t sensor);
```
读取当前光照强度。

**返回值**: 光照强度(lux)，失败返回-1.0

**示例**:
```c
float lux = bh1750_read_lux(sensor);
if (lux >= 0) {
    printf("当前光照: %.2f lux\n", lux);
    
    // 光照等级评估
    if (lux < 10) {
        printf("🌙 黑暗环境\n");
    } else if (lux < 50) {
        printf("🌆 昏暗环境\n");
    } else if (lux < 500) {
        printf("💡 室内照明\n");
    } else if (lux < 10000) {
        printf("🌤️ 明亮环境\n");
    } else {
        printf("☀️ 强烈阳光\n");
    }
} else {
    printf("❌ 光照测量失败\n");
}
```

### 状态查询

#### `bh1750_get_info()`
```c
int bh1750_get_info(bh1750_t sensor, bh1750_info_t* info);
```
获取传感器信息和统计数据。

#### `bh1750_is_connected()`
```c
bool bh1750_is_connected(bh1750_t sensor);
```
检查传感器连接状态。

## 🎛️ 配置选项

### 配置结构
```c
typedef struct {
    uint8_t i2c_bus;            // I2C总线号 (通常为1)
    uint8_t i2c_addr;           // I2C地址 (0x23或0x5C)
    bh1750_mode_t mode;         // 测量模式
    int measurement_delay;       // 测量延迟 (ms)
    int retry_count;            // 重试次数
    float calibration_factor;   // 校准因子
} bh1750_config_t;
```

### 默认配置值
```c
i2c_bus = 1;                           // I2C1总线
i2c_addr = 0x23;                       // 默认地址
mode = BH1750_MODE_CONTINUOUS_HIGH_RES; // 连续高分辨率
measurement_delay = 180;                // 180ms测量延迟
retry_count = 3;                       // 3次重试
calibration_factor = 1.0;              // 无校准
```

## 💡 使用技巧

### 1. 光照监控
```c
void monitor_light_conditions(bh1750_t sensor) {
    float lux = bh1750_read_lux(sensor);
    
    if (lux >= 0) {
        printf("光照监测报告:\n");
        printf("  强度: %.2f lux\n", lux);
        
        // 农业光照评估
        if (lux < 1000) {
            printf("  评估: 🌱 光照不足，需要补光\n");
        } else if (lux < 10000) {
            printf("  评估: 🌿 光照适中，适合生长\n");
        } else if (lux < 50000) {
            printf("  评估: 🌳 光照充足，生长良好\n");
        } else {
            printf("  评估: ☀️ 光照过强，需要遮阴\n");
        }
        
        // 光周期判断
        if (lux > 100) {
            printf("  光周期: 白天\n");
        } else {
            printf("  光周期: 夜晚\n");
        }
    }
}
```

### 2. 数据记录
```c
void log_light_data(bh1750_t sensor, logger_t logger) {
    float lux = bh1750_read_lux(sensor);
    
    if (lux >= 0) {
        logger_info(logger, "光照数据,强度=%.2f", lux);
        
        // 异常检测
        static float last_lux = -1;
        if (last_lux > 0) {
            float change_rate = fabs(lux - last_lux) / last_lux;
            if (change_rate > 0.5) {  // 变化超过50%
                logger_warn(logger, "光照急剧变化: %.2f -> %.2f lux", 
                           last_lux, lux);
            }
        }
        last_lux = lux;
    }
}
```

## ⚠️ 注意事项

1. **I2C权限**: 需要访问I2C设备的权限
2. **地址设置**: 确认ADDR引脚连接确定正确的I2C地址
3. **光线遮挡**: 避免传感器被遮挡影响测量
4. **测量延迟**: 不同模式有不同的测量时间
5. **校准**: 可能需要根据实际环境进行校准
6. **温度影响**: 极端温度可能影响测量精度

## 🔧 编译要求

```makefile
# 需要链接的库
LDLIBS += -lbh1750 -llogger -lwiringPi -lpthread
```

## 📝 完整示例

```c
#include "bh1750.h"
#include "logger.h"
#include <unistd.h>

int main() {
    // 创建日志记录器
    log_config_t log_config;
    logger_get_default_config(&log_config, "light_monitor");
    logger_t logger = logger_create(&log_config);
    
    // 创建传感器配置
    bh1750_config_t config;
    bh1750_get_default_config(&config);
    
    // 创建传感器实例
    bh1750_t sensor = bh1750_create(&config);
    bh1750_set_logger(sensor, logger);
    
    // 初始化传感器
    if (bh1750_init(sensor) != BH1750_OK) {
        logger_error(logger, "BH1750初始化失败");
        return -1;
    }
    
    logger_info(logger, "开始光照监控...");
    
    // 持续监控光照
    for (int i = 0; i < 60; i++) {  // 监控1分钟
        float lux = bh1750_read_lux(sensor);
        
        if (lux >= 0) {
            logger_info(logger, "测量 %d: 光照=%.2f lux", i+1, lux);
            printf("☀️ %.2f lux\n", lux);
        } else {
            logger_error(logger, "光照测量失败");
        }
        
        sleep(1);  // 每秒测量一次
    }
    
    // 清理资源
    bh1750_destroy(sensor);
    logger_destroy(logger);
    
    return 0;
}
```

---

**技术支持**: 刘旭  
**更新日期**: 2024年
