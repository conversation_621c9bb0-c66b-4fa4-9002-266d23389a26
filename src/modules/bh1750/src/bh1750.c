#define _DEFAULT_SOURCE  // 启用usleep函数
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <linux/i2c-dev.h>
#include <string.h>
#include <errno.h>
#include <time.h>
#include "bh1750.h"

// 全局变量
static int i2c_fd = -1;
static int initialized = 0;
static uint8_t device_addr = BH1750_I2C_ADDR;
static bh1750_mode_t current_mode = BH1750_MODE_CONT_H_RES;
static logger_t g_logger = NULL;
static bool g_logging_enabled = true;

// 内部函数声明
static int bh1750_i2c_write_cmd(uint8_t cmd);
static int bh1750_i2c_read_data(uint8_t* data, size_t len);
static float bh1750_convert_to_lux(uint16_t raw_data, bh1750_mode_t mode);
static int bh1750_get_measurement_time(bh1750_mode_t mode);
static uint8_t bh1750_get_mode_cmd(bh1750_mode_t mode);

// 模式命令映射表
static const uint8_t mode_commands[] = {
    BH1750_CMD_CONT_H_RES,    // BH1750_MODE_CONT_H_RES
    BH1750_CMD_CONT_H_RES2,   // BH1750_MODE_CONT_H_RES2
    BH1750_CMD_CONT_L_RES,    // BH1750_MODE_CONT_L_RES
    BH1750_CMD_ONE_H_RES,     // BH1750_MODE_ONE_H_RES
    BH1750_CMD_ONE_H_RES2,    // BH1750_MODE_ONE_H_RES2
    BH1750_CMD_ONE_L_RES      // BH1750_MODE_ONE_L_RES
};

// 测量时间映射表 (毫秒)
static const int measurement_times[] = {
    BH1750_MEAS_TIME_H_RES,   // BH1750_MODE_CONT_H_RES
    BH1750_MEAS_TIME_H_RES2,  // BH1750_MODE_CONT_H_RES2
    BH1750_MEAS_TIME_L_RES,   // BH1750_MODE_CONT_L_RES
    BH1750_MEAS_TIME_H_RES,   // BH1750_MODE_ONE_H_RES
    BH1750_MEAS_TIME_H_RES2,  // BH1750_MODE_ONE_H_RES2
    BH1750_MEAS_TIME_L_RES    // BH1750_MODE_ONE_L_RES
};

// 初始化BH1750传感器 (使用默认地址)
int bh1750_init(void) {
    return bh1750_init_with_addr(BH1750_I2C_ADDR);
}

// 初始化BH1750传感器 (指定I2C地址)
int bh1750_init_with_addr(uint8_t i2c_addr) {
    device_addr = i2c_addr;

    // 打开I2C设备
    i2c_fd = open(BH1750_I2C_BUS, O_RDWR);
    if (i2c_fd < 0) {
        return BH1750_ERROR_I2C;
    }

    // 设置I2C从设备地址
    if (ioctl(i2c_fd, I2C_SLAVE, device_addr) < 0) {
        close(i2c_fd);
        i2c_fd = -1;
        return BH1750_ERROR_I2C;
    }

    // BH1750初始化序列
    // 1. 上电
    uint8_t power_cmd = BH1750_CMD_POWER_ON;
    if (write(i2c_fd, &power_cmd, 1) != 1) {
        close(i2c_fd);
        i2c_fd = -1;
        return BH1750_ERROR_I2C;
    }
    usleep(10000); // 10ms

    // 2. 重置 (可选，但推荐)
    uint8_t reset_cmd = BH1750_CMD_RESET;
    if (write(i2c_fd, &reset_cmd, 1) != 1) {
        close(i2c_fd);
        i2c_fd = -1;
        return BH1750_ERROR_I2C;
    }
    usleep(10000); // 10ms

    // 3. 设置连续高分辨率模式
    uint8_t mode_cmd = BH1750_CMD_CONT_H_RES;
    if (write(i2c_fd, &mode_cmd, 1) != 1) {
        close(i2c_fd);
        i2c_fd = -1;
        return BH1750_ERROR_I2C;
    }

    current_mode = BH1750_MODE_CONT_H_RES;
    usleep(180000); // 180ms，等待第一次测量完成 (BH1750高分辨率模式需要120ms)

    initialized = 1;
    return BH1750_OK;
}

// 释放BH1750资源
void bh1750_deinit(void) {
    if (i2c_fd >= 0) {
        bh1750_power_down();
        close(i2c_fd);
        i2c_fd = -1;
    }
    initialized = 0;
    printf("BH1750 资源已释放\n");
}

// 设置测量模式
int bh1750_set_mode(bh1750_mode_t mode) {
    if (!initialized || i2c_fd < 0) {
        return BH1750_ERROR_INIT;
    }

    if (mode < 0 || mode > BH1750_MODE_ONE_L_RES) {
        return BH1750_ERROR_MODE;
    }

    uint8_t cmd = bh1750_get_mode_cmd(mode);
    if (bh1750_i2c_write_cmd(cmd) != BH1750_OK) {
        return BH1750_ERROR_I2C;
    }

    current_mode = mode;

    // 等待测量开始
    usleep(10000); // 10ms

    return BH1750_OK;
}

// 读取光照数据
int bh1750_read_data(bh1750_data_t* data) {
    if (!initialized || i2c_fd < 0) {
        return BH1750_ERROR_INIT;
    }

    if (data == NULL) {
        return BH1750_ERROR_DATA;
    }

    // 读取2字节数据
    uint8_t raw_data[2];
    if (read(i2c_fd, raw_data, 2) != 2) {
        return BH1750_ERROR_I2C;
    }

    // 组合16位数据 (高字节在前)
    uint16_t raw_value = (raw_data[0] << 8) | raw_data[1];

    // 转换为光照强度 (BH1750标准公式)
    float lux = (float)raw_value / 1.2f;

    // 填充数据结构
    data->lux = lux;
    data->raw_data = raw_value;
    data->mode = current_mode;

    return BH1750_OK;
}

// 读取光照强度 (简化接口)
int bh1750_read_lux(float* lux) {
    if (lux == NULL) {
        return BH1750_ERROR_DATA;
    }

    bh1750_data_t data;
    int ret = bh1750_read_data(&data);
    if (ret == BH1750_OK) {
        *lux = data.lux;
    }

    return ret;
}

// 读取光照强度 (简化接口别名)
int bh1750_read_light(float* light_intensity) {
    return bh1750_read_lux(light_intensity);
}

// 断电模式
int bh1750_power_down(void) {
    if (!initialized || i2c_fd < 0) {
        return BH1750_ERROR_INIT;
    }

    return bh1750_i2c_write_cmd(BH1750_CMD_POWER_DOWN);
}

// 上电模式
int bh1750_power_on(void) {
    if (i2c_fd < 0) {
        return BH1750_ERROR_INIT;
    }

    return bh1750_i2c_write_cmd(BH1750_CMD_POWER_ON);
}

// 重置数据寄存器
int bh1750_reset(void) {
    if (!initialized || i2c_fd < 0) {
        return BH1750_ERROR_INIT;
    }

    return bh1750_i2c_write_cmd(BH1750_CMD_RESET);
}

// 兼容旧接口
int bh1750_read_data_legacy(void* data, size_t size) {
    printf("读取 bh1750 数据 (兼容模式)\n");
    if (size < sizeof(bh1750_data_t)) {
        return BH1750_ERROR_DATA;
    }
    return bh1750_read_data((bh1750_data_t*)data);
}

// 内部函数实现

// I2C写命令
static int bh1750_i2c_write_cmd(uint8_t cmd) {
    printf("发送I2C命令: 0x%02X\n", cmd);

    ssize_t result = write(i2c_fd, &cmd, 1);
    if (result != 1) {
        printf("错误: I2C写命令失败 (返回值: %zd): %s\n", result, strerror(errno));
        return BH1750_ERROR_I2C;
    }

    printf("I2C命令发送成功\n");
    return BH1750_OK;
}

// I2C读数据
static int __attribute__((unused)) bh1750_i2c_read_data(uint8_t* data, size_t len) {
    printf("尝试读取 %zu 字节数据...\n", len);

    ssize_t result = read(i2c_fd, data, len);
    if (result != (ssize_t)len) {
        printf("错误: I2C读数据失败 (期望: %zu, 实际: %zd): %s\n",
               len, result, strerror(errno));
        return BH1750_ERROR_I2C;
    }

    printf("I2C数据读取成功\n");
    return BH1750_OK;
}

// 转换原始数据为光照强度
static float __attribute__((unused)) bh1750_convert_to_lux(uint16_t raw_data, bh1750_mode_t mode) {
    float lux = 0.0f;

    switch (mode) {
        case BH1750_MODE_CONT_H_RES:
        case BH1750_MODE_ONE_H_RES:
            // 高分辨率模式: 1 lx 分辨率
            lux = (float)raw_data / 1.2f;
            break;

        case BH1750_MODE_CONT_H_RES2:
        case BH1750_MODE_ONE_H_RES2:
            // 高分辨率模式2: 0.5 lx 分辨率
            lux = (float)raw_data / 2.4f;
            break;

        case BH1750_MODE_CONT_L_RES:
        case BH1750_MODE_ONE_L_RES:
            // 低分辨率模式: 4 lx 分辨率
            lux = (float)raw_data / 1.2f;
            break;

        default:
            lux = 0.0f;
            break;
    }

    return lux;
}

// 获取测量时间
static int __attribute__((unused)) bh1750_get_measurement_time(bh1750_mode_t mode) {
    if (mode < 0 || mode > BH1750_MODE_ONE_L_RES) {
        return 0;
    }
    return measurement_times[mode];
}

// 获取模式命令
static uint8_t bh1750_get_mode_cmd(bh1750_mode_t mode) {
    if (mode < 0 || mode > BH1750_MODE_ONE_L_RES) {
        return BH1750_CMD_CONT_H_RES;
    }
    return mode_commands[mode];
}

// 日志相关函数实现
int bh1750_set_logger(logger_t logger) {
    g_logger = logger;
    return BH1750_OK;
}

logger_t bh1750_get_logger(void) {
    return g_logger;
}

int bh1750_enable_logging(bool enable) {
    g_logging_enabled = enable;
    return BH1750_OK;
}
