/**
 * @file performance.h
 * @brief GreenLand 性能监控模块
 * <AUTHOR>
 * @date 2024
 */

#ifndef PERFORMANCE_H
#define PERFORMANCE_H

#include <stdint.h>
#include <stdbool.h>
#include <time.h>

#ifdef __cplusplus
extern "C" {
#endif

// 性能统计结构
typedef struct {
    // CPU使用率
    float cpu_usage;
    float cpu_temperature;
    
    // 内存使用情况
    uint64_t memory_total;
    uint64_t memory_used;
    uint64_t memory_free;
    float memory_usage_percent;
    
    // 磁盘使用情况
    uint64_t disk_total;
    uint64_t disk_used;
    uint64_t disk_free;
    float disk_usage_percent;
    
    // 网络统计
    uint64_t network_rx_bytes;
    uint64_t network_tx_bytes;
    uint64_t network_rx_packets;
    uint64_t network_tx_packets;
    
    // 系统负载
    float load_average_1min;
    float load_average_5min;
    float load_average_15min;
    
    // 运行时间
    uint64_t uptime_seconds;
    
    // 进程信息
    int process_count;
    int thread_count;
    
    // 时间戳
    time_t timestamp;
    
} performance_stats_t;

// 性能监控配置
typedef struct {
    bool enable_cpu_monitoring;
    bool enable_memory_monitoring;
    bool enable_disk_monitoring;
    bool enable_network_monitoring;
    bool enable_temperature_monitoring;
    
    int update_interval;  // 更新间隔(秒)
    bool enable_logging;  // 是否记录日志
    char log_file[256];   // 日志文件路径
    
} performance_config_t;

// 性能监控句柄
typedef struct performance_monitor* performance_monitor_t;

// 错误码
typedef enum {
    PERF_OK = 0,
    PERF_ERROR_PARAM,
    PERF_ERROR_INIT,
    PERF_ERROR_FILE,
    PERF_ERROR_MEMORY,
    PERF_ERROR_SYSTEM
} performance_error_t;

/**
 * @brief 创建性能监控器
 * @param config 配置参数，NULL使用默认配置
 * @return 性能监控器句柄，失败返回NULL
 */
performance_monitor_t performance_monitor_create(const performance_config_t* config);

/**
 * @brief 销毁性能监控器
 * @param monitor 性能监控器句柄
 */
void performance_monitor_destroy(performance_monitor_t monitor);

/**
 * @brief 启动性能监控
 * @param monitor 性能监控器句柄
 * @return 错误码
 */
int performance_monitor_start(performance_monitor_t monitor);

/**
 * @brief 停止性能监控
 * @param monitor 性能监控器句柄
 * @return 错误码
 */
int performance_monitor_stop(performance_monitor_t monitor);

/**
 * @brief 获取当前性能统计
 * @param monitor 性能监控器句柄
 * @param stats 性能统计结构指针
 * @return 错误码
 */
int performance_get_stats(performance_monitor_t monitor, performance_stats_t* stats);

/**
 * @brief 更新性能统计
 * @param monitor 性能监控器句柄
 * @return 错误码
 */
int performance_update_stats(performance_monitor_t monitor);

/**
 * @brief 获取CPU使用率
 * @return CPU使用率百分比，失败返回-1
 */
float performance_get_cpu_usage(void);

/**
 * @brief 获取CPU温度
 * @return CPU温度(摄氏度)，失败返回-1
 */
float performance_get_cpu_temperature(void);

/**
 * @brief 获取内存使用情况
 * @param total 总内存(字节)
 * @param used 已使用内存(字节)
 * @param free 空闲内存(字节)
 * @return 错误码
 */
int performance_get_memory_info(uint64_t* total, uint64_t* used, uint64_t* free);

/**
 * @brief 获取磁盘使用情况
 * @param path 磁盘路径
 * @param total 总空间(字节)
 * @param used 已使用空间(字节)
 * @param free 空闲空间(字节)
 * @return 错误码
 */
int performance_get_disk_info(const char* path, uint64_t* total, uint64_t* used, uint64_t* free);

/**
 * @brief 获取网络统计信息
 * @param interface 网络接口名，NULL获取所有接口总和
 * @param rx_bytes 接收字节数
 * @param tx_bytes 发送字节数
 * @param rx_packets 接收包数
 * @param tx_packets 发送包数
 * @return 错误码
 */
int performance_get_network_stats(const char* interface, uint64_t* rx_bytes, 
                                 uint64_t* tx_bytes, uint64_t* rx_packets, uint64_t* tx_packets);

/**
 * @brief 获取系统负载
 * @param load1 1分钟平均负载
 * @param load5 5分钟平均负载
 * @param load15 15分钟平均负载
 * @return 错误码
 */
int performance_get_load_average(float* load1, float* load5, float* load15);

/**
 * @brief 获取系统运行时间
 * @return 运行时间(秒)，失败返回0
 */
uint64_t performance_get_uptime(void);

/**
 * @brief 获取进程和线程数量
 * @param process_count 进程数量
 * @param thread_count 线程数量
 * @return 错误码
 */
int performance_get_process_info(int* process_count, int* thread_count);

/**
 * @brief 格式化性能统计为字符串
 * @param stats 性能统计结构
 * @param buffer 输出缓冲区
 * @param buffer_size 缓冲区大小
 * @return 错误码
 */
int performance_format_stats(const performance_stats_t* stats, char* buffer, size_t buffer_size);

/**
 * @brief 获取默认配置
 * @param config 配置结构指针
 */
void performance_get_default_config(performance_config_t* config);

/**
 * @brief 错误码转字符串
 * @param error 错误码
 * @return 错误描述字符串
 */
const char* performance_error_to_string(performance_error_t error);

/**
 * @brief 性能监控回调函数类型
 * @param stats 性能统计数据
 * @param user_data 用户数据
 */
typedef void (*performance_callback_t)(const performance_stats_t* stats, void* user_data);

/**
 * @brief 设置性能监控回调函数
 * @param monitor 性能监控器句柄
 * @param callback 回调函数
 * @param user_data 用户数据
 * @return 错误码
 */
int performance_set_callback(performance_monitor_t monitor, performance_callback_t callback, void* user_data);

#ifdef __cplusplus
}
#endif

#endif // PERFORMANCE_H
