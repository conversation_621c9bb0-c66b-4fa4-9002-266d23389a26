# AHT20温湿度传感器模块API使用说明

**作者**: 刘旭  
**版本**: 1.0.0  
**日期**: 2024

## 🌡️ 模块概述

AHT20温湿度传感器模块提供高精度的温度和湿度测量，基于I2C通信协议，为智能农业环境监控优化。

### 🎯 主要特性

- **高精度测量**: 温度±0.3°C，湿度±2%RH
- **宽测量范围**: 温度-40~85°C，湿度0~100%RH
- **I2C通信**: 标准I2C接口，地址0x38
- **低功耗**: 适合长期监控应用
- **快速响应**: 测量时间<80ms
- **校准补偿**: 内置温湿度补偿算法
- **错误检测**: CRC校验和状态检查
- **日志集成**: 详细的调试和监控日志

## 🔧 硬件连接

### Orange Pi Zero 2W I2C连接
```
AHT20      Orange Pi Zero 2W
VCC    ->  3.3V (物理引脚1)
GND    ->  GND (物理引脚6)
SDA    ->  SDA (物理引脚3, GP<PERSON> 264)
SCL    ->  SCL (物理引脚5, GPIO 263)
```

### I2C配置
```bash
# 启用I2C
sudo orangepi-config
# 选择 System -> Hardware -> i2c1 -> Enable

# 检查I2C设备
sudo i2cdetect -y 1
# 应该在地址0x38看到AHT20设备
```

## 🔧 快速开始

### 1. 包含头文件

```c
#include "aht20.h"
#include "logger.h"  // 可选，用于日志记录
```

### 2. 基本使用流程

```c
// 1. 获取默认配置
aht20_config_t config;
aht20_get_default_config(&config);

// 2. 创建传感器实例
aht20_t sensor = aht20_create(&config);

// 3. 初始化传感器
int result = aht20_init(sensor);
if (result != AHT20_OK) {
    printf("初始化失败: %s\n", aht20_error_to_string(result));
    return -1;
}

// 4. 读取温湿度
float temperature, humidity;
result = aht20_read_data(sensor, &temperature, &humidity);
if (result == AHT20_OK) {
    printf("温度: %.2f°C, 湿度: %.2f%%RH\n", temperature, humidity);
}

// 5. 清理资源
aht20_destroy(sensor);
```

## 📊 API参考

### 配置管理

#### `aht20_get_default_config()`
```c
void aht20_get_default_config(aht20_config_t* config);
```
获取默认传感器配置。

#### `aht20_create()`
```c
aht20_t aht20_create(const aht20_config_t* config);
```
创建传感器实例。

#### `aht20_destroy()`
```c
void aht20_destroy(aht20_t sensor);
```
销毁传感器实例并释放资源。

### 设备管理

#### `aht20_init()`
```c
int aht20_init(aht20_t sensor);
```
初始化传感器，配置I2C通信和传感器参数。

#### `aht20_deinit()`
```c
int aht20_deinit(aht20_t sensor);
```
反初始化传感器，释放I2C资源。

#### `aht20_reset()`
```c
int aht20_reset(aht20_t sensor);
```
软件复位传感器。

### 数据读取

#### `aht20_read_data()` - 读取温湿度
```c
int aht20_read_data(aht20_t sensor, float* temperature, float* humidity);
```
读取温度和湿度数据。

**参数**:
- `sensor`: 传感器句柄
- `temperature`: 温度输出指针(°C)
- `humidity`: 湿度输出指针(%RH)

**返回值**: AHT20_OK表示成功

**示例**:
```c
float temp, hum;
int result = aht20_read_data(sensor, &temp, &hum);
if (result == AHT20_OK) {
    printf("环境监测: %.2f°C, %.2f%%RH\n", temp, hum);
    
    // 环境评估
    if (temp < 10) {
        printf("🥶 温度过低，注意保温\n");
    } else if (temp > 35) {
        printf("🔥 温度过高，注意降温\n");
    }
    
    if (hum < 30) {
        printf("🏜️ 湿度过低，需要加湿\n");
    } else if (hum > 80) {
        printf("💧 湿度过高，注意通风\n");
    }
}
```

#### `aht20_read_temperature()` - 只读温度
```c
float aht20_read_temperature(aht20_t sensor);
```
只读取温度数据。

**返回值**: 温度值(°C)，失败返回-999.0

#### `aht20_read_humidity()` - 只读湿度
```c
float aht20_read_humidity(aht20_t sensor);
```
只读取湿度数据。

**返回值**: 湿度值(%RH)，失败返回-999.0

### 状态查询

#### `aht20_get_status()`
```c
int aht20_get_status(aht20_t sensor, aht20_status_t* status);
```
获取传感器状态信息。

#### `aht20_is_connected()`
```c
bool aht20_is_connected(aht20_t sensor);
```
检查传感器连接状态。

#### `aht20_get_info()`
```c
int aht20_get_info(aht20_t sensor, aht20_info_t* info);
```
获取传感器信息和统计数据。

## 🎛️ 配置选项

### 配置结构
```c
typedef struct {
    uint8_t i2c_bus;            // I2C总线号 (通常为1)
    uint8_t i2c_addr;           // I2C地址 (0x38)
    int measurement_delay;       // 测量延迟 (ms)
    int retry_count;            // 重试次数
    bool enable_crc_check;      // 启用CRC校验
    float temp_offset;          // 温度偏移校正
    float hum_offset;           // 湿度偏移校正
} aht20_config_t;
```

### 默认配置值
```c
i2c_bus = 1;                // I2C1总线
i2c_addr = 0x38;            // AHT20默认地址
measurement_delay = 80;      // 80ms测量延迟
retry_count = 3;            // 3次重试
enable_crc_check = true;    // 启用CRC校验
temp_offset = 0.0;          // 无温度偏移
hum_offset = 0.0;           // 无湿度偏移
```

## 💡 使用技巧

### 1. 环境监控
```c
void monitor_environment(aht20_t sensor) {
    float temp, hum;
    
    if (aht20_read_data(sensor, &temp, &hum) == AHT20_OK) {
        // 计算露点温度
        float dewpoint = calculate_dewpoint(temp, hum);
        
        // 计算热指数
        float heat_index = calculate_heat_index(temp, hum);
        
        printf("环境数据:\n");
        printf("  温度: %.2f°C\n", temp);
        printf("  湿度: %.2f%%RH\n", hum);
        printf("  露点: %.2f°C\n", dewpoint);
        printf("  热指数: %.2f°C\n", heat_index);
        
        // 舒适度评估
        if (temp >= 20 && temp <= 26 && hum >= 40 && hum <= 60) {
            printf("  舒适度: 😊 舒适\n");
        } else {
            printf("  舒适度: 😐 一般\n");
        }
    }
}
```

### 2. 数据记录
```c
void log_environmental_data(aht20_t sensor, logger_t logger) {
    float temp, hum;
    
    if (aht20_read_data(sensor, &temp, &hum) == AHT20_OK) {
        logger_info(logger, "环境数据,温度=%.2f,湿度=%.2f", temp, hum);
        
        // 异常检测
        if (temp < -10 || temp > 50) {
            logger_warn(logger, "温度异常: %.2f°C", temp);
        }
        if (hum < 10 || hum > 95) {
            logger_warn(logger, "湿度异常: %.2f%%RH", hum);
        }
    } else {
        logger_error(logger, "传感器读取失败");
    }
}
```

### 3. 校准补偿
```c
void calibrate_sensor(aht20_t sensor) {
    // 与标准设备对比，计算偏移量
    float standard_temp = 25.0;  // 标准温度
    float standard_hum = 50.0;   // 标准湿度
    
    float measured_temp = aht20_read_temperature(sensor);
    float measured_hum = aht20_read_humidity(sensor);
    
    if (measured_temp > -900 && measured_hum > -900) {
        float temp_offset = standard_temp - measured_temp;
        float hum_offset = standard_hum - measured_hum;
        
        aht20_config_t config;
        aht20_get_config(sensor, &config);
        config.temp_offset = temp_offset;
        config.hum_offset = hum_offset;
        aht20_set_config(sensor, &config);
        
        printf("校准完成: 温度偏移=%.2f, 湿度偏移=%.2f\n", 
               temp_offset, hum_offset);
    }
}
```

### 4. 错误处理
```c
int robust_read_data(aht20_t sensor, float* temp, float* hum) {
    for (int retry = 0; retry < 3; retry++) {
        int result = aht20_read_data(sensor, temp, hum);
        
        if (result == AHT20_OK) {
            // 数据合理性检查
            if (*temp >= -40 && *temp <= 85 && 
                *hum >= 0 && *hum <= 100) {
                return AHT20_OK;
            }
        }
        
        // 重试前等待
        usleep(100000);  // 100ms
        
        // 尝试复位传感器
        if (retry == 1) {
            aht20_reset(sensor);
            usleep(20000);  // 20ms
        }
    }
    
    return AHT20_ERROR_READ;
}
```

## ⚠️ 注意事项

1. **I2C权限**: 需要访问I2C设备的权限
2. **电源稳定**: 确保3.3V电源稳定
3. **连接质量**: 检查I2C线路连接
4. **测量频率**: 避免过于频繁的测量(建议间隔>1秒)
5. **环境干扰**: 避免强电磁干扰环境
6. **温度范围**: 工作温度范围-40~85°C

## 🔧 编译要求

```makefile
# 需要链接的库
LDLIBS += -laht20 -llogger -lwiringPi -lpthread
```

## 📝 完整示例

```c
#include "aht20.h"
#include "logger.h"
#include <unistd.h>

int main() {
    // 创建日志记录器
    log_config_t log_config;
    logger_get_default_config(&log_config, "env_monitor");
    logger_t logger = logger_create(&log_config);
    
    // 创建传感器配置
    aht20_config_t config;
    aht20_get_default_config(&config);
    
    // 创建传感器实例
    aht20_t sensor = aht20_create(&config);
    aht20_set_logger(sensor, logger);
    
    // 初始化传感器
    if (aht20_init(sensor) != AHT20_OK) {
        logger_error(logger, "AHT20初始化失败");
        return -1;
    }
    
    logger_info(logger, "开始环境监控...");
    
    // 持续监控环境
    for (int i = 0; i < 60; i++) {  // 监控1分钟
        float temp, hum;
        
        if (aht20_read_data(sensor, &temp, &hum) == AHT20_OK) {
            logger_info(logger, "测量 %d: 温度=%.2f°C, 湿度=%.2f%%RH", 
                       i+1, temp, hum);
            
            printf("🌡️ %.2f°C  💧 %.2f%%RH\n", temp, hum);
        } else {
            logger_error(logger, "测量失败");
        }
        
        sleep(1);  // 每秒测量一次
    }
    
    // 获取统计信息
    aht20_info_t info;
    if (aht20_get_info(sensor, &info) == AHT20_OK) {
        logger_info(logger, "统计: 成功=%lu, 失败=%lu", 
                   info.read_count, info.error_count);
    }
    
    // 清理资源
    aht20_destroy(sensor);
    logger_destroy(logger);
    
    return 0;
}
```

---

**技术支持**: 刘旭  
**更新日期**: 2024年
