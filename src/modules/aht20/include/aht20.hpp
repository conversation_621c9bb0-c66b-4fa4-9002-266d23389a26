/**
 * @file aht20.hpp
 * @brief AHT20温湿度传感器模块 - C++面向对象版本
 * <AUTHOR>
 * @date 2024
 *
 * 用于读取温度和湿度数据的I2C传感器驱动
 * 支持Orange Pi Zero 2W平台
 */

#ifndef AHT20_HPP
#define AHT20_HPP

#include <string>
#include <memory>
#include <mutex>
#include <chrono>
#include "logger.hpp"

namespace greenland {

// 传感器配置结构
struct AHT20Config {
    std::string i2c_device = "/dev/i2c-2";  // I2C设备路径
    uint8_t i2c_address = 0x38;             // I2C地址
    int measurement_delay_ms = 100;         // 测量间隔 (毫秒)
    int init_delay_ms = 40;                 // 初始化延迟 (毫秒)
    int measurement_timeout_ms = 100;       // 测量超时 (毫秒)
    bool enable_logging = true;             // 启用日志
    bool enable_crc_check = true;           // 启用CRC校验
};

// 测量结果结构
struct AHT20Result {
    bool success = false;                   // 测量是否成功
    float temperature_celsius = 0.0f;       // 温度 (摄氏度)
    float humidity_percent = 0.0f;          // 湿度 (百分比)
    std::chrono::system_clock::time_point timestamp; // 时间戳
    std::string error_message;              // 错误信息
    bool crc_valid = false;                 // CRC校验是否通过
};

// 错误码枚举
enum class AHT20Error {
    OK = 0,
    INIT_ERROR,
    I2C_ERROR,
    TIMEOUT_ERROR,
    CRC_ERROR,
    PARAM_ERROR,
    NOT_INITIALIZED,
    DEVICE_NOT_FOUND
};

/**
 * @brief AHT20温湿度传感器类
 */
class AHT20Sensor {
public:
    /**
     * @brief 构造函数
     * @param config 传感器配置
     * @param logger 日志器指针
     */
    explicit AHT20Sensor(const AHT20Config& config, 
                         std::shared_ptr<Logger> logger = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~AHT20Sensor();
    
    /**
     * @brief 禁用拷贝构造和赋值
     */
    AHT20Sensor(const AHT20Sensor&) = delete;
    AHT20Sensor& operator=(const AHT20Sensor&) = delete;
    
    /**
     * @brief 初始化传感器
     * @return 错误码
     */
    AHT20Error initialize();
    
    /**
     * @brief 清理资源
     */
    void cleanup();
    
    /**
     * @brief 读取温湿度数据
     * @return 测量结果
     */
    AHT20Result readData();
    
    /**
     * @brief 只读取温度
     * @return 测量结果
     */
    AHT20Result readTemperature();
    
    /**
     * @brief 只读取湿度
     * @return 测量结果
     */
    AHT20Result readHumidity();
    
    /**
     * @brief 获取配置
     * @return 当前配置
     */
    const AHT20Config& getConfig() const;
    
    /**
     * @brief 设置配置
     * @param config 新配置
     * @return 错误码
     */
    AHT20Error setConfig(const AHT20Config& config);
    
    /**
     * @brief 检查传感器是否已初始化
     * @return 是否已初始化
     */
    bool isInitialized() const;
    
    /**
     * @brief 软复位传感器
     * @return 错误码
     */
    AHT20Error softReset();
    
    /**
     * @brief 自检测试
     * @return 测试结果
     */
    AHT20Result selfTest();
    
    /**
     * @brief 获取传感器状态信息
     * @return 状态字符串
     */
    std::string getStatusInfo() const;
    
    /**
     * @brief 获取传感器ID
     * @return 传感器ID，失败返回0
     */
    uint32_t getSensorID();

private:
    /**
     * @brief 初始化I2C设备
     * @return 错误码
     */
    AHT20Error initializeI2C();
    
    /**
     * @brief 发送初始化命令
     * @return 错误码
     */
    AHT20Error sendInitCommand();
    
    /**
     * @brief 触发测量
     * @return 错误码
     */
    AHT20Error triggerMeasurement();
    
    /**
     * @brief 读取测量数据
     * @param data 数据缓冲区
     * @param size 缓冲区大小
     * @return 错误码
     */
    AHT20Error readMeasurementData(uint8_t* data, size_t size);
    
    /**
     * @brief 解析温湿度数据
     * @param data 原始数据
     * @param temperature 温度输出
     * @param humidity 湿度输出
     * @return 是否成功
     */
    bool parseData(const uint8_t* data, float& temperature, float& humidity);
    
    /**
     * @brief 计算CRC校验
     * @param data 数据
     * @param length 数据长度
     * @return CRC值
     */
    uint8_t calculateCRC(const uint8_t* data, size_t length);
    
    /**
     * @brief 验证CRC校验
     * @param data 数据
     * @param length 数据长度
     * @param expected_crc 期望的CRC值
     * @return 是否通过校验
     */
    bool verifyCRC(const uint8_t* data, size_t length, uint8_t expected_crc);
    
    /**
     * @brief 等待传感器就绪
     * @return 错误码
     */
    AHT20Error waitForReady();
    
    /**
     * @brief 记录日志
     * @param level 日志级别
     * @param message 日志消息
     */
    void logMessage(LogLevel level, const std::string& message);

private:
    AHT20Config config_;
    std::shared_ptr<Logger> logger_;
    mutable std::mutex mutex_;
    bool initialized_;
    int i2c_fd_;
    
    // 统计信息
    uint64_t total_measurements_;
    uint64_t successful_measurements_;
    uint64_t failed_measurements_;
    uint64_t crc_errors_;
    
    // AHT20命令定义
    static constexpr uint8_t CMD_INIT = 0xBE;
    static constexpr uint8_t CMD_TRIGGER = 0xAC;
    static constexpr uint8_t CMD_SOFT_RESET = 0xBA;
    static constexpr uint8_t INIT_PARAM1 = 0x08;
    static constexpr uint8_t INIT_PARAM2 = 0x00;
    static constexpr uint8_t TRIGGER_PARAM1 = 0x33;
    static constexpr uint8_t TRIGGER_PARAM2 = 0x00;
    static constexpr uint8_t STATUS_BUSY = 0x80;
    static constexpr uint8_t STATUS_CALIBRATED = 0x08;
};

/**
 * @brief 工具函数：错误码转字符串
 * @param error 错误码
 * @return 错误描述字符串
 */
std::string aht20ErrorToString(AHT20Error error);

/**
 * @brief 创建默认配置
 * @return 默认配置
 */
AHT20Config createDefaultAHT20Config();

/**
 * @brief 验证配置有效性
 * @param config 配置
 * @return 是否有效
 */
bool validateAHT20Config(const AHT20Config& config);

} // namespace greenland

#endif // AHT20_HPP
