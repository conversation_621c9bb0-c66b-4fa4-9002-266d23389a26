#ifndef AHT20_H
#define AHT20_H

#include <stdint.h>
#include <stddef.h>
#include "logger.h"

#ifdef __cplusplus
extern "C" {
#endif

// AHT20 I2C地址
#define AHT20_I2C_ADDR          0x38
#define AHT20_I2C_BUS           "/dev/i2c-2"

// AHT20 命令定义
#define AHT20_CMD_INIT          0xBE
#define AHT20_CMD_TRIGGER       0xAC
#define AHT20_CMD_SOFT_RESET    0xBA
#define AHT20_CMD_STATUS        0x71

// AHT20 状态位
#define AHT20_STATUS_BUSY       0x80
#define AHT20_STATUS_CALIBRATED 0x08

// AHT20 数据结构
typedef struct {
    float temperature;  // 温度 (°C)
    float humidity;     // 湿度 (%)
} aht20_data_t;

// AHT20 错误码
typedef enum {
    AHT20_OK = 0,
    AHT20_ERROR_INIT = -1,
    AHT20_ERROR_I2C = -2,
    A<PERSON>20_ERROR_TIMEOUT = -3,
    AHT20_ERROR_CALIBRATION = -4,
    AHT20_ERROR_CRC = -5
} aht20_error_t;

// AHT20 公共接口
int aht20_init(void);
void aht20_deinit(void);
int aht20_read_data(aht20_data_t* data);
int aht20_get_status(uint8_t* status);
int aht20_soft_reset(void);
int aht20_trigger_measurement(void);
int aht20_is_busy(void);
int aht20_is_calibrated(void);

// 兼容旧接口
int aht20_read_data_legacy(void* data, size_t size);

// 日志相关接口
int aht20_set_logger(logger_t logger);
logger_t aht20_get_logger(void);
int aht20_enable_logging(bool enable);

#ifdef __cplusplus
}
#endif

#endif // AHT20_H
