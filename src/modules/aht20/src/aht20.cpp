/**
 * @file aht20.cpp
 * @brief AHT20温湿度传感器模块实现 - C++面向对象版本
 * <AUTHOR>
 * @date 2024
 */

#include "aht20.hpp"
#include <fcntl.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <linux/i2c-dev.h>
#include <thread>
#include <sstream>
#include <iomanip>

namespace greenland {

AHT20Sensor::AHT20Sensor(const AHT20Config& config, std::shared_ptr<Logger> logger)
    : config_(config), logger_(logger), initialized_(false), i2c_fd_(-1),
      total_measurements_(0), successful_measurements_(0), failed_measurements_(0),
      crc_errors_(0) {
    
    if (!logger_) {
        // 创建默认日志器
        LogConfig log_config = createDefaultConfig("AHT20");
        logger_ = std::make_shared<Logger>(log_config);
    }
}

AHT20Sensor::~AHT20Sensor() {
    cleanup();
}

AHT20Error AHT20Sensor::initialize() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (initialized_) {
        return AHT20Error::OK;
    }
    
    logMessage(LogLevel::INFO, "初始化AHT20温湿度传感器...");
    
    // 验证配置
    if (!validateAHT20Config(config_)) {
        logMessage(LogLevel::ERROR, "传感器配置无效");
        return AHT20Error::PARAM_ERROR;
    }
    
    // 初始化I2C设备
    AHT20Error result = initializeI2C();
    if (result != AHT20Error::OK) {
        logMessage(LogLevel::ERROR, "I2C设备初始化失败");
        return result;
    }
    
    // 等待传感器启动
    std::this_thread::sleep_for(std::chrono::milliseconds(config_.init_delay_ms));
    
    // 发送初始化命令
    result = sendInitCommand();
    if (result != AHT20Error::OK) {
        logMessage(LogLevel::ERROR, "传感器初始化命令失败");
        cleanup();
        return result;
    }
    
    // 等待初始化完成
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    
    // 重置统计信息
    total_measurements_ = 0;
    successful_measurements_ = 0;
    failed_measurements_ = 0;
    crc_errors_ = 0;
    
    initialized_ = true;
    
    std::ostringstream oss;
    oss << "AHT20传感器初始化成功 - I2C地址: 0x" 
        << std::hex << std::uppercase << static_cast<int>(config_.i2c_address);
    logMessage(LogLevel::INFO, oss.str());
    
    return AHT20Error::OK;
}

AHT20Error AHT20Sensor::initializeI2C() {
    // 打开I2C设备
    i2c_fd_ = open(config_.i2c_device.c_str(), O_RDWR);
    if (i2c_fd_ < 0) {
        return AHT20Error::DEVICE_NOT_FOUND;
    }
    
    // 设置I2C从设备地址
    if (ioctl(i2c_fd_, I2C_SLAVE, config_.i2c_address) < 0) {
        close(i2c_fd_);
        i2c_fd_ = -1;
        return AHT20Error::I2C_ERROR;
    }
    
    return AHT20Error::OK;
}

AHT20Error AHT20Sensor::sendInitCommand() {
    uint8_t init_cmd[3] = {CMD_INIT, INIT_PARAM1, INIT_PARAM2};
    
    if (write(i2c_fd_, init_cmd, 3) != 3) {
        return AHT20Error::I2C_ERROR;
    }
    
    return AHT20Error::OK;
}

void AHT20Sensor::cleanup() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (i2c_fd_ >= 0) {
        close(i2c_fd_);
        i2c_fd_ = -1;
    }
    
    initialized_ = false;
    logMessage(LogLevel::INFO, "AHT20传感器已清理");
}

AHT20Result AHT20Sensor::readData() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    AHT20Result result;
    result.timestamp = std::chrono::system_clock::now();
    
    if (!initialized_) {
        result.error_message = "传感器未初始化";
        logMessage(LogLevel::ERROR, result.error_message);
        failed_measurements_++;
        return result;
    }
    
    total_measurements_++;
    
    try {
        // 触发测量
        AHT20Error error = triggerMeasurement();
        if (error != AHT20Error::OK) {
            result.error_message = "触发测量失败: " + aht20ErrorToString(error);
            logMessage(LogLevel::ERROR, result.error_message);
            failed_measurements_++;
            return result;
        }
        
        // 等待测量完成
        std::this_thread::sleep_for(std::chrono::milliseconds(config_.measurement_delay_ms));
        
        // 等待传感器就绪
        error = waitForReady();
        if (error != AHT20Error::OK) {
            result.error_message = "等待传感器就绪超时";
            logMessage(LogLevel::WARN, result.error_message);
            failed_measurements_++;
            return result;
        }
        
        // 读取数据
        uint8_t data[7];
        error = readMeasurementData(data, sizeof(data));
        if (error != AHT20Error::OK) {
            result.error_message = "读取数据失败: " + aht20ErrorToString(error);
            logMessage(LogLevel::ERROR, result.error_message);
            failed_measurements_++;
            return result;
        }
        
        // CRC校验
        if (config_.enable_crc_check) {
            result.crc_valid = verifyCRC(data, 6, data[6]);
            if (!result.crc_valid) {
                result.error_message = "CRC校验失败";
                logMessage(LogLevel::WARN, result.error_message);
                crc_errors_++;
                // 注意：CRC错误不一定意味着数据完全无效，继续解析
            }
        } else {
            result.crc_valid = true;
        }
        
        // 解析数据
        if (parseData(data, result.temperature_celsius, result.humidity_percent)) {
            result.success = true;
            successful_measurements_++;
            
            if (config_.enable_logging) {
                std::ostringstream oss;
                oss << "温湿度读取: " << std::fixed << std::setprecision(2) 
                    << result.temperature_celsius << "°C, " 
                    << result.humidity_percent << "%RH";
                logMessage(LogLevel::DEBUG, oss.str());
            }
        } else {
            result.error_message = "数据解析失败";
            logMessage(LogLevel::ERROR, result.error_message);
            failed_measurements_++;
        }
        
    } catch (const std::exception& e) {
        result.error_message = std::string("读取异常: ") + e.what();
        logMessage(LogLevel::ERROR, result.error_message);
        failed_measurements_++;
    }
    
    return result;
}

AHT20Result AHT20Sensor::readTemperature() {
    AHT20Result result = readData();
    // 只关心温度，清除湿度数据
    if (result.success) {
        result.humidity_percent = 0.0f;
    }
    return result;
}

AHT20Result AHT20Sensor::readHumidity() {
    AHT20Result result = readData();
    // 只关心湿度，清除温度数据
    if (result.success) {
        result.temperature_celsius = 0.0f;
    }
    return result;
}

AHT20Error AHT20Sensor::triggerMeasurement() {
    uint8_t trigger_cmd[3] = {CMD_TRIGGER, TRIGGER_PARAM1, TRIGGER_PARAM2};
    
    if (write(i2c_fd_, trigger_cmd, 3) != 3) {
        return AHT20Error::I2C_ERROR;
    }
    
    return AHT20Error::OK;
}

AHT20Error AHT20Sensor::readMeasurementData(uint8_t* data, size_t size) {
    if (read(i2c_fd_, data, size) != static_cast<ssize_t>(size)) {
        return AHT20Error::I2C_ERROR;
    }
    
    return AHT20Error::OK;
}

bool AHT20Sensor::parseData(const uint8_t* data, float& temperature, float& humidity) {
    // 检查状态位
    if (data[0] & STATUS_BUSY) {
        return false;  // 传感器仍在忙碌
    }
    
    // 解析湿度数据 (20位)
    uint32_t humidity_raw = ((uint32_t)data[1] << 12) | 
                           ((uint32_t)data[2] << 4) | 
                           ((uint32_t)data[3] >> 4);
    
    // 解析温度数据 (20位)
    uint32_t temperature_raw = (((uint32_t)data[3] & 0x0F) << 16) | 
                              ((uint32_t)data[4] << 8) | 
                              (uint32_t)data[5];
    
    // 转换为实际值
    humidity = (float)humidity_raw / 1048576.0f * 100.0f;
    temperature = (float)temperature_raw / 1048576.0f * 200.0f - 50.0f;
    
    // 范围检查
    if (humidity < 0.0f) humidity = 0.0f;
    if (humidity > 100.0f) humidity = 100.0f;
    if (temperature < -40.0f || temperature > 85.0f) {
        return false;  // 温度超出合理范围
    }
    
    return true;
}

uint8_t AHT20Sensor::calculateCRC(const uint8_t* data, size_t length) {
    uint8_t crc = 0xFF;
    
    for (size_t i = 0; i < length; i++) {
        crc ^= data[i];
        for (int j = 0; j < 8; j++) {
            if (crc & 0x80) {
                crc = (crc << 1) ^ 0x31;
            } else {
                crc <<= 1;
            }
        }
    }
    
    return crc;
}

bool AHT20Sensor::verifyCRC(const uint8_t* data, size_t length, uint8_t expected_crc) {
    uint8_t calculated_crc = calculateCRC(data, length);
    return calculated_crc == expected_crc;
}

AHT20Error AHT20Sensor::waitForReady() {
    auto start_time = std::chrono::steady_clock::now();
    auto timeout = std::chrono::milliseconds(config_.measurement_timeout_ms);
    
    while (true) {
        uint8_t status;
        if (read(i2c_fd_, &status, 1) != 1) {
            return AHT20Error::I2C_ERROR;
        }
        
        if (!(status & STATUS_BUSY)) {
            return AHT20Error::OK;  // 传感器就绪
        }
        
        auto current_time = std::chrono::steady_clock::now();
        if (current_time - start_time > timeout) {
            return AHT20Error::TIMEOUT_ERROR;
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
}

AHT20Error AHT20Sensor::softReset() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    logMessage(LogLevel::INFO, "执行AHT20软复位...");
    
    uint8_t reset_cmd = CMD_SOFT_RESET;
    if (write(i2c_fd_, &reset_cmd, 1) != 1) {
        return AHT20Error::I2C_ERROR;
    }
    
    // 等待复位完成
    std::this_thread::sleep_for(std::chrono::milliseconds(20));
    
    // 重新初始化
    initialized_ = false;
    return initialize();
}

AHT20Result AHT20Sensor::selfTest() {
    logMessage(LogLevel::INFO, "开始AHT20自检测试...");
    
    AHT20Result result = readData();
    
    if (result.success) {
        logMessage(LogLevel::INFO, "自检测试通过");
    } else {
        logMessage(LogLevel::ERROR, "自检测试失败: " + result.error_message);
    }
    
    return result;
}

const AHT20Config& AHT20Sensor::getConfig() const {
    return config_;
}

AHT20Error AHT20Sensor::setConfig(const AHT20Config& config) {
    if (!validateAHT20Config(config)) {
        return AHT20Error::PARAM_ERROR;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    config_ = config;
    
    // 如果已初始化，需要重新初始化
    if (initialized_) {
        cleanup();
        return initialize();
    }
    
    return AHT20Error::OK;
}

bool AHT20Sensor::isInitialized() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return initialized_;
}

std::string AHT20Sensor::getStatusInfo() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    std::ostringstream oss;
    oss << "AHT20传感器状态:\n";
    oss << "  初始化状态: " << (initialized_ ? "已初始化" : "未初始化") << "\n";
    oss << "  I2C设备: " << config_.i2c_device << "\n";
    oss << "  I2C地址: 0x" << std::hex << std::uppercase 
        << static_cast<int>(config_.i2c_address) << std::dec << "\n";
    oss << "  总测量次数: " << total_measurements_ << "\n";
    oss << "  成功次数: " << successful_measurements_ << "\n";
    oss << "  失败次数: " << failed_measurements_ << "\n";
    oss << "  CRC错误: " << crc_errors_ << "\n";
    
    if (total_measurements_ > 0) {
        float success_rate = (float)successful_measurements_ / total_measurements_ * 100.0f;
        oss << "  成功率: " << std::fixed << std::setprecision(1) 
            << success_rate << "%";
    }
    
    return oss.str();
}

uint32_t AHT20Sensor::getSensorID() {
    // AHT20没有专门的ID寄存器，返回固定值表示AHT20
    return 0x41485420;  // "AHT "的ASCII码
}

void AHT20Sensor::logMessage(LogLevel level, const std::string& message) {
    if (logger_) {
        logger_->log(level, message);
    }
}

// 工具函数实现
std::string aht20ErrorToString(AHT20Error error) {
    switch (error) {
        case AHT20Error::OK: return "成功";
        case AHT20Error::INIT_ERROR: return "初始化错误";
        case AHT20Error::I2C_ERROR: return "I2C错误";
        case AHT20Error::TIMEOUT_ERROR: return "超时错误";
        case AHT20Error::CRC_ERROR: return "CRC校验错误";
        case AHT20Error::PARAM_ERROR: return "参数错误";
        case AHT20Error::NOT_INITIALIZED: return "未初始化";
        case AHT20Error::DEVICE_NOT_FOUND: return "设备未找到";
        default: return "未知错误";
    }
}

AHT20Config createDefaultAHT20Config() {
    return AHT20Config{};
}

bool validateAHT20Config(const AHT20Config& config) {
    return !config.i2c_device.empty() &&
           config.i2c_address > 0 &&
           config.measurement_delay_ms > 0 &&
           config.init_delay_ms >= 0 &&
           config.measurement_timeout_ms > 0;
}

} // namespace greenland
