#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <linux/i2c-dev.h>
#include <string.h>
#include <errno.h>
#include <time.h>
#include "aht20.h"

// 全局变量
static int i2c_fd = -1;
static int initialized = 0;
static logger_t g_logger = NULL;
static bool g_logging_enabled = true;

// 内部函数声明
static int aht20_i2c_write(uint8_t* data, size_t len);
static int aht20_i2c_read(uint8_t* data, size_t len);
static int aht20_wait_ready(int timeout_ms);

// 初始化AHT20传感器
int aht20_init(void) {
    printf("正在初始化 AHT20 传感器...\n");

    // 打开I2C设备
    i2c_fd = open(AHT20_I2C_BUS, O_RDWR);
    if (i2c_fd < 0) {
        printf("错误: 无法打开I2C设备 %s: %s\n", AHT20_I2C_BUS, strerror(errno));
        return AHT20_ERROR_I2C;
    }

    // 设置I2C从设备地址
    if (ioctl(i2c_fd, I2C_SLAVE, AHT20_I2C_ADDR) < 0) {
        printf("错误: 无法设置I2C从设备地址 0x%02X: %s\n", AHT20_I2C_ADDR, strerror(errno));
        close(i2c_fd);
        i2c_fd = -1;
        return AHT20_ERROR_I2C;
    }

    // 等待传感器启动
    usleep(40000); // 40ms

    // 检查传感器状态
    uint8_t status;
    if (aht20_get_status(&status) != AHT20_OK) {
        printf("错误: 无法读取AHT20状态\n");
        close(i2c_fd);
        i2c_fd = -1;
        return AHT20_ERROR_I2C;
    }

    printf("AHT20 状态: 0x%02X\n", status);

    // 检查是否已校准
    if (!(status & AHT20_STATUS_CALIBRATED)) {
        printf("AHT20 未校准，正在初始化...\n");

        // 发送初始化命令
        uint8_t init_cmd[3] = {AHT20_CMD_INIT, 0x08, 0x00};
        if (aht20_i2c_write(init_cmd, 3) != AHT20_OK) {
            printf("错误: 发送初始化命令失败\n");
            close(i2c_fd);
            i2c_fd = -1;
            return AHT20_ERROR_INIT;
        }

        // 等待初始化完成
        usleep(10000); // 10ms

        // 再次检查校准状态
        if (aht20_get_status(&status) != AHT20_OK) {
            printf("错误: 初始化后无法读取状态\n");
            close(i2c_fd);
            i2c_fd = -1;
            return AHT20_ERROR_I2C;
        }

        if (!(status & AHT20_STATUS_CALIBRATED)) {
            printf("错误: AHT20 校准失败\n");
            close(i2c_fd);
            i2c_fd = -1;
            return AHT20_ERROR_CALIBRATION;
        }
    }

    initialized = 1;
    printf("✅ AHT20 初始化成功\n");
    return AHT20_OK;
}

// 释放AHT20资源
void aht20_deinit(void) {
    if (i2c_fd >= 0) {
        close(i2c_fd);
        i2c_fd = -1;
    }
    initialized = 0;
    printf("AHT20 资源已释放\n");
}

// 读取温湿度数据
int aht20_read_data(aht20_data_t* data) {
    if (!initialized || i2c_fd < 0) {
        printf("错误: AHT20 未初始化\n");
        return AHT20_ERROR_INIT;
    }

    if (data == NULL) {
        printf("错误: 数据指针为空\n");
        return AHT20_ERROR_INIT;
    }

    // 触发测量
    if (aht20_trigger_measurement() != AHT20_OK) {
        printf("错误: 触发测量失败\n");
        return AHT20_ERROR_I2C;
    }

    // 等待测量完成
    if (aht20_wait_ready(100) != AHT20_OK) {
        printf("错误: 等待测量完成超时\n");
        return AHT20_ERROR_TIMEOUT;
    }

    // 读取测量数据
    uint8_t raw_data[7];
    if (aht20_i2c_read(raw_data, 7) != AHT20_OK) {
        printf("错误: 读取测量数据失败\n");
        return AHT20_ERROR_I2C;
    }

    // 检查状态位
    if (raw_data[0] & AHT20_STATUS_BUSY) {
        printf("错误: 传感器仍在忙碌状态\n");
        return AHT20_ERROR_TIMEOUT;
    }

    // 解析湿度数据 (20位)
    uint32_t humidity_raw = ((uint32_t)raw_data[1] << 12) |
                           ((uint32_t)raw_data[2] << 4) |
                           ((uint32_t)raw_data[3] >> 4);

    // 解析温度数据 (20位)
    uint32_t temperature_raw = (((uint32_t)raw_data[3] & 0x0F) << 16) |
                              ((uint32_t)raw_data[4] << 8) |
                              (uint32_t)raw_data[5];

    // 转换为实际值
    data->humidity = (float)humidity_raw / 1048576.0f * 100.0f;
    data->temperature = (float)temperature_raw / 1048576.0f * 200.0f - 50.0f;

    return AHT20_OK;
}

// 获取传感器状态
int aht20_get_status(uint8_t* status) {
    if (i2c_fd < 0) {
        return AHT20_ERROR_INIT;
    }

    // AHT20在初始化时不需要发送状态命令，直接读取即可
    if (aht20_i2c_read(status, 1) != AHT20_OK) {
        return AHT20_ERROR_I2C;
    }

    return AHT20_OK;
}

// 软复位
int aht20_soft_reset(void) {
    if (!initialized || i2c_fd < 0) {
        return AHT20_ERROR_INIT;
    }

    uint8_t cmd = AHT20_CMD_SOFT_RESET;
    if (aht20_i2c_write(&cmd, 1) != AHT20_OK) {
        return AHT20_ERROR_I2C;
    }

    usleep(20000); // 20ms
    return AHT20_OK;
}

// 触发测量
int aht20_trigger_measurement(void) {
    if (!initialized || i2c_fd < 0) {
        return AHT20_ERROR_INIT;
    }

    uint8_t cmd[3] = {AHT20_CMD_TRIGGER, 0x33, 0x00};
    return aht20_i2c_write(cmd, 3);
}

// 检查是否忙碌
int aht20_is_busy(void) {
    uint8_t status;
    if (aht20_get_status(&status) != AHT20_OK) {
        return -1;
    }
    return (status & AHT20_STATUS_BUSY) ? 1 : 0;
}

// 检查是否已校准
int aht20_is_calibrated(void) {
    uint8_t status;
    if (aht20_get_status(&status) != AHT20_OK) {
        return -1;
    }
    return (status & AHT20_STATUS_CALIBRATED) ? 1 : 0;
}

// 简化接口实现
int aht20_read_temperature(float* temperature) {
    if (!temperature) {
        return AHT20_ERROR_INIT;
    }

    aht20_data_t data;
    int result = aht20_read_data(&data);
    if (result == AHT20_OK) {
        *temperature = data.temperature;
    }
    return result;
}

int aht20_read_humidity(float* humidity) {
    if (!humidity) {
        return AHT20_ERROR_INIT;
    }

    aht20_data_t data;
    int result = aht20_read_data(&data);
    if (result == AHT20_OK) {
        *humidity = data.humidity;
    }
    return result;
}

// 兼容旧接口
int aht20_read_data_legacy(void* data, size_t size) {
    printf("读取 aht20 数据 (兼容模式)\n");
    if (size < sizeof(aht20_data_t)) {
        return AHT20_ERROR_INIT;
    }
    return aht20_read_data((aht20_data_t*)data);
}

// 内部函数实现

// I2C写操作
static int aht20_i2c_write(uint8_t* data, size_t len) {
    if (write(i2c_fd, data, len) != (ssize_t)len) {
        printf("错误: I2C写操作失败: %s\n", strerror(errno));
        return AHT20_ERROR_I2C;
    }
    return AHT20_OK;
}

// I2C读操作
static int aht20_i2c_read(uint8_t* data, size_t len) {
    if (read(i2c_fd, data, len) != (ssize_t)len) {
        printf("错误: I2C读操作失败: %s\n", strerror(errno));
        return AHT20_ERROR_I2C;
    }
    return AHT20_OK;
}

// 等待传感器就绪
static int aht20_wait_ready(int timeout_ms) {
    int elapsed = 0;
    const int check_interval = 5; // 5ms

    while (elapsed < timeout_ms) {
        int busy = aht20_is_busy();
        if (busy < 0) {
            return AHT20_ERROR_I2C;
        }
        if (!busy) {
            return AHT20_OK;
        }

        usleep(check_interval * 1000);
        elapsed += check_interval;
    }

    return AHT20_ERROR_TIMEOUT;
}

// 日志相关函数实现
int aht20_set_logger(logger_t logger) {
    g_logger = logger;
    return AHT20_OK;
}

logger_t aht20_get_logger(void) {
    return g_logger;
}

int aht20_enable_logging(bool enable) {
    g_logging_enabled = enable;
    return AHT20_OK;
}

