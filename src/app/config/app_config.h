/**
 * @file app_config.h
 * @brief GreenLand应用程序配置管理
 * <AUTHOR>
 * @date 2024
 */

#ifndef APP_CONFIG_H
#define APP_CONFIG_H

#include <stdbool.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// 应用程序版本信息
#define APP_NAME "GreenLand"
#define APP_VERSION "1.0.0"
#define APP_AUTHOR "刘旭"
#define APP_DESCRIPTION "智能农业监控系统"

// 默认配置路径
#define DEFAULT_CONFIG_FILE "config/greenland.conf"
#define DEFAULT_LOG_DIR "Log"
#define DEFAULT_DATA_DIR "Data"

// 传感器配置
typedef struct {
    bool enable_water_level;    // 启用水位检测
    bool enable_temperature;    // 启用温湿度检测
    bool enable_light;          // 启用光照检测
    bool enable_camera;         // 启用摄像头
    
    // 水位传感器配置
    struct {
        uint8_t trig_pin;       // 触发引脚
        uint8_t echo_pin;       // 回响引脚
        float max_water_level;  // 最大水位(cm)
        float sensor_height;    // 传感器高度(cm)
        int sample_interval;    // 采样间隔(秒)
    } water_level;
    
    // 温湿度传感器配置
    struct {
        uint8_t i2c_bus;        // I2C总线
        uint8_t i2c_addr;       // I2C地址
        int sample_interval;    // 采样间隔(秒)
    } temperature;
    
    // 光照传感器配置
    struct {
        uint8_t i2c_bus;        // I2C总线
        uint8_t i2c_addr;       // I2C地址
        int sample_interval;    // 采样间隔(秒)
    } light;
    
    // 摄像头配置
    struct {
        int device_id;          // 设备ID
        int width;              // 分辨率宽度
        int height;             // 分辨率高度
        int rotation;           // 旋转角度
        bool auto_capture;      // 自动拍照
        int capture_interval;   // 拍照间隔(秒)
    } camera;
} sensor_config_t;

// 系统配置
typedef struct {
    char app_name[64];          // 应用程序名称
    char version[32];           // 版本号
    char log_level[16];         // 日志级别
    char log_dir[256];          // 日志目录
    char data_dir[256];         // 数据目录
    bool enable_web_server;     // 启用Web服务器
    int web_port;               // Web端口
    bool enable_mqtt;           // 启用MQTT
    char mqtt_broker[256];      // MQTT代理地址
    int mqtt_port;              // MQTT端口
    sensor_config_t sensors;    // 传感器配置
} app_config_t;

/**
 * @brief 获取默认应用程序配置
 * @param config 配置结构体指针
 */
void app_config_get_default(app_config_t* config);

/**
 * @brief 从文件加载配置
 * @param config 配置结构体指针
 * @param filename 配置文件路径
 * @return 0成功，-1失败
 */
int app_config_load_from_file(app_config_t* config, const char* filename);

/**
 * @brief 保存配置到文件
 * @param config 配置结构体指针
 * @param filename 配置文件路径
 * @return 0成功，-1失败
 */
int app_config_save_to_file(const app_config_t* config, const char* filename);

/**
 * @brief 验证配置有效性
 * @param config 配置结构体指针
 * @return true有效，false无效
 */
bool app_config_validate(const app_config_t* config);

/**
 * @brief 打印配置信息
 * @param config 配置结构体指针
 */
void app_config_print(const app_config_t* config);

#ifdef __cplusplus
}
#endif

#endif // APP_CONFIG_H
