/**
 * @file app_config.c
 * @brief GreenLand应用程序配置管理实现
 * <AUTHOR>
 * @date 2024
 */

#include "app_config.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <errno.h>

/**
 * @brief 获取默认应用程序配置
 */
void app_config_get_default(app_config_t* config) {
    if (!config) return;
    
    memset(config, 0, sizeof(app_config_t));
    
    // 基本信息
    strncpy(config->app_name, APP_NAME, sizeof(config->app_name) - 1);
    strncpy(config->version, APP_VERSION, sizeof(config->version) - 1);
    strncpy(config->log_level, "INFO", sizeof(config->log_level) - 1);
    strncpy(config->log_dir, DEFAULT_LOG_DIR, sizeof(config->log_dir) - 1);
    strncpy(config->data_dir, DEFAULT_DATA_DIR, sizeof(config->data_dir) - 1);
    
    // 网络配置
    config->enable_web_server = false;
    config->web_port = 8080;
    config->enable_mqtt = false;
    strncpy(config->mqtt_broker, "localhost", sizeof(config->mqtt_broker) - 1);
    config->mqtt_port = 1883;
    
    // 传感器启用状态
    config->sensors.enable_water_level = true;
    config->sensors.enable_temperature = true;
    config->sensors.enable_light = true;
    config->sensors.enable_camera = true;
    
    // 水位传感器配置 (Orange Pi Zero 2W)
    config->sensors.water_level.trig_pin = 22;      // wPi 22 (GPIO 268)
    config->sensors.water_level.echo_pin = 23;      // wPi 23 (GPIO 258)
    config->sensors.water_level.max_water_level = 35.0;
    config->sensors.water_level.sensor_height = 40.0;
    config->sensors.water_level.sample_interval = 5;
    
    // 温湿度传感器配置
    config->sensors.temperature.i2c_bus = 1;
    config->sensors.temperature.i2c_addr = 0x38;    // AHT20
    config->sensors.temperature.sample_interval = 10;
    
    // 光照传感器配置
    config->sensors.light.i2c_bus = 1;
    config->sensors.light.i2c_addr = 0x23;          // BH1750
    config->sensors.light.sample_interval = 10;
    
    // 摄像头配置
    config->sensors.camera.device_id = 0;
    config->sensors.camera.width = 2592;            // 500万像素
    config->sensors.camera.height = 1944;
    config->sensors.camera.rotation = 0;            // 不旋转
    config->sensors.camera.auto_capture = false;
    config->sensors.camera.capture_interval = 300;  // 5分钟
}

/**
 * @brief 创建目录
 */
static int create_directory(const char* path) {
    struct stat st = {0};
    if (stat(path, &st) == -1) {
        if (mkdir(path, 0755) == -1) {
            return -1;
        }
    }
    return 0;
}

/**
 * @brief 从文件加载配置
 */
int app_config_load_from_file(app_config_t* config, const char* filename) {
    if (!config || !filename) return -1;
    
    FILE* file = fopen(filename, "r");
    if (!file) {
        // 文件不存在，使用默认配置
        app_config_get_default(config);
        return 0;
    }
    
    // 先获取默认配置
    app_config_get_default(config);
    
    char line[512];
    char key[128], value[256];
    
    while (fgets(line, sizeof(line), file)) {
        // 跳过注释和空行
        if (line[0] == '#' || line[0] == '\n' || line[0] == '\r') {
            continue;
        }
        
        // 解析键值对
        if (sscanf(line, "%127[^=]=%255[^\n\r]", key, value) == 2) {
            // 去除键值前后空格
            char* key_trim = key;
            char* value_trim = value;
            while (*key_trim == ' ') key_trim++;
            while (*value_trim == ' ') value_trim++;
            
            // 解析配置项
            if (strcmp(key_trim, "log_level") == 0) {
                strncpy(config->log_level, value_trim, sizeof(config->log_level) - 1);
            } else if (strcmp(key_trim, "enable_water_level") == 0) {
                config->sensors.enable_water_level = (strcmp(value_trim, "true") == 0);
            } else if (strcmp(key_trim, "enable_temperature") == 0) {
                config->sensors.enable_temperature = (strcmp(value_trim, "true") == 0);
            } else if (strcmp(key_trim, "enable_light") == 0) {
                config->sensors.enable_light = (strcmp(value_trim, "true") == 0);
            } else if (strcmp(key_trim, "enable_camera") == 0) {
                config->sensors.enable_camera = (strcmp(value_trim, "true") == 0);
            }
        }
    }
    
    fclose(file);
    return 0;
}

/**
 * @brief 保存配置到文件
 */
int app_config_save_to_file(const app_config_t* config, const char* filename) {
    if (!config || !filename) return -1;
    
    // 创建配置目录
    char dir_path[256];
    strncpy(dir_path, filename, sizeof(dir_path) - 1);
    char* last_slash = strrchr(dir_path, '/');
    if (last_slash) {
        *last_slash = '\0';
        if (create_directory(dir_path) != 0) {
            return -1;
        }
    }
    
    FILE* file = fopen(filename, "w");
    if (!file) {
        return -1;
    }
    
    fprintf(file, "# GreenLand 智能农业监控系统配置文件\n");
    fprintf(file, "# 作者: %s\n", APP_AUTHOR);
    fprintf(file, "# 版本: %s\n\n", APP_VERSION);
    
    fprintf(file, "# 基本配置\n");
    fprintf(file, "log_level=%s\n", config->log_level);
    fprintf(file, "log_dir=%s\n", config->log_dir);
    fprintf(file, "data_dir=%s\n\n", config->data_dir);
    
    fprintf(file, "# 传感器启用配置\n");
    fprintf(file, "enable_water_level=%s\n", config->sensors.enable_water_level ? "true" : "false");
    fprintf(file, "enable_temperature=%s\n", config->sensors.enable_temperature ? "true" : "false");
    fprintf(file, "enable_light=%s\n", config->sensors.enable_light ? "true" : "false");
    fprintf(file, "enable_camera=%s\n", config->sensors.enable_camera ? "true" : "false");
    
    fclose(file);
    return 0;
}

/**
 * @brief 验证配置有效性
 */
bool app_config_validate(const app_config_t* config) {
    if (!config) return false;
    
    // 检查基本配置
    if (strlen(config->app_name) == 0) return false;
    if (strlen(config->version) == 0) return false;
    
    return true;
}

/**
 * @brief 打印配置信息
 */
void app_config_print(const app_config_t* config) {
    if (!config) return;
    
    printf("=== GreenLand 配置信息 ===\n");
    printf("应用程序: %s v%s\n", config->app_name, config->version);
    printf("日志级别: %s\n", config->log_level);
    printf("日志目录: %s\n", config->log_dir);
    printf("数据目录: %s\n", config->data_dir);
    printf("\n");
    
    printf("=== 传感器配置 ===\n");
    printf("水位检测: %s\n", config->sensors.enable_water_level ? "启用" : "禁用");
    printf("温湿度检测: %s\n", config->sensors.enable_temperature ? "启用" : "禁用");
    printf("光照检测: %s\n", config->sensors.enable_light ? "启用" : "禁用");
    printf("摄像头: %s\n", config->sensors.enable_camera ? "启用" : "禁用");
    printf("\n");
}
