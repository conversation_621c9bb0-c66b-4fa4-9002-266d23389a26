#include <iostream>
#include <thread>
#include <chrono>
#include <fstream>
#include <iomanip>
#include <sstream>
#include <mutex>
#include <atomic>
#include <signal.h>
#include <unistd.h>
#include <sys/stat.h>

// 传感器头文件
extern "C" {
    #include "aht20.h"
    #include "bh1750.h"
    #include "hcsr04.h"
}

using namespace std;

// 全局变量
static atomic<bool> running(true);
static mutex log_mutex;

// 信号处理函数
void signal_handler(int sig) {
    cout << "\n收到信号 " << sig << "，正在退出..." << endl;
    running = false;
}

// 获取当前时间字符串
string get_current_time() {
    auto now = chrono::system_clock::now();
    auto time_t = chrono::system_clock::to_time_t(now);
    auto ms = chrono::duration_cast<chrono::milliseconds>(
        now.time_since_epoch()) % 1000;

    stringstream ss;
    ss << put_time(localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    ss << "." << setfill('0') << setw(3) << ms.count();
    return ss.str();
}

// 检查日志目录是否存在
bool check_log_directory() {
    struct stat st = {0};
    if (stat("Log", &st) == -1) {
        cerr << "❌ Log目录不存在，请先创建Log目录" << endl;
        return false;
    }
    return true;
}

// 写入日志文件
void write_log(const string& filename, const string& message) {
    lock_guard<mutex> lock(log_mutex);

    ofstream log_file("Log/" + filename, ios::app);
    if (log_file.is_open()) {
        log_file << get_current_time() << " - " << message << endl;
        log_file.close();
    } else {
        cerr << "❌ 无法写入日志文件: " << filename << endl;
    }
}

// AHT20传感器线程
void aht20_thread() {
    cout << "🌡️  AHT20线程启动" << endl;

    // 初始化AHT20
    if (aht20_init() != AHT20_OK) {
        string error_msg = "AHT20初始化失败";
        cerr << "❌ " << error_msg << endl;
        write_log("aht20_error.log", error_msg);
        return;
    }

    write_log("aht20.log", "AHT20传感器初始化成功");
    cout << "✅ AHT20初始化成功" << endl;

    int read_count = 0;
    int error_count = 0;

    while (running) {
        aht20_data_t data;
        int result = aht20_read_data(&data);

        if (result == AHT20_OK) {
            read_count++;

            // 格式化数据
            stringstream ss;
            ss << fixed << setprecision(2);
            ss << "温度=" << data.temperature << "°C, ";
            ss << "湿度=" << data.humidity << "%, ";
            ss << "读取次数=" << read_count;

            string log_message = ss.str();
            write_log("aht20.log", log_message);

            // 控制台输出（每10次输出一次）
            if (read_count % 10 == 0) {
                cout << "🌡️  AHT20: " << log_message << endl;
            }

            // 检查数据异常
            if (data.temperature < -40 || data.temperature > 85) {
                string warning = "温度异常: " + to_string(data.temperature) + "°C";
                write_log("aht20_warning.log", warning);
            }

            if (data.humidity < 0 || data.humidity > 100) {
                string warning = "湿度异常: " + to_string(data.humidity) + "%";
                write_log("aht20_warning.log", warning);
            }

        } else {
            error_count++;
            string error_msg = "读取失败 (错误码: " + to_string(result) +
                             "), 错误次数: " + to_string(error_count);
            write_log("aht20_error.log", error_msg);

            if (error_count % 5 == 0) {
                cerr << "❌ AHT20: " << error_msg << endl;
            }
        }

        // 等待2秒
        this_thread::sleep_for(chrono::seconds(2));
    }

    // 清理资源
    aht20_deinit();
    write_log("aht20.log", "AHT20线程退出，总读取次数: " + to_string(read_count) +
              ", 错误次数: " + to_string(error_count));
    cout << "🌡️  AHT20线程退出" << endl;
}

// BH1750传感器线程
void bh1750_thread() {
    cout << "💡 BH1750线程启动" << endl;

    // 初始化BH1750
    if (bh1750_init() != BH1750_OK) {
        string error_msg = "BH1750初始化失败";
        cerr << "❌ " << error_msg << endl;
        write_log("bh1750_error.log", error_msg);
        return;
    }

    write_log("bh1750.log", "BH1750传感器初始化成功");
    cout << "✅ BH1750初始化成功" << endl;

    int read_count = 0;
    int error_count = 0;

    while (running) {
        bh1750_data_t data;
        int result = bh1750_read_data(&data);

        if (result == BH1750_OK) {
            read_count++;

            // 格式化数据
            stringstream ss;
            ss << fixed << setprecision(2);
            ss << "光照强度=" << data.lux << " lx, ";
            ss << "原始数据=" << data.raw_data << ", ";
            ss << "读取次数=" << read_count;

            string log_message = ss.str();
            write_log("bh1750.log", log_message);

            // 控制台输出（每10次输出一次）
            if (read_count % 10 == 0) {
                cout << "💡 BH1750: " << log_message << endl;
            }

            // 检查数据异常
            if (data.lux < 0 || data.lux > 65535) {
                string warning = "光照强度异常: " + to_string(data.lux) + " lx";
                write_log("bh1750_warning.log", warning);
            }

            // 光照等级判断
            string light_level;
            if (data.lux < 10) {
                light_level = "黑暗";
            } else if (data.lux < 50) {
                light_level = "昏暗";
            } else if (data.lux < 200) {
                light_level = "室内照明";
            } else if (data.lux < 1000) {
                light_level = "明亮";
            } else {
                light_level = "强光";
            }

            // 每50次记录一次光照等级
            if (read_count % 50 == 0) {
                write_log("bh1750.log", "光照等级: " + light_level);
            }

        } else {
            error_count++;
            string error_msg = "读取失败 (错误码: " + to_string(result) +
                             "), 错误次数: " + to_string(error_count);
            write_log("bh1750_error.log", error_msg);

            if (error_count % 5 == 0) {
                cerr << "❌ BH1750: " << error_msg << endl;
            }
        }

        // 等待3秒
        this_thread::sleep_for(chrono::seconds(3));
    }

    // 清理资源
    bh1750_deinit();
    write_log("bh1750.log", "BH1750线程退出，总读取次数: " + to_string(read_count) +
              ", 错误次数: " + to_string(error_count));
    cout << "💡 BH1750线程退出" << endl;
}

// HC-SR04传感器线程
void hcsr04_thread() {
    cout << "🌊 HC-SR04线程启动" << endl;

    // 初始化HC-SR04
    if (hcsr04_init() != HCSR04_OK) {
        string error_msg = "HC-SR04初始化失败";
        cerr << "❌ " << error_msg << endl;
        write_log("hcsr04_error.log", error_msg);
        return;
    }

    write_log("hcsr04.log", "HC-SR04传感器初始化成功");
    cout << "✅ HC-SR04初始化成功" << endl;

    // 设置水箱参数
    hcsr04_set_tank_height(100.0f);  // 100cm 水箱
    hcsr04_set_sensor_offset(5.0f);  // 5cm 偏移

    int read_count = 0;
    int error_count = 0;

    while (running) {
        hcsr04_data_t data;
        int result = hcsr04_read_averaged(&data, 3);  // 3次采样平均

        if (result == HCSR04_OK && data.valid) {
            read_count++;

            // 格式化数据
            stringstream ss;
            ss << fixed << setprecision(2);
            ss << "距离=" << data.distance_cm << "cm, ";
            ss << "水位=" << data.water_level_cm << "cm (" << data.water_level_percent << "%), ";
            ss << "回响时间=" << data.echo_time_us << "μs, ";
            ss << "读取次数=" << read_count;

            string log_message = ss.str();
            write_log("hcsr04.log", log_message);

            // 控制台输出（每5次输出一次）
            if (read_count % 5 == 0) {
                cout << "🌊 HC-SR04: " << log_message << endl;
            }

            // 水位报警
            if (data.water_level_percent < 10.0f) {
                string warning = "水位过低: " + to_string(data.water_level_percent) + "%";
                write_log("hcsr04_warning.log", warning);
                cout << "⚠️  " << warning << endl;
            } else if (data.water_level_percent > 90.0f) {
                string warning = "水位过高: " + to_string(data.water_level_percent) + "%";
                write_log("hcsr04_warning.log", warning);
                cout << "⚠️  " << warning << endl;
            }

            // 检查数据异常
            if (data.distance_cm < 2.0 || data.distance_cm > 400.0) {
                string warning = "距离异常: " + to_string(data.distance_cm) + "cm";
                write_log("hcsr04_warning.log", warning);
            }

        } else {
            error_count++;
            string error_msg = "读取失败 (错误码: " + to_string(result) +
                             "), 错误次数: " + to_string(error_count);
            write_log("hcsr04_error.log", error_msg);

            if (error_count % 3 == 0) {
                cerr << "❌ HC-SR04: " << error_msg << endl;
            }
        }

        // 等待5秒（超声波传感器建议间隔）
        this_thread::sleep_for(chrono::seconds(5));
    }

    // 清理资源
    hcsr04_deinit();
    write_log("hcsr04.log", "HC-SR04线程退出，总读取次数: " + to_string(read_count) +
              ", 错误次数: " + to_string(error_count));
    cout << "🌊 HC-SR04线程退出" << endl;
}

// 系统监控线程
void system_monitor_thread() {
    cout << "📊 系统监控线程启动" << endl;
    write_log("system.log", "系统监控线程启动");

    int monitor_count = 0;

    while (running) {
        monitor_count++;

        // 每分钟记录一次系统状态
        if (monitor_count % 20 == 0) { // 20 * 3秒 = 60秒
            stringstream ss;
            ss << "系统运行正常，监控次数: " << monitor_count;
            write_log("system.log", ss.str());
            cout << "📊 " << ss.str() << endl;
        }

        // 每10分钟记录一次详细状态
        if (monitor_count % 200 == 0) { // 200 * 3秒 = 600秒 = 10分钟
            write_log("system.log", "=== 系统状态报告 ===");
            write_log("system.log", "运行时间: " + to_string(monitor_count * 3) + " 秒");
            write_log("system.log", "AHT20线程: 运行中");
            write_log("system.log", "BH1750线程: 运行中");
            write_log("system.log", "HC-SR04线程: 运行中");
            write_log("system.log", "系统监控线程: 运行中");
            write_log("system.log", "========================");
        }

        // 等待3秒
        this_thread::sleep_for(chrono::seconds(3));
    }

    write_log("system.log", "系统监控线程退出，总监控次数: " + to_string(monitor_count));
    cout << "📊 系统监控线程退出" << endl;
}

// 主函数
int main() {
    cout << "🚀 GreenLand传感器监控系统启动" << endl;
    cout << "================================" << endl;

    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 检查日志目录
    if (!check_log_directory()) {
        cerr << "❌ 请先创建Log目录，程序退出" << endl;
        return 1;
    }

    // 记录系统启动
    write_log("system.log", "=== GreenLand系统启动 ===");
    write_log("system.log", "版本: 1.0.0");
    write_log("system.log", "传感器: AHT20(温湿度), BH1750(光照), HC-SR04(水位)");
    write_log("system.log", "========================");

    cout << "✅ 日志系统初始化完成" << endl;
    cout << "📁 日志文件保存在 Log/ 目录下" << endl;
    cout << "📝 日志文件:" << endl;
    cout << "   - aht20.log (AHT20数据日志)" << endl;
    cout << "   - bh1750.log (BH1750数据日志)" << endl;
    cout << "   - hcsr04.log (HC-SR04数据日志)" << endl;
    cout << "   - system.log (系统日志)" << endl;
    cout << "   - *_error.log (错误日志)" << endl;
    cout << "   - *_warning.log (警告日志)" << endl;
    cout << "================================" << endl;

    // 启动传感器线程
    thread aht20_t(aht20_thread);
    thread bh1750_t(bh1750_thread);
    thread hcsr04_t(hcsr04_thread);
    thread monitor_t(system_monitor_thread);

    cout << "🔄 所有线程已启动，系统运行中..." << endl;
    cout << "💡 按 Ctrl+C 退出程序" << endl;
    cout << "================================" << endl;

    // 等待线程结束
    aht20_t.join();
    bh1750_t.join();
    hcsr04_t.join();
    monitor_t.join();

    // 记录系统关闭
    write_log("system.log", "=== GreenLand系统关闭 ===");
    cout << "🛑 GreenLand传感器监控系统已退出" << endl;

    return 0;
}