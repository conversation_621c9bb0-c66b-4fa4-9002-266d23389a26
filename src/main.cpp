/**
 * @file main.cpp
 * @brief GreenLand 智能农业监控系统 - 多线程传感器监控
 * <AUTHOR>
 * @date 2024
 */

#include <iostream>
#include <cstring>
#include <signal.h>
#include <pthread.h>
#include <unistd.h>
#include <sys/stat.h>
#include <ctime>
#include <cstdio>

extern "C" {
    #include "modules/logger/include/logger.h"
    #include "modules/hcsr04/include/hcsr04.h"
    #include "modules/aht20/include/aht20.h"
    #include "modules/bh1750/include/bh1750.h"
    #include "modules/camera/include/camera.h"
}

using namespace std;

static volatile int running = 1;

// 日志记录器
static logger_t system_logger = nullptr;    // 系统日志
static logger_t sensor_logger = nullptr;    // 传感器数据日志

// 线程句柄
static pthread_t water_sensor_thread;
static pthread_t temperature_sensor_thread;
static pthread_t light_sensor_thread;
static pthread_t camera_thread;

// 传感器数据结构
typedef struct {
    float distance;
    float water_level;
    float water_percentage;
    time_t last_update;
    int valid;
} water_sensor_data_t;

typedef struct {
    float temperature;
    float humidity;
    time_t last_update;
    int valid;
} temperature_sensor_data_t;

typedef struct {
    float light_intensity;
    time_t last_update;
    int valid;
} light_sensor_data_t;

static water_sensor_data_t g_water_data = {0};
static temperature_sensor_data_t g_temperature_data = {0};
static light_sensor_data_t g_light_data = {0};

static pthread_mutex_t water_data_mutex = PTHREAD_MUTEX_INITIALIZER;
static pthread_mutex_t temperature_data_mutex = PTHREAD_MUTEX_INITIALIZER;
static pthread_mutex_t light_data_mutex = PTHREAD_MUTEX_INITIALIZER;

static camera_t g_camera = nullptr;

void signal_handler(int sig) {
    cout << "\n收到信号 " << sig << "，正在退出..." << endl;
    running = 0;
}

// 创建必要的目录
int create_directories() {
    struct stat st;
    memset(&st, 0, sizeof(st));

    // 创建Log目录
    if (stat("Log", &st) == -1) {
        if (mkdir("Log", 0755) == -1) {
            cout << "❌ 无法创建Log目录" << endl;
            return -1;
        }
    }

    // 创建media目录
    if (stat("media", &st) == -1) {
        if (mkdir("media", 0755) == -1) {
            cout << "❌ 无法创建media目录" << endl;
            return -1;
        }
    }

    // 创建photos子目录
    if (stat("media/photos", &st) == -1) {
        if (mkdir("media/photos", 0755) == -1) {
            cout << "❌ 无法创建media/photos目录" << endl;
            return -1;
        }
    }

    // 创建videos子目录
    if (stat("media/videos", &st) == -1) {
        if (mkdir("media/videos", 0755) == -1) {
            cout << "❌ 无法创建media/videos目录" << endl;
            return -1;
        }
    }

    return 0;
}

// 初始化日志系统
int init_logging() {
    log_config_t config;

    // 系统日志 - 包含启动、错误、状态等信息
    logger_get_default_config(&config, "system");
    config.level = LOG_LEVEL_INFO;
    config.enable_console = true;
    system_logger = logger_create(&config);
    if (!system_logger) {
        cout << "❌ 系统日志初始化失败" << endl;
        return -1;
    }

    // 传感器数据日志 - 只记录传感器读取的数据
    logger_get_default_config(&config, "sensor_data");
    config.level = LOG_LEVEL_INFO;
    config.enable_console = false;  // 传感器数据不在控制台显示
    sensor_logger = logger_create(&config);
    if (!sensor_logger) {
        logger_error(system_logger, "传感器数据日志初始化失败");
        return -1;
    }

    logger_info(system_logger, "GreenLand 智能农业监控系统启动");
    logger_info(system_logger, "日志系统初始化完成 - 系统日志: system.log, 传感器数据: sensor_data.log");
    return 0;
}

// 水位传感器线程函数
void* water_sensor_thread_func(void* arg) {
    (void)arg; // 避免未使用参数警告

    logger_info(system_logger, "水位传感器线程启动");

    // 初始化传感器
    if (hcsr04_init() != HCSR04_OK) {
        logger_error(system_logger, "水位传感器初始化失败");
        return nullptr;
    }

    // 设置水箱参数
    hcsr04_set_tank_height(35.0f);  // 35cm水箱
    hcsr04_set_sensor_offset(3.0f); // 3cm偏移

    logger_info(system_logger, "水位传感器初始化成功，开始监控");

    while (running) {
        float distance, water_level;

        // 读取传感器数据
        if (hcsr04_read_distance(&distance) == HCSR04_OK &&
            hcsr04_read_water_level(&water_level) == HCSR04_OK) {

            // 更新全局数据
            pthread_mutex_lock(&water_data_mutex);
            g_water_data.distance = distance;
            g_water_data.water_level = water_level;
            g_water_data.water_percentage = (water_level / 35.0f) * 100.0f;
            g_water_data.last_update = time(nullptr);
            g_water_data.valid = 1;
            pthread_mutex_unlock(&water_data_mutex);

            // 记录传感器数据到专用日志
            logger_info(sensor_logger, "WATER,%.2f,%.2f,%.1f",
                       distance, water_level, g_water_data.water_percentage);

            // 水位报警记录到系统日志
            if (water_level < 5.0) {
                logger_warn(system_logger, "⚠️ 水位过低: %.2fcm", water_level);
            } else if (water_level > 30.0) {
                logger_info(system_logger, "💧 水位充足: %.2fcm", water_level);
            }
        } else {
            pthread_mutex_lock(&water_data_mutex);
            g_water_data.valid = 0;
            pthread_mutex_unlock(&water_data_mutex);

            logger_warn(system_logger, "水位传感器读取失败");
        }

        sleep(10); // 10秒间隔，避免超声波干扰
    }

    hcsr04_deinit();
    logger_info(system_logger, "水位传感器线程退出");
    return nullptr;
}

// AHT20温湿度传感器线程函数
void* temperature_sensor_thread_func(void* arg) {
    (void)arg; // 避免未使用参数警告

    logger_info(system_logger, "AHT20温湿度传感器线程启动");

    // 初始化传感器
    if (aht20_init() != AHT20_OK) {
        logger_error(system_logger, "AHT20传感器初始化失败");
        return nullptr;
    }

    logger_info(system_logger, "AHT20传感器初始化成功，开始监控");

    while (running) {
        float temperature, humidity;

        // 读取传感器数据
        if (aht20_read_temperature(&temperature) == AHT20_OK &&
            aht20_read_humidity(&humidity) == AHT20_OK) {

            // 更新全局数据
            pthread_mutex_lock(&temperature_data_mutex);
            g_temperature_data.temperature = temperature;
            g_temperature_data.humidity = humidity;
            g_temperature_data.last_update = time(nullptr);
            g_temperature_data.valid = 1;
            pthread_mutex_unlock(&temperature_data_mutex);

            // 记录传感器数据到专用日志
            logger_info(sensor_logger, "TEMPERATURE,%.2f,%.2f", temperature, humidity);

            // 温度报警记录到系统日志
            if (temperature > 35.0) {
                logger_warn(system_logger, "⚠️ 温度过高: %.2f°C", temperature);
            } else if (temperature < 5.0) {
                logger_warn(system_logger, "⚠️ 温度过低: %.2f°C", temperature);
            }

            // 湿度报警记录到系统日志
            if (humidity > 80.0) {
                logger_warn(system_logger, "⚠️ 湿度过高: %.2f%%", humidity);
            } else if (humidity < 30.0) {
                logger_warn(system_logger, "⚠️ 湿度过低: %.2f%%", humidity);
            }
        } else {
            pthread_mutex_lock(&temperature_data_mutex);
            g_temperature_data.valid = 0;
            pthread_mutex_unlock(&temperature_data_mutex);

            logger_warn(system_logger, "AHT20传感器读取失败");
        }

        sleep(10); // 10秒间隔
    }

    aht20_deinit();
    logger_info(system_logger, "AHT20温湿度传感器线程退出");
    return nullptr;
}

// BH1750光照传感器线程函数
void* light_sensor_thread_func(void* arg) {
    (void)arg; // 避免未使用参数警告

    logger_info(system_logger, "BH1750光照传感器线程启动");

    // 初始化传感器
    if (bh1750_init() != BH1750_OK) {
        logger_error(system_logger, "BH1750传感器初始化失败");
        return nullptr;
    }

    logger_info(system_logger, "BH1750传感器初始化成功，开始监控");

    while (running) {
        float light_intensity;

        // 读取传感器数据
        if (bh1750_read_light(&light_intensity) == BH1750_OK) {

            // 更新全局数据
            pthread_mutex_lock(&light_data_mutex);
            g_light_data.light_intensity = light_intensity;
            g_light_data.last_update = time(nullptr);
            g_light_data.valid = 1;
            pthread_mutex_unlock(&light_data_mutex);

            // 记录传感器数据到专用日志
            logger_info(sensor_logger, "LIGHT,%.2f", light_intensity);

            // 光照报警记录到系统日志
            if (light_intensity < 100.0) {
                logger_warn(system_logger, "⚠️ 光照不足: %.2f lux", light_intensity);
            } else if (light_intensity > 50000.0) {
                logger_warn(system_logger, "⚠️ 光照过强: %.2f lux", light_intensity);
            }
        } else {
            pthread_mutex_lock(&light_data_mutex);
            g_light_data.valid = 0;
            pthread_mutex_unlock(&light_data_mutex);

            logger_warn(system_logger, "BH1750传感器读取失败");
        }

        sleep(15); // 15秒间隔
    }

    bh1750_deinit();
    logger_info(system_logger, "BH1750光照传感器线程退出");
    return nullptr;
}

// 摄像头线程函数
void* camera_thread_func(void* arg) {
    (void)arg; // 避免未使用参数警告

    logger_info(system_logger, "摄像头线程启动");

    // 检查摄像头设备文件
    logger_info(system_logger, "检查摄像头设备...");

    // 手动检查常见的摄像头设备
    bool camera_found = false;
    for (int i = 0; i < 4; i++) {
        char device_path[32];
        snprintf(device_path, sizeof(device_path), "/dev/video%d", i);

        if (access(device_path, F_OK) == 0) {
            logger_info(system_logger, "发现摄像头设备: %s", device_path);
            camera_found = true;
        }
    }

    if (!camera_found) {
        logger_warn(system_logger, "未找到任何摄像头设备文件 (/dev/video*)");
        logger_warn(system_logger, "请检查: 1)USB摄像头是否连接 2)设备权限 3)驱动程序");
        return nullptr;
    }

    // 扫描摄像头设备
    int devices[4];
    int device_count = camera_scan_devices(devices, 4);

    logger_info(system_logger, "摄像头扫描结果: 发现 %d 个可用设备", device_count);

    if (device_count == 0) {
        logger_warn(system_logger, "摄像头设备扫描失败，可能原因:");
        logger_warn(system_logger, "1. 设备被其他程序占用");
        logger_warn(system_logger, "2. 权限不足 (尝试: sudo usermod -a -G video $USER)");
        logger_warn(system_logger, "3. OpenCV编译问题");
        return nullptr;
    }

    // 创建摄像头
    camera_config_t config;
    camera_get_default_config(&config);
    config.device_id = devices[0];
    config.width = 640;    // 降低分辨率提高兼容性
    config.height = 480;

    logger_info(system_logger, "使用摄像头设备 /dev/video%d，分辨率 %dx%d",
               config.device_id, config.width, config.height);

    g_camera = camera_create(&config);
    if (!g_camera) {
        logger_error(system_logger, "摄像头创建失败");
        return nullptr;
    }

    if (camera_init(g_camera) != CAMERA_OK) {
        logger_error(system_logger, "摄像头初始化失败，可能原因：设备被占用、权限不足或驱动问题");
        camera_destroy(g_camera);
        g_camera = nullptr;
        return nullptr;
    }

    logger_info(system_logger, "摄像头初始化成功，开始定时拍照 (60秒间隔)");

    int photo_count = 0;
    int failed_count = 0;

    while (running) {
        // 等待60秒或程序退出
        for (int i = 0; i < 60 && running; i++) {
            sleep(1);
        }

        if (!running) break;

        // 拍照
        char filename[64];
        time_t now = time(nullptr);
        struct tm *tm_info = localtime(&now);
        strftime(filename, sizeof(filename), "auto_%Y%m%d_%H%M%S", tm_info);

        logger_info(system_logger, "开始拍照: %s", filename);

        camera_result_t result = camera_quick_photo(g_camera, filename);
        if (result == CAMERA_OK) {
            photo_count++;
            failed_count = 0; // 重置失败计数
            logger_info(system_logger, "📸 自动拍照成功: %s.jpg (第%d张)", filename, photo_count);
            // 记录拍照事件到传感器日志
            logger_info(sensor_logger, "CAMERA,%s.jpg,%d", filename, photo_count);
        } else {
            failed_count++;
            logger_error(system_logger, "自动拍照失败: %s (连续失败%d次)",
                        camera_error_to_string(result), failed_count);

            // 连续失败5次后重新初始化摄像头
            if (failed_count >= 5) {
                logger_warn(system_logger, "连续拍照失败，尝试重新初始化摄像头");
                camera_destroy(g_camera);
                g_camera = camera_create(&config);
                if (g_camera && camera_init(g_camera) == CAMERA_OK) {
                    logger_info(system_logger, "摄像头重新初始化成功");
                    failed_count = 0;
                } else {
                    logger_error(system_logger, "摄像头重新初始化失败，退出摄像头线程");
                    break;
                }
            }
        }
    }

    if (g_camera) {
        camera_destroy(g_camera);
        g_camera = nullptr;
    }

    logger_info(system_logger, "摄像头线程退出，共拍摄 %d 张照片", photo_count);
    return nullptr;
}

// 显示传感器状态
void show_sensor_status() {
    char time_str[64];

    // 水位传感器状态
    pthread_mutex_lock(&water_data_mutex);
    if (g_water_data.valid) {
        strftime(time_str, sizeof(time_str), "%H:%M:%S", localtime(&g_water_data.last_update));
        cout << "💧 水位传感器: 距离=" << g_water_data.distance << "cm, 水位="
             << g_water_data.water_level << "cm (" << g_water_data.water_percentage
             << "%) [" << time_str << "]" << endl;
    } else {
        cout << "❌ 水位传感器: 数据无效" << endl;
    }
    pthread_mutex_unlock(&water_data_mutex);

    // 温湿度传感器状态
    pthread_mutex_lock(&temperature_data_mutex);
    if (g_temperature_data.valid) {
        strftime(time_str, sizeof(time_str), "%H:%M:%S", localtime(&g_temperature_data.last_update));
        cout << "🌡️ 温湿度传感器: 温度=" << g_temperature_data.temperature
             << "°C, 湿度=" << g_temperature_data.humidity << "% [" << time_str << "]" << endl;
    } else {
        cout << "❌ 温湿度传感器: 数据无效" << endl;
    }
    pthread_mutex_unlock(&temperature_data_mutex);

    // 光照传感器状态
    pthread_mutex_lock(&light_data_mutex);
    if (g_light_data.valid) {
        strftime(time_str, sizeof(time_str), "%H:%M:%S", localtime(&g_light_data.last_update));
        cout << "☀️ 光照传感器: 强度=" << g_light_data.light_intensity
             << " lux [" << time_str << "]" << endl;
    } else {
        cout << "❌ 光照传感器: 数据无效" << endl;
    }
    pthread_mutex_unlock(&light_data_mutex);

    if (g_camera) {
        cout << "📷 摄像头: 运行中 (60秒间隔自动拍照)" << endl;
    } else {
        cout << "❌ 摄像头: 未初始化" << endl;
    }
}

int GreenLand(int argc, char* argv[]) {
    cout << "🌱 GreenLand 智能农业监控系统" << endl;
    cout << "==============================" << endl;
    cout << "作者: aubuty" << endl;
    cout << "版本: 1.0.0" << endl;
    cout << "平台: Orange Pi Zero 2W" << endl << endl;

    // 简单参数解析
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "-h") == 0) {
            cout << "用法: " << argv[0] << " [-t] [-h] [-i]" << endl;
            cout << "选项:" << endl;
            cout << "  -t    运行测试模式" << endl;
            cout << "  -h    显示帮助信息" << endl;
            cout << "  -i    显示系统信息" << endl;
            return 0;
        } else if (strcmp(argv[i], "-i") == 0) {
            cout << "📊 系统信息" << endl;
            cout << "============" << endl;
            cout << "程序名称: GreenLand 智能农业监控系统" << endl;
            cout << "版本: 1.0.0" << endl;
            cout << "作者: aubuty" << endl;
            cout << "邮箱: <EMAIL>" << endl;
            cout << "平台: Orange Pi Zero 2W" << endl;
            cout << "传感器: HC-SR04, AHT20, BH1750" << endl;
            cout << "摄像头: USB Camera (OpenCV)" << endl;
            return 0;
        } else if (strcmp(argv[i], "-t") == 0) {
            cout << "🧪 测试模式暂未实现，请使用监控模式" << endl;
            return 0;
        }
    }

    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 创建目录和初始化日志
    if (create_directories() != 0 || init_logging() != 0) {
        cerr << "❌ 初始化失败" << endl;
        return 1;
    }

    cout << "\n🔄 启动多线程监控模式" << endl;
    cout << "========================" << endl;
    cout << "💡 按 Ctrl+C 退出程序" << endl;
    cout << "💡 使用 -h 参数查看帮助" << endl << endl;

    // 启动水位传感器线程
    cout << "🌊 启动水位传感器线程..." << endl;
    if (pthread_create(&water_sensor_thread, nullptr, water_sensor_thread_func, nullptr) != 0) {
        logger_error(system_logger, "创建水位传感器线程失败");
        return 1;
    }

    // 启动温湿度传感器线程
    cout << "🌡️ 启动温湿度传感器线程..." << endl;
    if (pthread_create(&temperature_sensor_thread, nullptr, temperature_sensor_thread_func, nullptr) != 0) {
        logger_error(system_logger, "创建温湿度传感器线程失败");
        running = 0;
        pthread_join(water_sensor_thread, nullptr);
        return 1;
    }

    // 启动光照传感器线程
    cout << "☀️ 启动光照传感器线程..." << endl;
    if (pthread_create(&light_sensor_thread, nullptr, light_sensor_thread_func, nullptr) != 0) {
        logger_error(system_logger, "创建光照传感器线程失败");
        running = 0;
        pthread_join(water_sensor_thread, nullptr);
        pthread_join(temperature_sensor_thread, nullptr);
        return 1;
    }

    // 启动摄像头线程
    cout << "📷 启动摄像头线程..." << endl;
    if (pthread_create(&camera_thread, nullptr, camera_thread_func, nullptr) != 0) {
        logger_error(system_logger, "创建摄像头线程失败");
        running = 0;
        pthread_join(water_sensor_thread, nullptr);
        pthread_join(temperature_sensor_thread, nullptr);
        pthread_join(light_sensor_thread, nullptr);
        return 1;
    }

    cout << "✅ 所有传感器线程启动完成" << endl << endl;
    logger_info(system_logger, "GreenLand 多线程监控系统启动完成");

    // 主监控循环 - 每30秒显示一次状态
    int status_count = 0;
    while (running) {
        sleep(30);
        if (!running) break;

        status_count++;
        cout << "\n📊 系统状态报告 #" << status_count << " (运行时间: "
             << status_count * 30 / 60 << "分钟)" << endl;
        cout << "==========================================" << endl;
        show_sensor_status();
        cout << endl;

        logger_info(system_logger, "系统状态检查 #%d", status_count);
    }

    cout << "\n🛑 正在停止所有线程..." << endl;

    // 等待所有线程结束
    pthread_join(water_sensor_thread, nullptr);
    cout << "✅ 水位传感器线程已停止" << endl;

    pthread_join(temperature_sensor_thread, nullptr);
    cout << "✅ 温湿度传感器线程已停止" << endl;

    pthread_join(light_sensor_thread, nullptr);
    cout << "✅ 光照传感器线程已停止" << endl;

    pthread_join(camera_thread, nullptr);
    cout << "✅ 摄像头线程已停止" << endl;

    // 清理日志系统
    if (system_logger) logger_destroy(system_logger);
    if (sensor_logger) logger_destroy(sensor_logger);

    cout << "\n👋 GreenLand 系统已退出" << endl;
    return 0;
}

// 标准main函数入口
int main(int argc, char* argv[]) {
    return GreenLand(argc, argv);
}
