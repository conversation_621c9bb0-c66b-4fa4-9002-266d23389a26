/**
 * @file main.cpp
 * @brief GreenLand 智能农业监控系统主程序入口
 * <AUTHOR>
 * @date 2024
 */

#include <iostream>
#include <cstring>
#include <signal.h>

extern "C" {
    #include "app/core/app_core.h"
}

using namespace std;

static app_core_t g_app_core = nullptr;

void signal_handler(int sig) {
    cout << "\n正在退出..." << endl;
    if (g_app_core) app_core_stop(g_app_core);
}

int main(int argc, char* argv[]) {
    const char* config_file = nullptr;
    
    // 简单参数解析
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "-h") == 0) {
            cout << "用法: " << argv[0] << " [-c config] [-h] [-v] [-i]" << endl;
            return 0;
        } else if (strcmp(argv[i], "-v") == 0) {
            app_core_print_version();
            return 0;
        } else if (strcmp(argv[i], "-i") == 0) {
            // 显示信息模式
            g_app_core = app_core_create();
            if (g_app_core && app_core_init(g_app_core, config_file) == 0) {
                app_core_print_info(g_app_core);
            }
            if (g_app_core) app_core_destroy(g_app_core);
            return 0;
        } else if (strcmp(argv[i], "-c") == 0 && i + 1 < argc) {
            config_file = argv[++i];
        }
    }
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 创建并运行应用程序
    g_app_core = app_core_create();
    if (!g_app_core) {
        cerr << "❌ 创建失败" << endl;
        return 1;
    }
    
    app_core_set_global_instance(g_app_core);
    
    if (app_core_init(g_app_core, config_file) != 0 ||
        app_core_start(g_app_core) != 0) {
        cerr << "❌ 启动失败" << endl;
        app_core_destroy(g_app_core);
        return 1;
    }
    
    // 运行主循环
    int result = app_core_run(g_app_core);
    
    // 清理
    app_core_destroy(g_app_core);
    return result;
}
