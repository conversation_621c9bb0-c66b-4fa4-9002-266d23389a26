/**
 * @file bh1750_unit_test.c
 * @brief BH1750光照传感器模块全面单元测试
 * <AUTHOR> Team
 * @date 2024
 * 
 * 本文件包含BH1750传感器模块的全面单元测试，包括：
 * - 基础功能测试
 * - 多模式测试
 * - 边界条件测试
 * - 错误处理测试
 * - 性能测试
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <time.h>
#include <math.h>
#include "test_framework.h"
#include "bh1750.h"

// 测试配置
#define TEST_ITERATIONS 50
#define PERFORMANCE_TEST_COUNT 500
#define LUX_TOLERANCE 10.0  // lx

// 全局变量
static volatile int test_running = 1;

// 信号处理函数
void signal_handler(int sig) {
    test_running = 0;
    printf("\n收到信号 %d，正在停止测试...\n", sig);
}

/**
 * @brief 获取模式名称
 */
const char* get_mode_name(bh1750_mode_t mode) {
    switch (mode) {
        case BH1750_MODE_CONT_H_RES: return "连续高分辨率";
        case BH1750_MODE_CONT_H_RES2: return "连续高分辨率2";
        case BH1750_MODE_CONT_L_RES: return "连续低分辨率";
        case BH1750_MODE_ONE_H_RES: return "单次高分辨率";
        case BH1750_MODE_ONE_H_RES2: return "单次高分辨率2";
        case BH1750_MODE_ONE_L_RES: return "单次低分辨率";
        default: return "未知模式";
    }
}

/**
 * @brief 测试BH1750初始化功能
 */
void test_bh1750_initialization(void) {
    TEST_CASE_START("BH1750初始化测试");
    
    // 测试默认地址初始化
    int result = bh1750_init();
    TEST_ASSERT(result == BH1750_OK, "BH1750默认地址初始化应该成功");
    bh1750_deinit();
    
    // 测试指定地址初始化
    result = bh1750_init_with_addr(BH1750_I2C_ADDR);
    TEST_ASSERT(result == BH1750_OK, "BH1750指定地址初始化应该成功");
    
    // 测试重复初始化
    result = bh1750_init_with_addr(BH1750_I2C_ADDR);
    TEST_ASSERT(result == BH1750_OK, "重复初始化应该成功");
    
    bh1750_deinit();
    
    TEST_CASE_END();
}

/**
 * @brief 测试BH1750模式设置
 */
void test_bh1750_mode_setting(void) {
    TEST_CASE_START("BH1750模式设置测试");
    
    int result = bh1750_init();
    if (result != BH1750_OK) {
        TEST_SKIP("传感器初始化失败，跳过模式设置测试");
        return;
    }
    
    // 测试所有有效模式
    bh1750_mode_t modes[] = {
        BH1750_MODE_CONT_H_RES,
        BH1750_MODE_CONT_H_RES2,
        BH1750_MODE_CONT_L_RES,
        BH1750_MODE_ONE_H_RES,
        BH1750_MODE_ONE_H_RES2,
        BH1750_MODE_ONE_L_RES
    };
    
    int num_modes = sizeof(modes) / sizeof(modes[0]);
    
    for (int i = 0; i < num_modes; i++) {
        result = bh1750_set_mode(modes[i]);
        TEST_ASSERT(result == BH1750_OK, "设置有效模式应该成功");
        printf("成功设置模式: %s\n", get_mode_name(modes[i]));
    }
    
    // 测试无效模式
    result = bh1750_set_mode((bh1750_mode_t)99);
    TEST_ASSERT(result != BH1750_OK, "设置无效模式应该失败");
    
    bh1750_deinit();
    
    TEST_CASE_END();
}

/**
 * @brief 测试BH1750数据读取功能
 */
void test_bh1750_data_reading(void) {
    TEST_CASE_START("BH1750数据读取测试");
    
    int result = bh1750_init();
    if (result != BH1750_OK) {
        TEST_SKIP("传感器初始化失败，跳过数据读取测试");
        return;
    }
    
    // 设置为连续高分辨率模式
    result = bh1750_set_mode(BH1750_MODE_CONT_H_RES);
    TEST_ASSERT(result == BH1750_OK, "设置测量模式应该成功");
    
    sleep(1); // 等待测量稳定
    
    // 测试完整数据读取
    bh1750_data_t data;
    result = bh1750_read_data(&data);
    TEST_ASSERT(result == BH1750_OK, "数据读取应该成功");
    
    // 验证数据合理性
    TEST_ASSERT(data.lux >= 0.0 && data.lux <= 65535.0, 
                "光照强度值应该在合理范围内(0-65535 lx)");
    TEST_ASSERT(data.raw_data <= 65535, "原始数据应该在16位范围内");
    
    printf("读取到的数据: 光照强度=%.2f lx, 原始数据=%d, 模式=%s\n", 
           data.lux, data.raw_data, get_mode_name(data.mode));
    
    // 测试简化接口
    float lux;
    result = bh1750_read_lux(&lux);
    TEST_ASSERT(result == BH1750_OK, "简化接口读取应该成功");
    TEST_ASSERT(lux >= 0.0 && lux <= 65535.0, "简化接口读取的光照值应该合理");
    
    // 测试空指针
    result = bh1750_read_data(NULL);
    TEST_ASSERT(result != BH1750_OK, "传入空指针应该返回错误");
    
    result = bh1750_read_lux(NULL);
    TEST_ASSERT(result != BH1750_OK, "传入空指针应该返回错误");
    
    bh1750_deinit();
    
    TEST_CASE_END();
}

/**
 * @brief 测试BH1750所有模式的数据读取
 */
void test_bh1750_all_modes_reading(void) {
    TEST_CASE_START("BH1750所有模式数据读取测试");
    
    int result = bh1750_init();
    if (result != BH1750_OK) {
        TEST_SKIP("传感器初始化失败，跳过多模式测试");
        return;
    }
    
    bh1750_mode_t modes[] = {
        BH1750_MODE_CONT_H_RES,
        BH1750_MODE_CONT_H_RES2,
        BH1750_MODE_CONT_L_RES,
        BH1750_MODE_ONE_H_RES,
        BH1750_MODE_ONE_H_RES2,
        BH1750_MODE_ONE_L_RES
    };
    
    int num_modes = sizeof(modes) / sizeof(modes[0]);
    
    for (int i = 0; i < num_modes && test_running; i++) {
        printf("测试模式: %s\n", get_mode_name(modes[i]));
        
        // 设置模式
        result = bh1750_set_mode(modes[i]);
        TEST_ASSERT(result == BH1750_OK, "设置模式应该成功");
        
        // 等待测量稳定
        sleep(1);
        
        // 读取数据
        bh1750_data_t data;
        result = bh1750_read_data(&data);
        TEST_ASSERT(result == BH1750_OK, "数据读取应该成功");
        
        printf("  光照强度: %.2f lx (原始: %d)\n", data.lux, data.raw_data);
        
        // 验证模式是否正确设置
        TEST_ASSERT(data.mode == modes[i], "返回的模式应该与设置的模式一致");
    }
    
    bh1750_deinit();
    
    TEST_CASE_END();
}

/**
 * @brief 测试BH1750电源管理
 */
void test_bh1750_power_management(void) {
    TEST_CASE_START("BH1750电源管理测试");
    
    int result = bh1750_init();
    if (result != BH1750_OK) {
        TEST_SKIP("传感器初始化失败，跳过电源管理测试");
        return;
    }
    
    // 测试断电
    result = bh1750_power_down();
    TEST_ASSERT(result == BH1750_OK, "断电操作应该成功");
    
    // 测试上电
    result = bh1750_power_on();
    TEST_ASSERT(result == BH1750_OK, "上电操作应该成功");
    
    // 测试重置
    result = bh1750_reset();
    TEST_ASSERT(result == BH1750_OK, "重置操作应该成功");
    
    bh1750_deinit();
    
    TEST_CASE_END();
}

/**
 * @brief 测试BH1750性能
 */
void test_bh1750_performance(void) {
    TEST_CASE_START("BH1750性能测试");
    
    int result = bh1750_init();
    if (result != BH1750_OK) {
        TEST_SKIP("传感器初始化失败，跳过性能测试");
        return;
    }
    
    // 设置为连续高分辨率模式
    bh1750_set_mode(BH1750_MODE_CONT_H_RES);
    sleep(1);
    
    printf("执行 %d 次数据读取性能测试...\n", PERFORMANCE_TEST_COUNT);
    
    performance_test_start();
    
    int success_count = 0;
    for (int i = 0; i < PERFORMANCE_TEST_COUNT && test_running; i++) {
        float lux;
        if (bh1750_read_lux(&lux) == BH1750_OK) {
            success_count++;
        }
        
        if (i % 50 == 0) {
            printf("已完成 %d/%d 次测试\n", i, PERFORMANCE_TEST_COUNT);
        }
    }
    
    double elapsed_ms = performance_test_end();
    double avg_time_ms = elapsed_ms / PERFORMANCE_TEST_COUNT;
    
    printf("性能测试结果:\n");
    printf("  总耗时: %.2f ms\n", elapsed_ms);
    printf("  平均每次: %.2f ms\n", avg_time_ms);
    printf("  成功率: %.1f%% (%d/%d)\n", 
           (double)success_count / PERFORMANCE_TEST_COUNT * 100.0,
           success_count, PERFORMANCE_TEST_COUNT);
    
    TEST_ASSERT(avg_time_ms < 50.0, "平均响应时间应该小于50ms");
    TEST_ASSERT(success_count > PERFORMANCE_TEST_COUNT * 0.95, "成功率应该大于95%");
    
    bh1750_deinit();
    
    TEST_CASE_END();
}

/**
 * @brief 主测试函数
 */
int main(void) {
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 初始化测试框架
    test_framework_init();
    
    TEST_SUITE_START("BH1750传感器单元测试");
    
    // 执行各项测试
    test_bh1750_initialization();
    test_bh1750_mode_setting();
    test_bh1750_data_reading();
    test_bh1750_all_modes_reading();
    test_bh1750_power_management();
    test_bh1750_performance();
    
    TEST_SUITE_END();
    
    // 输出测试总结
    test_framework_summary();
    
    return test_framework_get_exit_code();
}
