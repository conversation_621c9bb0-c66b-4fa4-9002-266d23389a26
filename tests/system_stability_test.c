/**
 * @file system_stability_test.c
 * @brief 系统稳定性测试程序
 * <AUTHOR>
 * @date 2024
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <time.h>
#include <string.h>
#include <sys/time.h>

// 包含所有模块
#include "../src/modules/logger/include/logger.h"
#include "../src/modules/hcsr04/include/hcsr04.h"

#ifdef ENABLE_CAMERA
#include "../src/modules/camera/include/camera.h"
#endif

static volatile int running = 1;
static logger_t test_logger = NULL;

void signal_handler(int sig) {
    printf("\n收到信号 %d，停止测试...\n", sig);
    running = 0;
}

// 获取当前时间戳（毫秒）
long long get_timestamp_ms() {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (long long)tv.tv_sec * 1000 + tv.tv_usec / 1000;
}

// 测试日志系统稳定性
int test_logger_stability() {
    printf("🔍 测试日志系统稳定性...\n");
    
    log_config_t config;
    logger_get_default_config(&config, "stability_test");
    config.enable_console = false;  // 减少输出
    
    test_logger = logger_create(&config);
    if (!test_logger) {
        printf("❌ 日志系统创建失败\n");
        return -1;
    }
    
    // 快速写入大量日志
    for (int i = 0; i < 1000 && running; i++) {
        logger_info(test_logger, "稳定性测试日志 %d", i);
        if (i % 100 == 0) {
            printf("  日志测试进度: %d/1000\n", i);
        }
    }
    
    printf("✅ 日志系统稳定性测试通过\n");
    return 0;
}

// 测试HC-SR04传感器稳定性
int test_hcsr04_stability() {
    printf("🔍 测试HC-SR04传感器稳定性...\n");
    
    hcsr04_config_t config;
    hcsr04_get_default_config(&config);
    
    hcsr04_t sensor = hcsr04_create(&config);
    if (!sensor) {
        printf("❌ HC-SR04传感器创建失败\n");
        return -1;
    }
    
    if (hcsr04_init(sensor) != HCSR04_OK) {
        printf("⚠️ HC-SR04传感器初始化失败（可能无硬件）\n");
        hcsr04_destroy(sensor);
        return 0;  // 不算错误，可能没有硬件
    }
    
    int success_count = 0;
    int total_count = 100;
    
    for (int i = 0; i < total_count && running; i++) {
        float distance = hcsr04_read_distance(sensor);
        if (distance > 0) {
            success_count++;
        }
        
        if (i % 20 == 0) {
            printf("  传感器测试进度: %d/%d (成功率: %.1f%%)\n", 
                   i, total_count, (float)success_count / (i + 1) * 100);
        }
        
        usleep(50000);  // 50ms间隔
    }
    
    float success_rate = (float)success_count / total_count * 100;
    printf("  最终成功率: %.1f%% (%d/%d)\n", success_rate, success_count, total_count);
    
    hcsr04_destroy(sensor);
    
    if (success_rate >= 80.0) {
        printf("✅ HC-SR04传感器稳定性测试通过\n");
        return 0;
    } else {
        printf("⚠️ HC-SR04传感器稳定性较低\n");
        return 0;  // 不算致命错误
    }
}

#ifdef ENABLE_CAMERA
// 测试摄像头稳定性
int test_camera_stability() {
    printf("🔍 测试摄像头稳定性...\n");
    
    // 扫描设备
    int devices[4];
    int device_count = camera_scan_devices(devices, 4);
    
    if (device_count == 0) {
        printf("⚠️ 未找到摄像头设备\n");
        return 0;  // 不算错误
    }
    
    camera_config_t config;
    camera_get_default_config(&config);
    config.device_id = devices[0];
    
    camera_t camera = camera_create(&config);
    if (!camera) {
        printf("❌ 摄像头创建失败\n");
        return -1;
    }
    
    if (camera_init(camera) != CAMERA_OK) {
        printf("⚠️ 摄像头初始化失败\n");
        camera_destroy(camera);
        return 0;
    }
    
    int success_count = 0;
    int total_count = 10;
    
    for (int i = 0; i < total_count && running; i++) {
        char filename[64];
        snprintf(filename, sizeof(filename), "stability_test_%d", i);
        
        if (camera_quick_photo(camera, filename) == CAMERA_OK) {
            success_count++;
        }
        
        printf("  摄像头测试进度: %d/%d\n", i + 1, total_count);
        sleep(1);
    }
    
    float success_rate = (float)success_count / total_count * 100;
    printf("  拍照成功率: %.1f%% (%d/%d)\n", success_rate, success_count, total_count);
    
    camera_destroy(camera);
    
    if (success_rate >= 80.0) {
        printf("✅ 摄像头稳定性测试通过\n");
        return 0;
    } else {
        printf("⚠️ 摄像头稳定性较低\n");
        return 0;
    }
}
#endif

// 内存泄漏测试
int test_memory_stability() {
    printf("🔍 测试内存稳定性...\n");
    
    for (int i = 0; i < 100 && running; i++) {
        // 创建和销毁日志记录器
        log_config_t config;
        logger_get_default_config(&config, "memory_test");
        config.enable_console = false;
        config.enable_file = false;
        
        logger_t logger = logger_create(&config);
        if (logger) {
            logger_info(logger, "内存测试 %d", i);
            logger_destroy(logger);
        }
        
        // 创建和销毁传感器
        hcsr04_config_t hc_config;
        hcsr04_get_default_config(&hc_config);
        
        hcsr04_t sensor = hcsr04_create(&hc_config);
        if (sensor) {
            hcsr04_destroy(sensor);
        }
        
        if (i % 20 == 0) {
            printf("  内存测试进度: %d/100\n", i);
        }
    }
    
    printf("✅ 内存稳定性测试通过\n");
    return 0;
}

int main() {
    printf("🚀 GreenLand 系统稳定性测试\n");
    printf("==============================\n\n");
    
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    long long start_time = get_timestamp_ms();
    int failed_tests = 0;
    
    // 运行各项稳定性测试
    if (test_logger_stability() != 0) failed_tests++;
    if (running && test_hcsr04_stability() != 0) failed_tests++;
    
#ifdef ENABLE_CAMERA
    if (running && test_camera_stability() != 0) failed_tests++;
#endif
    
    if (running && test_memory_stability() != 0) failed_tests++;
    
    long long end_time = get_timestamp_ms();
    long long duration = end_time - start_time;
    
    printf("\n📊 测试结果总结:\n");
    printf("  测试时长: %lld 毫秒\n", duration);
    printf("  失败测试: %d\n", failed_tests);
    
    if (failed_tests == 0) {
        printf("✅ 所有稳定性测试通过！系统稳定可靠。\n");
    } else {
        printf("⚠️ 有 %d 项测试失败，请检查系统配置。\n", failed_tests);
    }
    
    // 清理
    if (test_logger) {
        logger_destroy(test_logger);
    }
    
    printf("\n👋 稳定性测试完成\n");
    return failed_tests;
}
