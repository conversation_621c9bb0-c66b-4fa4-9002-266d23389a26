/**
 * @file integration_test.c
 * @brief GreenLand系统集成测试
 * <AUTHOR> Team
 * @date 2024
 * 
 * 本文件包含GreenLand环境监控系统的集成测试，包括：
 * - 多传感器协同工作测试
 * - 系统稳定性测试
 * - 数据一致性测试
 * - 多线程环境测试
 * - 资源管理测试
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <time.h>
#include <pthread.h>
#include <math.h>
#include "test_framework.h"
#include "aht20.h"
#include "bh1750.h"

// 测试配置
#define INTEGRATION_TEST_DURATION 60  // 秒
#define THREAD_TEST_COUNT 100
#define DATA_CONSISTENCY_SAMPLES 20

// 全局变量
static volatile int test_running = 1;
static pthread_mutex_t data_mutex = PTHREAD_MUTEX_INITIALIZER;

// 传感器数据结构
typedef struct {
    aht20_data_t aht20_data;
    bh1750_data_t bh1750_data;
    time_t timestamp;
    int valid;
} sensor_reading_t;

// 线程测试数据
typedef struct {
    int thread_id;
    int success_count;
    int error_count;
    double total_time;
} thread_test_data_t;

// 信号处理函数
void signal_handler(int sig) {
    test_running = 0;
    printf("\n收到信号 %d，正在停止测试...\n", sig);
}

/**
 * @brief 读取所有传感器数据
 */
int read_all_sensors(sensor_reading_t* reading) {
    if (!reading) return -1;
    
    reading->valid = 0;
    reading->timestamp = time(NULL);
    
    // 读取AHT20数据
    int aht20_result = aht20_read_data(&reading->aht20_data);
    
    // 读取BH1750数据
    int bh1750_result = bh1750_read_data(&reading->bh1750_data);
    
    if (aht20_result == AHT20_OK && bh1750_result == BH1750_OK) {
        reading->valid = 1;
        return 0;
    }
    
    return -1;
}

/**
 * @brief 测试传感器初始化和清理
 */
void test_sensor_initialization_cleanup(void) {
    TEST_CASE_START("传感器初始化和清理测试");
    
    // 测试单独初始化
    int aht20_result = aht20_init();
    int bh1750_result = bh1750_init();
    
    TEST_ASSERT(aht20_result == AHT20_OK, "AHT20初始化应该成功");
    TEST_ASSERT(bh1750_result == BH1750_OK, "BH1750初始化应该成功");
    
    if (aht20_result == AHT20_OK && bh1750_result == BH1750_OK) {
        printf("✅ 所有传感器初始化成功\n");
        
        // 测试同时读取
        sensor_reading_t reading;
        int result = read_all_sensors(&reading);
        TEST_ASSERT(result == 0, "同时读取所有传感器应该成功");
        
        if (result == 0) {
            printf("环境数据: 温度=%.2f°C, 湿度=%.2f%%, 光照=%.2f lx\n",
                   reading.aht20_data.temperature,
                   reading.aht20_data.humidity,
                   reading.bh1750_data.lux);
        }
    }
    
    // 清理资源
    aht20_deinit();
    bh1750_deinit();
    
    TEST_CASE_END();
}

/**
 * @brief 测试数据一致性
 */
void test_data_consistency(void) {
    TEST_CASE_START("数据一致性测试");
    
    int aht20_result = aht20_init();
    int bh1750_result = bh1750_init();
    
    if (aht20_result != AHT20_OK || bh1750_result != BH1750_OK) {
        TEST_SKIP("传感器初始化失败，跳过数据一致性测试");
        return;
    }
    
    printf("收集 %d 个样本进行一致性分析...\n", DATA_CONSISTENCY_SAMPLES);
    
    sensor_reading_t readings[DATA_CONSISTENCY_SAMPLES];
    int valid_samples = 0;
    
    for (int i = 0; i < DATA_CONSISTENCY_SAMPLES && test_running; i++) {
        if (read_all_sensors(&readings[valid_samples]) == 0) {
            printf("样本 %d: T=%.2f°C, H=%.2f%%, L=%.2f lx\n",
                   valid_samples + 1,
                   readings[valid_samples].aht20_data.temperature,
                   readings[valid_samples].aht20_data.humidity,
                   readings[valid_samples].bh1750_data.lux);
            valid_samples++;
        }
        sleep(1);
    }
    
    TEST_ASSERT(valid_samples >= DATA_CONSISTENCY_SAMPLES * 0.8, 
                "至少80%的样本应该有效");
    
    if (valid_samples >= 2) {
        // 分析数据变化
        double temp_variance = 0.0, hum_variance = 0.0, lux_variance = 0.0;
        double temp_mean = 0.0, hum_mean = 0.0, lux_mean = 0.0;
        
        // 计算均值
        for (int i = 0; i < valid_samples; i++) {
            temp_mean += readings[i].aht20_data.temperature;
            hum_mean += readings[i].aht20_data.humidity;
            lux_mean += readings[i].bh1750_data.lux;
        }
        temp_mean /= valid_samples;
        hum_mean /= valid_samples;
        lux_mean /= valid_samples;
        
        // 计算方差
        for (int i = 0; i < valid_samples; i++) {
            double temp_diff = readings[i].aht20_data.temperature - temp_mean;
            double hum_diff = readings[i].aht20_data.humidity - hum_mean;
            double lux_diff = readings[i].bh1750_data.lux - lux_mean;
            
            temp_variance += temp_diff * temp_diff;
            hum_variance += hum_diff * hum_diff;
            lux_variance += lux_diff * lux_diff;
        }
        temp_variance /= valid_samples;
        hum_variance /= valid_samples;
        lux_variance /= valid_samples;
        
        printf("数据统计:\n");
        printf("  温度: 均值=%.2f°C, 方差=%.4f\n", temp_mean, temp_variance);
        printf("  湿度: 均值=%.2f%%, 方差=%.4f\n", hum_mean, hum_variance);
        printf("  光照: 均值=%.2f lx, 方差=%.4f\n", lux_mean, lux_variance);
        
        // 数据应该相对稳定
        TEST_ASSERT(temp_variance < 4.0, "温度方差应该小于4.0");
        TEST_ASSERT(hum_variance < 16.0, "湿度方差应该小于16.0");
    }
    
    aht20_deinit();
    bh1750_deinit();
    
    TEST_CASE_END();
}

/**
 * @brief 线程测试函数
 */
void* thread_test_function(void* arg) {
    thread_test_data_t* data = (thread_test_data_t*)arg;
    
    struct timeval start_time, end_time;
    gettimeofday(&start_time, NULL);
    
    for (int i = 0; i < THREAD_TEST_COUNT && test_running; i++) {
        sensor_reading_t reading;
        
        pthread_mutex_lock(&data_mutex);
        int result = read_all_sensors(&reading);
        pthread_mutex_unlock(&data_mutex);
        
        if (result == 0) {
            data->success_count++;
        } else {
            data->error_count++;
        }
        
        usleep(10000); // 10ms
    }
    
    gettimeofday(&end_time, NULL);
    data->total_time = (end_time.tv_sec - start_time.tv_sec) +
                       (end_time.tv_usec - start_time.tv_usec) / 1000000.0;
    
    return NULL;
}

/**
 * @brief 测试多线程环境
 */
void test_multithreading(void) {
    TEST_CASE_START("多线程环境测试");
    
    int aht20_result = aht20_init();
    int bh1750_result = bh1750_init();
    
    if (aht20_result != AHT20_OK || bh1750_result != BH1750_OK) {
        TEST_SKIP("传感器初始化失败，跳过多线程测试");
        return;
    }
    
    const int num_threads = 4;
    pthread_t threads[num_threads];
    thread_test_data_t thread_data[num_threads];
    
    printf("启动 %d 个线程进行并发测试...\n", num_threads);
    
    // 创建线程
    for (int i = 0; i < num_threads; i++) {
        thread_data[i].thread_id = i;
        thread_data[i].success_count = 0;
        thread_data[i].error_count = 0;
        thread_data[i].total_time = 0.0;
        
        int result = pthread_create(&threads[i], NULL, thread_test_function, &thread_data[i]);
        TEST_ASSERT(result == 0, "线程创建应该成功");
    }
    
    // 等待线程完成
    for (int i = 0; i < num_threads; i++) {
        pthread_join(threads[i], NULL);
    }
    
    // 统计结果
    int total_success = 0, total_errors = 0;
    double total_time = 0.0;
    
    for (int i = 0; i < num_threads; i++) {
        total_success += thread_data[i].success_count;
        total_errors += thread_data[i].error_count;
        total_time += thread_data[i].total_time;
        
        printf("线程 %d: 成功=%d, 错误=%d, 耗时=%.2fs\n",
               i, thread_data[i].success_count, thread_data[i].error_count,
               thread_data[i].total_time);
    }
    
    double success_rate = (double)total_success / (total_success + total_errors) * 100.0;
    printf("多线程测试总结: 成功率=%.1f%%, 平均耗时=%.2fs\n",
           success_rate, total_time / num_threads);
    
    TEST_ASSERT(success_rate > 90.0, "多线程环境下成功率应该大于90%");
    
    aht20_deinit();
    bh1750_deinit();
    
    TEST_CASE_END();
}

/**
 * @brief 测试系统稳定性
 */
void test_system_stability(void) {
    TEST_CASE_START("系统稳定性测试");
    
    int aht20_result = aht20_init();
    int bh1750_result = bh1750_init();
    
    if (aht20_result != AHT20_OK || bh1750_result != BH1750_OK) {
        TEST_SKIP("传感器初始化失败，跳过稳定性测试");
        return;
    }
    
    printf("运行 30 秒稳定性测试...\n");
    
    time_t start_time = time(NULL);
    int total_readings = 0;
    int successful_readings = 0;
    
    while (test_running && (time(NULL) - start_time) < 30) {
        sensor_reading_t reading;
        total_readings++;
        
        if (read_all_sensors(&reading) == 0) {
            successful_readings++;
            
            if (total_readings % 10 == 0) {
                printf("第 %d 次读取: T=%.2f°C, H=%.2f%%, L=%.2f lx\n",
                       total_readings,
                       reading.aht20_data.temperature,
                       reading.aht20_data.humidity,
                       reading.bh1750_data.lux);
            }
        }
        
        sleep(1);
    }
    
    double stability_rate = (double)successful_readings / total_readings * 100.0;
    printf("稳定性测试结果: %d/%d 次成功 (%.1f%%)\n",
           successful_readings, total_readings, stability_rate);
    
    TEST_ASSERT(stability_rate > 95.0, "系统稳定性应该大于95%");
    
    aht20_deinit();
    bh1750_deinit();
    
    TEST_CASE_END();
}

/**
 * @brief 主测试函数
 */
int main(void) {
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 初始化测试框架
    test_framework_init();
    
    TEST_SUITE_START("GreenLand系统集成测试");
    
    // 执行各项测试
    test_sensor_initialization_cleanup();
    test_data_consistency();
    test_multithreading();
    test_system_stability();
    
    TEST_SUITE_END();
    
    // 输出测试总结
    test_framework_summary();
    
    return test_framework_get_exit_code();
}
