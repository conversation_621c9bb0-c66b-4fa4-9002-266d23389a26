#include "test_framework.h"
#include <sys/time.h>
#include <unistd.h>

// 全局测试统计
test_stats_t g_test_stats = {0};

// 性能测试时间记录
static struct timeval perf_start_time;

// 简单的内存跟踪
typedef struct memory_block {
    void* ptr;
    size_t size;
    const char* file;
    int line;
    struct memory_block* next;
} memory_block_t;

static memory_block_t* memory_blocks = NULL;
static int total_allocations = 0;
static size_t total_allocated = 0;

// 初始化测试框架
void test_framework_init(void) {
    memset(&g_test_stats, 0, sizeof(g_test_stats));
    memory_blocks = NULL;
    total_allocations = 0;
    total_allocated = 0;
    
    printf(COLOR_PURPLE "🧪 GreenLand 测试框架初始化" COLOR_RESET "\n");
    printf(COLOR_PURPLE "================================" COLOR_RESET "\n");
}

// 输出测试总结
void test_framework_summary(void) {
    printf(COLOR_PURPLE "\n================================" COLOR_RESET "\n");
    printf(COLOR_PURPLE "🧪 测试总结报告" COLOR_RESET "\n");
    printf(COLOR_PURPLE "================================" COLOR_RESET "\n");
    
    printf("总测试数: %d\n", g_test_stats.total_tests);
    printf(COLOR_GREEN "通过: %d" COLOR_RESET "\n", g_test_stats.passed_tests);
    printf(COLOR_RED "失败: %d" COLOR_RESET "\n", g_test_stats.failed_tests);
    printf(COLOR_YELLOW "跳过: %d" COLOR_RESET "\n", g_test_stats.skipped_tests);
    printf("总耗时: %.3f 秒\n", g_test_stats.total_time);
    
    if (g_test_stats.total_tests > 0) {
        double pass_rate = (double)g_test_stats.passed_tests / g_test_stats.total_tests * 100.0;
        printf("通过率: %.1f%%\n", pass_rate);
    }
    
    if (g_test_stats.failed_tests == 0) {
        printf(COLOR_GREEN "\n🎉 所有测试通过！" COLOR_RESET "\n");
    } else {
        printf(COLOR_RED "\n❌ 有 %d 个测试失败" COLOR_RESET "\n", g_test_stats.failed_tests);
    }
    
    // 内存报告
    test_memory_report();
}

// 获取退出码
int test_framework_get_exit_code(void) {
    return (g_test_stats.failed_tests > 0) ? 1 : 0;
}

// 重置统计
void test_framework_reset_stats(void) {
    memset(&g_test_stats, 0, sizeof(g_test_stats));
}

// 获取当前时间（毫秒）
double get_current_time_ms(void) {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return tv.tv_sec * 1000.0 + tv.tv_usec / 1000.0;
}

// 开始性能测试
void performance_test_start(void) {
    gettimeofday(&perf_start_time, NULL);
}

// 结束性能测试并返回耗时（毫秒）
double performance_test_end(void) {
    struct timeval end_time;
    gettimeofday(&end_time, NULL);
    
    return (end_time.tv_sec - perf_start_time.tv_sec) * 1000.0 +
           (end_time.tv_usec - perf_start_time.tv_usec) / 1000.0;
}

// 测试用内存分配
void* test_malloc(size_t size, const char* file, int line) {
    void* ptr = malloc(size);
    if (ptr) {
        memory_block_t* block = malloc(sizeof(memory_block_t));
        if (block) {
            block->ptr = ptr;
            block->size = size;
            block->file = file;
            block->line = line;
            block->next = memory_blocks;
            memory_blocks = block;
            total_allocations++;
            total_allocated += size;
        }
    }
    return ptr;
}

// 测试用内存释放
void test_free(void* ptr, const char* file, int line) {
    if (!ptr) return;
    
    memory_block_t** current = &memory_blocks;
    while (*current) {
        if ((*current)->ptr == ptr) {
            memory_block_t* to_remove = *current;
            *current = (*current)->next;
            total_allocated -= to_remove->size;
            free(to_remove);
            break;
        }
        current = &(*current)->next;
    }
    
    free(ptr);
}

// 内存报告
void test_memory_report(void) {
    printf(COLOR_PURPLE "\n--- 内存使用报告 ---" COLOR_RESET "\n");
    printf("总分配次数: %d\n", total_allocations);
    printf("当前已分配: %zu 字节\n", total_allocated);
    
    if (memory_blocks) {
        printf(COLOR_YELLOW "⚠️  检测到内存泄漏:" COLOR_RESET "\n");
        memory_block_t* current = memory_blocks;
        int leak_count = 0;
        while (current) {
            printf("  - %zu 字节 在 %s:%d\n", current->size, current->file, current->line);
            current = current->next;
            leak_count++;
        }
        printf(COLOR_YELLOW "总泄漏: %d 个块" COLOR_RESET "\n", leak_count);
    } else {
        printf(COLOR_GREEN "✅ 无内存泄漏检测到" COLOR_RESET "\n");
    }
}
