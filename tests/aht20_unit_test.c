/**
 * @file aht20_unit_test.c
 * @brief AHT20传感器模块全面单元测试
 * <AUTHOR> Team
 * @date 2024
 * 
 * 本文件包含AHT20传感器模块的全面单元测试，包括：
 * - 基础功能测试
 * - 边界条件测试
 * - 错误处理测试
 * - 性能测试
 * - 稳定性测试
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <time.h>
#include <math.h>
#include "test_framework.h"
#include "aht20.h"

// 测试配置
#define TEST_ITERATIONS 100
#define PERFORMANCE_TEST_COUNT 1000
#define STABILITY_TEST_DURATION 30  // 秒
#define TEMPERATURE_TOLERANCE 0.5   // °C
#define HUMIDITY_TOLERANCE 2.0      // %

// 全局变量
static volatile int test_running = 1;

// 信号处理函数
void signal_handler(int sig) {
    test_running = 0;
    printf("\n收到信号 %d，正在停止测试...\n", sig);
}

/**
 * @brief 测试AHT20初始化功能
 */
void test_aht20_initialization(void) {
    TEST_CASE_START("AHT20初始化测试");
    
    // 测试正常初始化
    int result = aht20_init();
    TEST_ASSERT(result == AHT20_OK, "AHT20初始化应该成功");
    
    // 测试重复初始化
    result = aht20_init();
    TEST_ASSERT(result == AHT20_OK, "重复初始化应该成功");
    
    // 清理
    aht20_deinit();
    
    TEST_CASE_END();
}

/**
 * @brief 测试AHT20数据读取功能
 */
void test_aht20_data_reading(void) {
    TEST_CASE_START("AHT20数据读取测试");
    
    // 初始化传感器
    int result = aht20_init();
    if (result != AHT20_OK) {
        TEST_SKIP("传感器初始化失败，跳过数据读取测试");
        return;
    }
    
    // 测试正常数据读取
    aht20_data_t data;
    result = aht20_read_data(&data);
    TEST_ASSERT(result == AHT20_OK, "数据读取应该成功");
    
    // 验证数据合理性
    TEST_ASSERT(data.temperature >= -40.0 && data.temperature <= 85.0, 
                "温度值应该在合理范围内(-40°C到85°C)");
    TEST_ASSERT(data.humidity >= 0.0 && data.humidity <= 100.0, 
                "湿度值应该在合理范围内(0%到100%)");
    
    printf("读取到的数据: 温度=%.2f°C, 湿度=%.2f%%\n", data.temperature, data.humidity);
    
    // 测试空指针
    result = aht20_read_data(NULL);
    TEST_ASSERT(result != AHT20_OK, "传入空指针应该返回错误");
    
    aht20_deinit();
    
    TEST_CASE_END();
}

/**
 * @brief 测试AHT20状态检查功能
 */
void test_aht20_status_check(void) {
    TEST_CASE_START("AHT20状态检查测试");
    
    int result = aht20_init();
    if (result != AHT20_OK) {
        TEST_SKIP("传感器初始化失败，跳过状态检查测试");
        return;
    }
    
    // 测试状态读取
    uint8_t status;
    result = aht20_get_status(&status);
    TEST_ASSERT(result == AHT20_OK, "状态读取应该成功");
    
    // 测试校准状态检查
    result = aht20_is_calibrated();
    TEST_ASSERT(result >= 0, "校准状态检查应该返回有效值");
    
    // 测试忙碌状态检查
    result = aht20_is_busy();
    TEST_ASSERT(result >= 0, "忙碌状态检查应该返回有效值");
    
    aht20_deinit();
    
    TEST_CASE_END();
}

/**
 * @brief 测试AHT20错误处理
 */
void test_aht20_error_handling(void) {
    TEST_CASE_START("AHT20错误处理测试");
    
    // 测试未初始化时的操作
    aht20_data_t data;
    int result = aht20_read_data(&data);
    TEST_ASSERT(result != AHT20_OK, "未初始化时读取数据应该失败");
    
    result = aht20_is_busy();
    TEST_ASSERT(result < 0, "未初始化时检查忙碌状态应该返回错误");
    
    // 初始化后测试
    result = aht20_init();
    if (result == AHT20_OK) {
        // 测试软复位
        result = aht20_soft_reset();
        TEST_ASSERT(result == AHT20_OK, "软复位应该成功");
        
        // 测试触发测量
        result = aht20_trigger_measurement();
        TEST_ASSERT(result == AHT20_OK, "触发测量应该成功");
        
        aht20_deinit();
    } else {
        TEST_SKIP("传感器初始化失败，跳过部分错误处理测试");
    }
    
    TEST_CASE_END();
}

/**
 * @brief 测试AHT20性能
 */
void test_aht20_performance(void) {
    TEST_CASE_START("AHT20性能测试");
    
    int result = aht20_init();
    if (result != AHT20_OK) {
        TEST_SKIP("传感器初始化失败，跳过性能测试");
        return;
    }
    
    printf("执行 %d 次数据读取性能测试...\n", PERFORMANCE_TEST_COUNT);
    
    performance_test_start();
    
    int success_count = 0;
    for (int i = 0; i < PERFORMANCE_TEST_COUNT && test_running; i++) {
        aht20_data_t data;
        if (aht20_read_data(&data) == AHT20_OK) {
            success_count++;
        }
        
        if (i % 100 == 0) {
            printf("已完成 %d/%d 次测试\n", i, PERFORMANCE_TEST_COUNT);
        }
    }
    
    double elapsed_ms = performance_test_end();
    double avg_time_ms = elapsed_ms / PERFORMANCE_TEST_COUNT;
    
    printf("性能测试结果:\n");
    printf("  总耗时: %.2f ms\n", elapsed_ms);
    printf("  平均每次: %.2f ms\n", avg_time_ms);
    printf("  成功率: %.1f%% (%d/%d)\n", 
           (double)success_count / PERFORMANCE_TEST_COUNT * 100.0,
           success_count, PERFORMANCE_TEST_COUNT);
    
    TEST_ASSERT(avg_time_ms < 100.0, "平均响应时间应该小于100ms");
    TEST_ASSERT(success_count > PERFORMANCE_TEST_COUNT * 0.95, "成功率应该大于95%");
    
    aht20_deinit();
    
    TEST_CASE_END();
}

/**
 * @brief 测试AHT20数据一致性
 */
void test_aht20_data_consistency(void) {
    TEST_CASE_START("AHT20数据一致性测试");
    
    int result = aht20_init();
    if (result != AHT20_OK) {
        TEST_SKIP("传感器初始化失败，跳过一致性测试");
        return;
    }
    
    printf("执行连续读取测试，检查数据一致性...\n");
    
    aht20_data_t readings[10];
    int valid_readings = 0;
    
    for (int i = 0; i < 10; i++) {
        if (aht20_read_data(&readings[i]) == AHT20_OK) {
            valid_readings++;
            printf("第%d次: 温度=%.2f°C, 湿度=%.2f%%\n", 
                   i+1, readings[i].temperature, readings[i].humidity);
        }
        sleep(1);
    }
    
    TEST_ASSERT(valid_readings >= 8, "至少80%的读取应该成功");
    
    if (valid_readings >= 2) {
        // 检查数据变化是否合理
        double max_temp_diff = 0.0;
        double max_hum_diff = 0.0;
        
        for (int i = 1; i < valid_readings; i++) {
            double temp_diff = fabs(readings[i].temperature - readings[i-1].temperature);
            double hum_diff = fabs(readings[i].humidity - readings[i-1].humidity);
            
            if (temp_diff > max_temp_diff) max_temp_diff = temp_diff;
            if (hum_diff > max_hum_diff) max_hum_diff = hum_diff;
        }
        
        printf("最大温度变化: %.2f°C\n", max_temp_diff);
        printf("最大湿度变化: %.2f%%\n", max_hum_diff);
        
        // 在短时间内，环境变化应该不会太大
        TEST_ASSERT(max_temp_diff < 5.0, "短时间内温度变化应该小于5°C");
        TEST_ASSERT(max_hum_diff < 10.0, "短时间内湿度变化应该小于10%");
    }
    
    aht20_deinit();
    
    TEST_CASE_END();
}

/**
 * @brief 主测试函数
 */
int main(void) {
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 初始化测试框架
    test_framework_init();
    
    TEST_SUITE_START("AHT20传感器单元测试");
    
    // 执行各项测试
    test_aht20_initialization();
    test_aht20_data_reading();
    test_aht20_status_check();
    test_aht20_error_handling();
    test_aht20_performance();
    test_aht20_data_consistency();
    
    TEST_SUITE_END();
    
    // 输出测试总结
    test_framework_summary();
    
    return test_framework_get_exit_code();
}
