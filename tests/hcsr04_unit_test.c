/**
 * @file hcsr04_unit_test.c
 * @brief HC-SR04超声波传感器模块全面单元测试
 * <AUTHOR> Team
 * @date 2024
 * 
 * 本文件包含HC-SR04传感器模块的全面单元测试，包括：
 * - 基础功能测试
 * - 配置管理测试
 * - 边界条件测试
 * - 错误处理测试
 * - 性能测试
 * - 稳定性测试
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <time.h>
#include <math.h>
#include "test_framework.h"
#include "hcsr04.h"

// 测试配置
#define TEST_ITERATIONS 50
#define PERFORMANCE_TEST_COUNT 100
#define STABILITY_TEST_DURATION 30  // 秒
#define DISTANCE_TOLERANCE 2.0      // cm

// 全局变量
static volatile int test_running = 1;

// 信号处理函数
void signal_handler(int sig) {
    test_running = 0;
    printf("\n收到信号 %d，正在停止测试...\n", sig);
}

/**
 * @brief 测试HC-SR04初始化功能
 */
void test_hcsr04_initialization(void) {
    TEST_CASE_START("HC-SR04初始化测试");
    
    // 测试正常初始化
    int result = hcsr04_init();
    TEST_ASSERT(result == HCSR04_OK, "HC-SR04初始化应该成功");
    TEST_ASSERT(hcsr04_is_initialized(), "初始化后状态应该为已初始化");
    
    // 测试重复初始化
    result = hcsr04_init();
    TEST_ASSERT(result == HCSR04_OK, "重复初始化应该成功");
    
    // 测试自定义配置初始化
    hcsr04_config_t config;
    hcsr04_get_default_config(&config);
    config.tank_height_cm = 200.0f;
    config.sensor_offset_cm = 10.0f;
    
    result = hcsr04_init_with_config(&config);
    TEST_ASSERT(result == HCSR04_OK, "自定义配置初始化应该成功");
    TEST_ASSERT(hcsr04_get_tank_height() == 200.0f, "水箱高度应该设置正确");
    TEST_ASSERT(hcsr04_get_sensor_offset() == 10.0f, "传感器偏移应该设置正确");
    
    // 清理
    hcsr04_deinit();
    TEST_ASSERT(!hcsr04_is_initialized(), "清理后状态应该为未初始化");
    
    TEST_CASE_END();
}

/**
 * @brief 测试HC-SR04数据读取功能
 */
void test_hcsr04_data_reading(void) {
    TEST_CASE_START("HC-SR04数据读取测试");
    
    // 初始化传感器
    int result = hcsr04_init();
    if (result != HCSR04_OK) {
        TEST_SKIP("传感器初始化失败，跳过数据读取测试");
        return;
    }
    
    // 测试正常数据读取
    hcsr04_data_t data;
    result = hcsr04_read_data(&data);
    TEST_ASSERT(result == HCSR04_OK, "数据读取应该成功");
    
    if (result == HCSR04_OK && data.valid) {
        // 验证数据合理性
        TEST_ASSERT(data.distance_cm >= 2.0 && data.distance_cm <= 400.0, 
                    "距离值应该在合理范围内(2-400cm)");
        TEST_ASSERT(data.water_level_cm >= 0.0, "水位值应该非负");
        TEST_ASSERT(data.water_level_percent >= 0.0 && data.water_level_percent <= 100.0, 
                    "水位百分比应该在0-100%范围内");
        TEST_ASSERT(data.echo_time_us > 0, "回响时间应该大于0");
        
        printf("读取到的数据: 距离=%.2f cm, 水位=%.2f cm (%.1f%%)\n", 
               data.distance_cm, data.water_level_cm, data.water_level_percent);
    }
    
    // 测试空指针
    result = hcsr04_read_data(NULL);
    TEST_ASSERT(result != HCSR04_OK, "传入空指针应该返回错误");
    
    // 测试简化接口
    float distance, water_level;
    result = hcsr04_read_distance(&distance);
    if (result == HCSR04_OK) {
        TEST_ASSERT(distance >= 2.0 && distance <= 400.0, "距离值应该在合理范围内");
    }
    
    result = hcsr04_read_water_level(&water_level);
    if (result == HCSR04_OK) {
        TEST_ASSERT(water_level >= 0.0, "水位值应该非负");
    }
    
    // 测试空指针
    result = hcsr04_read_distance(NULL);
    TEST_ASSERT(result != HCSR04_OK, "传入空指针应该返回错误");
    
    result = hcsr04_read_water_level(NULL);
    TEST_ASSERT(result != HCSR04_OK, "传入空指针应该返回错误");
    
    hcsr04_deinit();
    
    TEST_CASE_END();
}

/**
 * @brief 测试HC-SR04配置管理
 */
void test_hcsr04_configuration(void) {
    TEST_CASE_START("HC-SR04配置管理测试");
    
    int result = hcsr04_init();
    if (result != HCSR04_OK) {
        TEST_SKIP("传感器初始化失败，跳过配置测试");
        return;
    }
    
    // 测试水箱高度设置
    float original_height = hcsr04_get_tank_height();
    
    result = hcsr04_set_tank_height(150.0f);
    TEST_ASSERT(result == HCSR04_OK, "设置有效水箱高度应该成功");
    TEST_ASSERT(hcsr04_get_tank_height() == 150.0f, "水箱高度应该设置正确");
    
    // 测试无效水箱高度
    result = hcsr04_set_tank_height(-10.0f);
    TEST_ASSERT(result != HCSR04_OK, "设置负数水箱高度应该失败");
    
    result = hcsr04_set_tank_height(0.0f);
    TEST_ASSERT(result != HCSR04_OK, "设置零水箱高度应该失败");
    
    result = hcsr04_set_tank_height(1500.0f);
    TEST_ASSERT(result != HCSR04_OK, "设置过大水箱高度应该失败");
    
    // 恢复原值
    hcsr04_set_tank_height(original_height);
    
    // 测试传感器偏移设置
    float original_offset = hcsr04_get_sensor_offset();
    
    result = hcsr04_set_sensor_offset(8.0f);
    TEST_ASSERT(result == HCSR04_OK, "设置有效传感器偏移应该成功");
    TEST_ASSERT(hcsr04_get_sensor_offset() == 8.0f, "传感器偏移应该设置正确");
    
    // 测试无效传感器偏移
    result = hcsr04_set_sensor_offset(-60.0f);
    TEST_ASSERT(result != HCSR04_OK, "设置过小传感器偏移应该失败");
    
    result = hcsr04_set_sensor_offset(60.0f);
    TEST_ASSERT(result != HCSR04_OK, "设置过大传感器偏移应该失败");
    
    // 恢复原值
    hcsr04_set_sensor_offset(original_offset);
    
    // 测试状态信息获取
    char status_info[512];
    result = hcsr04_get_status_info(status_info, sizeof(status_info));
    TEST_ASSERT(result == HCSR04_OK, "状态信息获取应该成功");
    
    result = hcsr04_get_status_info(NULL, 100);
    TEST_ASSERT(result != HCSR04_OK, "传入空指针应该返回错误");
    
    hcsr04_deinit();
    
    TEST_CASE_END();
}

/**
 * @brief 测试HC-SR04错误处理
 */
void test_hcsr04_error_handling(void) {
    TEST_CASE_START("HC-SR04错误处理测试");
    
    // 测试未初始化时的操作
    hcsr04_data_t data;
    int result = hcsr04_read_data(&data);
    TEST_ASSERT(result != HCSR04_OK, "未初始化时读取数据应该失败");
    
    float distance;
    result = hcsr04_read_distance(&distance);
    TEST_ASSERT(result != HCSR04_OK, "未初始化时读取距离应该失败");
    
    result = hcsr04_read_averaged(&data, 3);
    TEST_ASSERT(result != HCSR04_OK, "未初始化时高精度读取应该失败");
    
    // 测试错误码转换
    const char* error_str = hcsr04_error_to_string(HCSR04_OK);
    TEST_ASSERT(error_str != NULL, "错误码转换应该返回有效字符串");
    
    error_str = hcsr04_error_to_string(HCSR04_ERROR_TIMEOUT);
    TEST_ASSERT(error_str != NULL, "错误码转换应该返回有效字符串");
    
    // 测试无效参数
    hcsr04_config_t config;
    result = hcsr04_init_with_config(NULL);
    TEST_ASSERT(result != HCSR04_OK, "传入空配置应该失败");
    
    // 初始化后测试
    result = hcsr04_init();
    if (result == HCSR04_OK) {
        // 测试无效采样次数
        result = hcsr04_read_averaged(&data, 0);
        TEST_ASSERT(result != HCSR04_OK, "零采样次数应该失败");
        
        result = hcsr04_read_averaged(&data, 15);
        TEST_ASSERT(result != HCSR04_OK, "过多采样次数应该失败");
        
        hcsr04_deinit();
    } else {
        TEST_SKIP("传感器初始化失败，跳过部分错误处理测试");
    }
    
    TEST_CASE_END();
}

/**
 * @brief 测试HC-SR04性能
 */
void test_hcsr04_performance(void) {
    TEST_CASE_START("HC-SR04性能测试");
    
    int result = hcsr04_init();
    if (result != HCSR04_OK) {
        TEST_SKIP("传感器初始化失败，跳过性能测试");
        return;
    }
    
    printf("执行 %d 次数据读取性能测试...\n", PERFORMANCE_TEST_COUNT);
    
    performance_test_start();
    
    int success_count = 0;
    for (int i = 0; i < PERFORMANCE_TEST_COUNT && test_running; i++) {
        hcsr04_data_t data;
        if (hcsr04_read_data(&data) == HCSR04_OK && data.valid) {
            success_count++;
        }
        
        if (i % 20 == 0) {
            printf("已完成 %d/%d 次测试\n", i, PERFORMANCE_TEST_COUNT);
        }
        
        usleep(100000); // 100ms间隔
    }
    
    double elapsed_ms = performance_test_end();
    double avg_time_ms = elapsed_ms / PERFORMANCE_TEST_COUNT;
    
    printf("性能测试结果:\n");
    printf("  总耗时: %.2f ms\n", elapsed_ms);
    printf("  平均每次: %.2f ms\n", avg_time_ms);
    printf("  成功率: %.1f%% (%d/%d)\n", 
           (double)success_count / PERFORMANCE_TEST_COUNT * 100.0,
           success_count, PERFORMANCE_TEST_COUNT);
    
    TEST_ASSERT(avg_time_ms < 200.0, "平均响应时间应该小于200ms");
    TEST_ASSERT(success_count > PERFORMANCE_TEST_COUNT * 0.80, "成功率应该大于80%");
    
    hcsr04_deinit();
    
    TEST_CASE_END();
}

/**
 * @brief 测试HC-SR04高精度读取
 */
void test_hcsr04_averaged_reading(void) {
    TEST_CASE_START("HC-SR04高精度读取测试");
    
    int result = hcsr04_init();
    if (result != HCSR04_OK) {
        TEST_SKIP("传感器初始化失败，跳过高精度测试");
        return;
    }
    
    printf("执行高精度读取测试...\n");
    
    // 测试不同采样次数
    for (int samples = 1; samples <= 5; samples++) {
        hcsr04_data_t data;
        result = hcsr04_read_averaged(&data, samples);
        
        if (result == HCSR04_OK && data.valid) {
            printf("  %d次采样: 距离=%.3f cm, 水位=%.3f cm\n", 
                   samples, data.distance_cm, data.water_level_cm);
            
            TEST_ASSERT(data.distance_cm >= 2.0 && data.distance_cm <= 400.0, 
                        "高精度读取距离应该在合理范围内");
            TEST_ASSERT(data.water_level_cm >= 0.0, "高精度读取水位应该非负");
        }
        
        sleep(1);
    }
    
    hcsr04_deinit();
    
    TEST_CASE_END();
}

/**
 * @brief 主测试函数
 */
int main(void) {
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 初始化测试框架
    test_framework_init();
    
    TEST_SUITE_START("HC-SR04传感器单元测试");
    
    // 执行各项测试
    test_hcsr04_initialization();
    test_hcsr04_data_reading();
    test_hcsr04_configuration();
    test_hcsr04_error_handling();
    test_hcsr04_performance();
    test_hcsr04_averaged_reading();
    
    TEST_SUITE_END();
    
    // 输出测试总结
    test_framework_summary();
    
    return test_framework_get_exit_code();
}
