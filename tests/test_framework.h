#ifndef TEST_FRAMEWORK_H
#define TEST_FRAMEWORK_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <sys/time.h>

#ifdef __cplusplus
extern "C" {
#endif

// 测试结果统计
typedef struct {
    int total_tests;
    int passed_tests;
    int failed_tests;
    int skipped_tests;
    double total_time;
} test_stats_t;

// 全局测试统计
extern test_stats_t g_test_stats;

// 颜色定义
#define COLOR_RED     "\033[0;31m"
#define COLOR_GREEN   "\033[0;32m"
#define COLOR_YELLOW  "\033[1;33m"
#define COLOR_BLUE    "\033[0;34m"
#define COLOR_PURPLE  "\033[0;35m"
#define COLOR_CYAN    "\033[0;36m"
#define COLOR_RESET   "\033[0m"

// 测试宏定义
#define TEST_ASSERT(condition, message) \
    do { \
        g_test_stats.total_tests++; \
        if (condition) { \
            printf(COLOR_GREEN "✅ PASS: %s" COLOR_RESET "\n", message); \
            g_test_stats.passed_tests++; \
        } else { \
            printf(COLOR_RED "❌ FAIL: %s" COLOR_RESET "\n", message); \
            printf(COLOR_RED "   条件失败: %s" COLOR_RESET "\n", #condition); \
            printf(COLOR_RED "   文件: %s, 行: %d" COLOR_RESET "\n", __FILE__, __LINE__); \
            g_test_stats.failed_tests++; \
        } \
    } while(0)

#define TEST_ASSERT_EQUAL(expected, actual, message) \
    do { \
        g_test_stats.total_tests++; \
        if ((expected) == (actual)) { \
            printf(COLOR_GREEN "✅ PASS: %s" COLOR_RESET "\n", message); \
            g_test_stats.passed_tests++; \
        } else { \
            printf(COLOR_RED "❌ FAIL: %s" COLOR_RESET "\n", message); \
            printf(COLOR_RED "   期望值: %d, 实际值: %d" COLOR_RESET "\n", (int)(expected), (int)(actual)); \
            printf(COLOR_RED "   文件: %s, 行: %d" COLOR_RESET "\n", __FILE__, __LINE__); \
            g_test_stats.failed_tests++; \
        } \
    } while(0)

#define TEST_ASSERT_FLOAT_EQUAL(expected, actual, tolerance, message) \
    do { \
        g_test_stats.total_tests++; \
        double diff = (expected) - (actual); \
        if (diff < 0) diff = -diff; \
        if (diff <= (tolerance)) { \
            printf(COLOR_GREEN "✅ PASS: %s" COLOR_RESET "\n", message); \
            g_test_stats.passed_tests++; \
        } else { \
            printf(COLOR_RED "❌ FAIL: %s" COLOR_RESET "\n", message); \
            printf(COLOR_RED "   期望值: %.6f, 实际值: %.6f, 差值: %.6f, 容差: %.6f" COLOR_RESET "\n", \
                   (double)(expected), (double)(actual), diff, (double)(tolerance)); \
            printf(COLOR_RED "   文件: %s, 行: %d" COLOR_RESET "\n", __FILE__, __LINE__); \
            g_test_stats.failed_tests++; \
        } \
    } while(0)

#define TEST_ASSERT_NOT_NULL(ptr, message) \
    TEST_ASSERT((ptr) != NULL, message)

#define TEST_ASSERT_NULL(ptr, message) \
    TEST_ASSERT((ptr) == NULL, message)

#define TEST_SKIP(message) \
    do { \
        printf(COLOR_YELLOW "⏭️  SKIP: %s" COLOR_RESET "\n", message); \
        g_test_stats.skipped_tests++; \
    } while(0)

// 测试套件宏
#define TEST_SUITE_START(suite_name) \
    do { \
        printf(COLOR_BLUE "\n=== 测试套件: %s ===" COLOR_RESET "\n", suite_name); \
        test_stats_t suite_stats = {0}; \
        struct timeval start_time, end_time; \
        gettimeofday(&start_time, NULL);

#define TEST_SUITE_END() \
        gettimeofday(&end_time, NULL); \
        double suite_time = (end_time.tv_sec - start_time.tv_sec) + \
                           (end_time.tv_usec - start_time.tv_usec) / 1000000.0; \
        g_test_stats.total_time += suite_time; \
        printf(COLOR_BLUE "=== 套件完成 (%.3fs) ===" COLOR_RESET "\n", suite_time); \
    } while(0)

// 单个测试宏
#define TEST_CASE_START(test_name) \
    do { \
        printf(COLOR_CYAN "\n--- 测试用例: %s ---" COLOR_RESET "\n", test_name); \
        struct timeval test_start, test_end; \
        gettimeofday(&test_start, NULL);

#define TEST_CASE_END() \
        gettimeofday(&test_end, NULL); \
        double test_time = (test_end.tv_sec - test_start.tv_sec) + \
                          (test_end.tv_usec - test_start.tv_usec) / 1000000.0; \
        printf(COLOR_CYAN "--- 用例完成 (%.3fs) ---" COLOR_RESET "\n", test_time); \
    } while(0)

// 函数声明
void test_framework_init(void);
void test_framework_summary(void);
int test_framework_get_exit_code(void);
void test_framework_reset_stats(void);

// 性能测试辅助函数
double get_current_time_ms(void);
void performance_test_start(void);
double performance_test_end(void);

// 内存泄漏检测 (简单版本)
void* test_malloc(size_t size, const char* file, int line);
void test_free(void* ptr, const char* file, int line);
void test_memory_report(void);

#define TEST_MALLOC(size) test_malloc(size, __FILE__, __LINE__)
#define TEST_FREE(ptr) test_free(ptr, __FILE__, __LINE__)

#ifdef __cplusplus
}
#endif

#endif // TEST_FRAMEWORK_H
