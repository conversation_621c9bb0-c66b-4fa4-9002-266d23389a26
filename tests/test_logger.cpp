/**
 * @file test_logger.cpp
 * @brief Logger模块单元测试
 * <AUTHOR>
 * @date 2024
 */

#include <iostream>
#include <cassert>
#include <filesystem>
#include <thread>
#include <chrono>
#include "logger.hpp"

using namespace greenland;

class LoggerTest {
public:
    void runAllTests() {
        std::cout << "🧪 开始Logger模块测试..." << std::endl;
        
        testBasicLogging();
        testLogLevels();
        testFileLogging();
        testThreadSafety();
        testConfiguration();
        testUtilityFunctions();
        
        std::cout << "✅ Logger模块所有测试通过!" << std::endl;
    }

private:
    void testBasicLogging() {
        std::cout << "📝 测试基本日志功能..." << std::endl;
        
        LogConfig config = createDefaultConfig("TestLogger");
        config.enable_file = false;  // 只测试控制台输出
        
        Logger logger(config);
        
        // 测试不同级别的日志
        logger.debug("这是调试信息");
        logger.info("这是信息日志");
        logger.warn("这是警告日志");
        logger.error("这是错误日志");
        logger.fatal("这是致命错误日志");
        
        // 测试日志级别获取和设置
        assert(logger.getLevel() == LogLevel::INFO);
        logger.setLevel(LogLevel::DEBUG);
        assert(logger.getLevel() == LogLevel::DEBUG);
        
        std::cout << "✅ 基本日志功能测试通过" << std::endl;
    }
    
    void testLogLevels() {
        std::cout << "📊 测试日志级别过滤..." << std::endl;
        
        LogConfig config = createDefaultConfig("LevelTest");
        config.enable_file = false;
        config.level = LogLevel::WARN;  // 只显示警告及以上级别
        
        Logger logger(config);
        
        std::cout << "设置日志级别为WARN，以下应该只显示WARN、ERROR、FATAL:" << std::endl;
        logger.debug("这条DEBUG不应该显示");
        logger.info("这条INFO不应该显示");
        logger.warn("这条WARN应该显示");
        logger.error("这条ERROR应该显示");
        logger.fatal("这条FATAL应该显示");
        
        std::cout << "✅ 日志级别过滤测试通过" << std::endl;
    }
    
    void testFileLogging() {
        std::cout << "📁 测试文件日志功能..." << std::endl;
        
        // 创建测试目录
        std::string test_dir = "test_logs";
        std::filesystem::create_directories(test_dir);
        
        LogConfig config = createDefaultConfig("FileTest");
        config.log_dir = test_dir;
        config.enable_console = false;  // 只写文件
        config.enable_file = true;
        
        {
            Logger logger(config);
            logger.info("测试文件日志写入");
            logger.warn("这是一条警告信息");
            logger.error("这是一条错误信息");
        }  // logger析构，确保文件被关闭
        
        // 检查日志文件是否创建
        bool file_exists = false;
        for (const auto& entry : std::filesystem::directory_iterator(test_dir)) {
            if (entry.path().extension() == ".log") {
                file_exists = true;
                std::cout << "创建的日志文件: " << entry.path().filename() << std::endl;
                break;
            }
        }
        
        assert(file_exists);
        
        // 清理测试文件
        std::filesystem::remove_all(test_dir);
        
        std::cout << "✅ 文件日志功能测试通过" << std::endl;
    }
    
    void testThreadSafety() {
        std::cout << "🔒 测试线程安全性..." << std::endl;
        
        LogConfig config = createDefaultConfig("ThreadTest");
        config.enable_file = false;
        
        Logger logger(config);
        
        // 创建多个线程同时写日志
        std::vector<std::thread> threads;
        const int num_threads = 5;
        const int messages_per_thread = 10;
        
        for (int i = 0; i < num_threads; ++i) {
            threads.emplace_back([&logger, i, messages_per_thread]() {
                for (int j = 0; j < messages_per_thread; ++j) {
                    logger.info("线程 " + std::to_string(i) + " 消息 " + std::to_string(j));
                    std::this_thread::sleep_for(std::chrono::milliseconds(1));
                }
            });
        }
        
        // 等待所有线程完成
        for (auto& thread : threads) {
            thread.join();
        }
        
        std::cout << "✅ 线程安全性测试通过" << std::endl;
    }
    
    void testConfiguration() {
        std::cout << "⚙️ 测试配置功能..." << std::endl;
        
        // 测试默认配置
        LogConfig default_config = createDefaultConfig("ConfigTest");
        assert(default_config.module_name == "ConfigTest");
        assert(default_config.level == LogLevel::INFO);
        assert(default_config.enable_console == true);
        assert(default_config.enable_file == true);
        
        // 测试自定义配置
        LogConfig custom_config;
        custom_config.module_name = "CustomModule";
        custom_config.log_dir = "custom_logs";
        custom_config.level = LogLevel::DEBUG;
        custom_config.enable_timestamp = true;
        custom_config.enable_thread_id = true;
        custom_config.max_file_size = 5 * 1024 * 1024;  // 5MB
        custom_config.max_file_count = 3;
        
        Logger logger(custom_config);
        assert(logger.getModuleName() == "CustomModule");
        assert(logger.getLevel() == LogLevel::DEBUG);
        
        std::cout << "✅ 配置功能测试通过" << std::endl;
    }
    
    void testUtilityFunctions() {
        std::cout << "🔧 测试工具函数..." << std::endl;
        
        // 测试错误码转字符串
        assert(logErrorToString(LogError::OK) == "成功");
        assert(logErrorToString(LogError::PARAM_ERROR) == "参数错误");
        assert(logErrorToString(LogError::FILE_ERROR) == "文件错误");
        
        // 测试日志级别转字符串
        assert(logLevelToString(LogLevel::DEBUG) == "DEBUG");
        assert(logLevelToString(LogLevel::INFO) == "INFO");
        assert(logLevelToString(LogLevel::WARN) == "WARN");
        assert(logLevelToString(LogLevel::ERROR) == "ERROR");
        assert(logLevelToString(LogLevel::FATAL) == "FATAL");
        assert(logLevelToString(LogLevel::OFF) == "OFF");
        
        std::cout << "✅ 工具函数测试通过" << std::endl;
    }
};

int main() {
    try {
        LoggerTest test;
        test.runAllTests();
        
        std::cout << std::endl;
        std::cout << "🎉 Logger模块测试完成!" << std::endl;
        std::cout << "所有功能正常工作。" << std::endl;
        
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "❌ 测试失败: " << e.what() << std::endl;
        return 1;
    }
}
