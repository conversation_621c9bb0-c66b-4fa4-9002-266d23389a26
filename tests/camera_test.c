/**
 * @file camera_test.c
 * @brief 摄像头模块测试程序
 * <AUTHOR>
 * @date 2024
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <signal.h>
#include "camera.h"
#include "logger.h"

static volatile int running = 1;
static camera_t camera = NULL;

// 信号处理函数
void signal_handler(int sig) {
    printf("\n收到信号 %d，正在退出...\n", sig);
    running = 0;

    if (camera) {
        camera_stop_recording(camera);
    }
}

// 打印摄像头信息
void print_camera_info(camera_t cam) {
    camera_info_t info;
    if (camera_get_info(cam, &info) == CAMERA_OK) {
        printf("\n📷 摄像头信息:\n");
        printf("  设备ID: %d\n", info.device_id);
        printf("  分辨率: %dx%d\n", info.width, info.height);
        printf("  帧率: %d fps\n", info.fps);
        printf("  状态: %s\n", camera_state_to_string(info.state));
        printf("  连接状态: %s\n", info.is_connected ? "已连接" : "未连接");
        printf("  已拍照片: %lu 张\n", info.photos_taken);
        printf("  已录视频: %lu 个\n", info.videos_recorded);
        printf("  已捕获帧: %lu 帧\n", info.frames_captured);
        printf("  总文件大小: %.2f MB\n", info.total_file_size / (1024.0 * 1024.0));
    }
}

// 扫描摄像头设备
void scan_cameras() {
    printf("🔍 扫描摄像头设备...\n");

    int devices[8];
    int count = camera_scan_devices(devices, 8);

    if (count > 0) {
        printf("✅ 找到 %d 个摄像头设备:\n", count);
        for (int i = 0; i < count; i++) {
            printf("  设备 %d: /dev/video%d\n", i, devices[i]);
        }
    } else {
        printf("❌ 未找到摄像头设备\n");
    }
    printf("\n");
}

// 测试拍照功能
void test_photo(camera_t cam) {
    printf("📸 测试拍照功能...\n");

    // 快速拍照
    printf("  快速拍照...\n");
    int result = camera_quick_photo(cam, "test_quick");
    if (result == CAMERA_OK) {
        printf("  ✅ 快速拍照成功\n");
    } else {
        printf("  ❌ 快速拍照失败: %s\n", camera_error_to_string(result));
    }

    // 高质量拍照
    printf("  高质量拍照...\n");
    camera_photo_params_t params = {
        .filename = "test_hq",
        .format = CAMERA_FORMAT_JPEG,
        .quality = 100,
        .add_timestamp = true,
        .add_watermark = true,
        .width = 0,
        .height = 0
    };

    result = camera_take_photo(cam, &params);
    if (result == CAMERA_OK) {
        printf("  ✅ 高质量拍照成功\n");
    } else {
        printf("  ❌ 高质量拍照失败: %s\n", camera_error_to_string(result));
    }

    // PNG格式拍照
    printf("  PNG格式拍照...\n");
    params.format = CAMERA_FORMAT_PNG;
    strcpy(params.filename, "test_png");

    result = camera_take_photo(cam, &params);
    if (result == CAMERA_OK) {
        printf("  ✅ PNG格式拍照成功\n");
    } else {
        printf("  ❌ PNG格式拍照失败: %s\n", camera_error_to_string(result));
    }

    printf("\n");
}

// 测试录像功能
void test_video(camera_t cam) {
    printf("🎥 测试录像功能...\n");

    // 短时录像测试
    printf("  开始5秒录像测试...\n");
    camera_video_params_t params = {
        .filename = "test_video",
        .format = CAMERA_VIDEO_MP4,
        .duration_seconds = 5,
        .fps = 0,
        .width = 0,
        .height = 0,
        .add_timestamp = true,
        .add_watermark = true
    };

    int result = camera_start_recording(cam, &params);
    if (result == CAMERA_OK) {
        printf("  ✅ 录像开始\n");

        // 等待录像完成
        for (int i = 5; i > 0; i--) {
            printf("  录像中... %d 秒\r", i);
            fflush(stdout);
            sleep(1);
        }
        printf("\n");

        // 检查录像状态
        camera_state_t state = camera_get_state(cam);
        if (state == CAMERA_STATE_IDLE) {
            printf("  ✅ 录像自动完成\n");
        } else {
            printf("  🔄 手动停止录像...\n");
            camera_stop_recording(cam);
        }
    } else {
        printf("  ❌ 录像开始失败: %s\n", camera_error_to_string(result));
    }

    printf("\n");
}

// 测试参数设置
void test_settings(camera_t cam) {
    printf("⚙️  测试参数设置...\n");

    // 测试分辨率设置
    printf("  设置分辨率为 640x480...\n");
    int result = camera_set_resolution(cam, 640, 480);
    if (result == CAMERA_OK) {
        printf("  ✅ 分辨率设置成功\n");
    } else {
        printf("  ❌ 分辨率设置失败: %s\n", camera_error_to_string(result));
    }

    // 测试帧率设置
    printf("  设置帧率为 15 fps...\n");
    result = camera_set_fps(cam, 15);
    if (result == CAMERA_OK) {
        printf("  ✅ 帧率设置成功\n");
    } else {
        printf("  ❌ 帧率设置失败: %s\n", camera_error_to_string(result));
    }

    // 测试图像参数
    printf("  设置图像参数...\n");
    result = camera_set_image_params(cam, 10, 20, 30, 0);
    if (result == CAMERA_OK) {
        printf("  ✅ 图像参数设置成功\n");
    } else {
        printf("  ❌ 图像参数设置失败: %s\n", camera_error_to_string(result));
    }

    // 测试自动功能
    printf("  设置自动功能...\n");
    result = camera_set_auto_features(cam, true, true);
    if (result == CAMERA_OK) {
        printf("  ✅ 自动功能设置成功\n");
    } else {
        printf("  ❌ 自动功能设置失败: %s\n", camera_error_to_string(result));
    }

    printf("\n");
}

// 显示统计信息
void show_stats(camera_t cam) {
    printf("📊 摄像头统计信息:\n");

    char stats[1024];
    int result = camera_get_stats(cam, stats, sizeof(stats));
    if (result == CAMERA_OK) {
        printf("%s\n", stats);
    } else {
        printf("❌ 获取统计信息失败: %s\n", camera_error_to_string(result));
    }

    printf("\n");
}

int main() {
    printf("🚀 摄像头模块测试程序\n");
    printf("========================\n\n");

    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 创建日志记录器
    log_config_t log_config;
    logger_get_default_config(&log_config, "camera_test");
    log_config.enable_console = false;
    logger_t logger = logger_create(&log_config);

    if (logger) {
        camera_set_logger(logger);
        printf("✅ 日志系统初始化完成\n\n");
    }

    // 扫描摄像头设备
    scan_cameras();

    // 获取默认配置
    camera_config_t config;
    camera_get_default_config(&config);

    // 创建摄像头实例
    printf("📷 创建摄像头实例...\n");
    camera = camera_create(&config);
    if (!camera) {
        printf("❌ 创建摄像头实例失败\n");
        goto cleanup;
    }
    printf("✅ 摄像头实例创建成功\n\n");

    // 初始化摄像头
    printf("🔧 初始化摄像头...\n");
    int result = camera_init(camera);
    if (result != CAMERA_OK) {
        printf("❌ 摄像头初始化失败: %s\n", camera_error_to_string(result));
        goto cleanup;
    }
    printf("✅ 摄像头初始化成功\n\n");

    // 显示摄像头信息
    print_camera_info(camera);

    // 测试各项功能
    if (running) test_photo(camera);
    if (running) test_video(camera);
    if (running) test_settings(camera);
    if (running) show_stats(camera);

    printf("✅ 所有测试完成\n");

cleanup:
    // 清理资源
    if (camera) {
        printf("\n🧹 清理摄像头资源...\n");
        camera_destroy(camera);
        camera = NULL;
    }

    if (logger) {
        logger_destroy(logger);
    }

    printf("👋 测试程序退出\n");
    return 0;
}
