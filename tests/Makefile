# GreenLand 测试程序 Makefile
# 作者: Alex
# 版本: 1.0.0
# 自动检测和编译tests目录下的所有测试文件

# 颜色定义
RED     = \033[0;31m
GREEN   = \033[0;32m
YELLOW  = \033[1;33m
BLUE    = \033[0;34m
PURPLE  = \033[0;35m
CYAN    = \033[0;36m
NC      = \033[0m

# 项目配置
PROJECT_NAME = GreenLand-Tests
VERSION = 1.0.0
AUTHOR = Alex

# 编译器配置
CXX = g++
CC = gcc
CXXFLAGS = -Wall -Wextra -Wno-unused-function -std=c++11 -O2 -fPIC
CFLAGS = -Wall -Wextra -Wno-unused-function -std=c99 -O2 -fPIC

# 目录配置
PROJECT_ROOT = ..
SRC_DIR = $(PROJECT_ROOT)/src
BUILD_DIR = $(PROJECT_ROOT)/build
BIN_DIR = $(PROJECT_ROOT)/bin
TESTS_BIN_DIR = $(BIN_DIR)/Tests
OBJ_DIR = $(BUILD_DIR)/obj

# 模块目录
LOGGER_DIR = $(SRC_DIR)/modules/logger
HCSR04_DIR = $(SRC_DIR)/modules/hcsr04
AHT20_DIR = $(SRC_DIR)/modules/aht20
BH1750_DIR = $(SRC_DIR)/modules/bh1750
CAMERA_DIR = $(SRC_DIR)/modules/camera

# 包含路径
INCLUDES = -I$(LOGGER_DIR)/include \
           -I$(HCSR04_DIR)/include \
           -I$(AHT20_DIR)/include \
           -I$(BH1750_DIR)/include

# 基础库
LIBS = -lwiringPi -lpthread

# 自动检测测试源文件
TEST_C_SOURCES = $(wildcard *.c)
TEST_CPP_SOURCES = $(wildcard *.cpp)
TEST_ALL_SOURCES = $(TEST_C_SOURCES) $(TEST_CPP_SOURCES)

# 生成测试目标文件名
TEST_C_TARGETS = $(patsubst %.c,$(TESTS_BIN_DIR)/%,$(TEST_C_SOURCES))
TEST_CPP_TARGETS = $(patsubst %.cpp,$(TESTS_BIN_DIR)/%,$(TEST_CPP_SOURCES))
TEST_ALL_TARGETS = $(TEST_C_TARGETS) $(TEST_CPP_TARGETS)

# 模块对象文件
LOGGER_OBJ = $(OBJ_DIR)/logger.o
HCSR04_OBJ = $(OBJ_DIR)/hcsr04.o
AHT20_OBJ = $(OBJ_DIR)/aht20.o
BH1750_OBJ = $(OBJ_DIR)/bh1750.o

BASE_OBJECTS = $(LOGGER_OBJ) $(HCSR04_OBJ) $(AHT20_OBJ) $(BH1750_OBJ)

# 检查OpenCV
OPENCV_EXISTS := $(shell pkg-config --exists opencv4 2>/dev/null && echo "opencv4" || \
                         (pkg-config --exists opencv 2>/dev/null && echo "opencv" || echo "none"))

# 根据OpenCV配置摄像头支持
ifeq ($(OPENCV_EXISTS),opencv4)
    CAMERA_ENABLED = 1
    OPENCV_CFLAGS = $(shell pkg-config --cflags opencv4 2>/dev/null || echo "-I/usr/include/opencv4")
    OPENCV_LIBS = -lopencv_core -lopencv_imgproc -lopencv_imgcodecs -lopencv_videoio -lopencv_highgui
    CAMERA_OBJ = $(OBJ_DIR)/camera.o
    INCLUDES += -I$(CAMERA_DIR)/include $(OPENCV_CFLAGS)
    LIBS += $(OPENCV_LIBS)
    ALL_OBJECTS = $(BASE_OBJECTS) $(CAMERA_OBJ)
else ifeq ($(OPENCV_EXISTS),opencv)
    CAMERA_ENABLED = 1
    OPENCV_CFLAGS = $(shell pkg-config --cflags opencv 2>/dev/null || echo "-I/usr/include/opencv")
    OPENCV_LIBS = -lopencv_core -lopencv_imgproc -lopencv_imgcodecs -lopencv_videoio -lopencv_highgui
    CAMERA_OBJ = $(OBJ_DIR)/camera.o
    INCLUDES += -I$(CAMERA_DIR)/include $(OPENCV_CFLAGS)
    LIBS += $(OPENCV_LIBS)
    ALL_OBJECTS = $(BASE_OBJECTS) $(CAMERA_OBJ)
else
    CAMERA_ENABLED = 0
    ALL_OBJECTS = $(BASE_OBJECTS)
endif

# 默认目标
.PHONY: all clean help info list modules

all: info modules show_results

# 显示项目信息
info:
	@echo "$(CYAN)╔══════════════════════════════════════════════════════════════╗$(NC)"
	@echo "$(CYAN)║                  $(GREEN)GreenLand 测试编译系统$(CYAN)                   ║$(NC)"
	@echo "$(CYAN)╠══════════════════════════════════════════════════════════════╣$(NC)"
	@echo "$(CYAN)║ 项目名称: $(YELLOW)$(PROJECT_NAME)$(CYAN)                                   ║$(NC)"
	@echo "$(CYAN)║ 版本号:   $(YELLOW)$(VERSION)$(CYAN)                                           ║$(NC)"
	@echo "$(CYAN)║ 作者:     $(YELLOW)$(AUTHOR)$(CYAN)                                            ║$(NC)"
	@echo "$(CYAN)║ 编译器:   $(YELLOW)$(CXX) / $(CC)$(CYAN)                                      ║$(NC)"
ifeq ($(CAMERA_ENABLED),1)
	@echo "$(CYAN)║ OpenCV:   $(GREEN)✅ 已启用 ($(OPENCV_EXISTS))$(CYAN)                           ║$(NC)"
	@echo "$(CYAN)║ 摄像头:   $(GREEN)✅ 支持 (500万像素)$(CYAN)                           ║$(NC)"
else
	@echo "$(CYAN)║ OpenCV:   $(RED)❌ 未找到$(CYAN)                                         ║$(NC)"
	@echo "$(CYAN)║ 摄像头:   $(RED)❌ 禁用$(CYAN)                                           ║$(NC)"
endif
	@echo "$(CYAN)║ 测试文件: $(YELLOW)$(words $(TEST_ALL_SOURCES)) 个 (C: $(words $(TEST_C_SOURCES)), C++: $(words $(TEST_CPP_SOURCES)))$(CYAN)                    ║$(NC)"
	@echo "$(CYAN)║ 输出目录: $(YELLOW)$(TESTS_BIN_DIR)$(CYAN)                              ║$(NC)"
	@echo "$(CYAN)╚══════════════════════════════════════════════════════════════╝$(NC)"
	@echo ""

# 列出所有测试文件
list:
	@echo "$(BLUE)📋 检测到的测试文件:$(NC)"
	@if [ -n "$(TEST_C_SOURCES)" ]; then \
		echo "$(YELLOW)C测试文件:$(NC)"; \
		for file in $(TEST_C_SOURCES); do \
			echo "  $(CYAN)$$file$(NC) -> $(GREEN)$(TESTS_BIN_DIR)/$${file%.c}$(NC)"; \
		done; \
	fi
	@if [ -n "$(TEST_CPP_SOURCES)" ]; then \
		echo "$(YELLOW)C++测试文件:$(NC)"; \
		for file in $(TEST_CPP_SOURCES); do \
			echo "  $(CYAN)$$file$(NC) -> $(GREEN)$(TESTS_BIN_DIR)/$${file%.cpp}$(NC)"; \
		done; \
	fi
	@if [ -z "$(TEST_ALL_SOURCES)" ]; then \
		echo "$(RED)❌ 没有找到测试文件$(NC)"; \
	fi
	@echo ""

# 创建目录
$(OBJ_DIR) $(TESTS_BIN_DIR):
	@echo "$(BLUE)📁 创建目录: $@$(NC)"
	@mkdir -p $@

# 编译模块对象文件
modules: $(ALL_OBJECTS)
	@echo "$(GREEN)✅ 所有模块编译完成$(NC)"

# 编译日志模块
$(LOGGER_OBJ): $(LOGGER_DIR)/src/logger.c | $(OBJ_DIR)
	@echo "$(PURPLE)[1/$(words $(ALL_OBJECTS))]$(NC) $(BLUE)🔧 编译日志模块...$(NC) $(YELLOW)$(notdir $<)$(NC)"
	@$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 编译水位传感器模块
$(HCSR04_OBJ): $(HCSR04_DIR)/src/hcsr04.c | $(OBJ_DIR)
	@echo "$(PURPLE)[2/$(words $(ALL_OBJECTS))]$(NC) $(BLUE)🔧 编译水位传感器...$(NC) $(YELLOW)$(notdir $<)$(NC)"
	@$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 编译温湿度传感器模块
$(AHT20_OBJ): $(AHT20_DIR)/src/aht20.c | $(OBJ_DIR)
	@echo "$(PURPLE)[3/$(words $(ALL_OBJECTS))]$(NC) $(BLUE)🔧 编译温湿度传感器...$(NC) $(YELLOW)$(notdir $<)$(NC)"
	@$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 编译光照传感器模块
$(BH1750_OBJ): $(BH1750_DIR)/src/bh1750.c | $(OBJ_DIR)
	@echo "$(PURPLE)[4/$(words $(ALL_OBJECTS))]$(NC) $(BLUE)🔧 编译光照传感器...$(NC) $(YELLOW)$(notdir $<)$(NC)"
	@$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 编译摄像头模块 (如果启用)
ifeq ($(CAMERA_ENABLED),1)
$(CAMERA_OBJ): $(CAMERA_DIR)/src/camera.cpp | $(OBJ_DIR)
	@echo "$(PURPLE)[5/$(words $(ALL_OBJECTS))]$(NC) $(BLUE)🔧 编译摄像头模块...$(NC) $(YELLOW)$(notdir $<)$(NC)"
	@$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@
endif

# C测试程序编译规则
$(TESTS_BIN_DIR)/%: %.c $(ALL_OBJECTS) | $(TESTS_BIN_DIR)
	@echo "$(GREEN)🧪 编译C测试程序...$(NC) $(YELLOW)$(notdir $<)$(NC)"
	@$(CC) $(CFLAGS) $(INCLUDES) $< $(ALL_OBJECTS) $(LIBS) -o $@
	@echo "$(GREEN)✅ 测试程序编译完成: $(CYAN)$@$(NC)"

# C++测试程序编译规则
$(TESTS_BIN_DIR)/%: %.cpp $(ALL_OBJECTS) | $(TESTS_BIN_DIR)
	@echo "$(GREEN)🧪 编译C++测试程序...$(NC) $(YELLOW)$(notdir $<)$(NC)"
	@$(CXX) $(CXXFLAGS) $(INCLUDES) $< $(ALL_OBJECTS) $(LIBS) -o $@
	@echo "$(GREEN)✅ 测试程序编译完成: $(CYAN)$@$(NC)"

# 编译完成后显示结果
.PHONY: show_results
show_results: $(TEST_ALL_TARGETS)
	@if [ -n "$(TEST_ALL_TARGETS)" ]; then \
		echo ""; \
		echo "$(GREEN)🎉 测试程序编译完成！$(NC)"; \
		echo "$(CYAN)📁 测试程序目录: $(YELLOW)$(TESTS_BIN_DIR)$(NC)"; \
		echo "$(CYAN)🧪 编译的测试程序:$(NC)"; \
		for test in $(TEST_ALL_TARGETS); do \
			if [ -f "$$test" ]; then \
				echo "$(YELLOW)  ./$$test$(NC)"; \
			fi; \
		done; \
		echo ""; \
	else \
		echo "$(YELLOW)⚠️ 没有找到测试文件需要编译$(NC)"; \
	fi

# 清理
clean:
	@echo "$(BLUE)🧹 清理测试构建文件...$(NC)"
	@rm -f $(ALL_OBJECTS)
	@rm -rf $(TESTS_BIN_DIR)
	@echo "$(GREEN)✅ 清理完成$(NC)"

# 深度清理 (包括主项目的构建文件)
distclean:
	@echo "$(BLUE)🧹 深度清理所有构建文件...$(NC)"
	@rm -rf $(BUILD_DIR)
	@rm -rf $(BIN_DIR)
	@echo "$(GREEN)✅ 深度清理完成$(NC)"

# 帮助信息
help:
	@echo "$(CYAN)GreenLand 测试编译系统帮助$(NC)"
	@echo "$(CYAN)============================$(NC)"
	@echo "$(YELLOW)make$(NC)          - 编译所有测试程序"
	@echo "$(YELLOW)make modules$(NC)  - 只编译模块对象文件"
	@echo "$(YELLOW)make list$(NC)     - 列出所有检测到的测试文件"
	@echo "$(YELLOW)make clean$(NC)    - 清理测试构建文件"
	@echo "$(YELLOW)make distclean$(NC) - 深度清理所有构建文件"
	@echo "$(YELLOW)make help$(NC)     - 显示帮助信息"
	@echo "$(YELLOW)make info$(NC)     - 显示项目信息"
	@echo ""
	@echo "$(CYAN)特性:$(NC)"
	@echo "$(GREEN)✅ 自动检测新的.c和.cpp测试文件$(NC)"
	@echo "$(GREEN)✅ 自动配置OpenCV和摄像头支持$(NC)"
	@echo "$(GREEN)✅ 彩色编译进度显示$(NC)"
	@echo "$(GREEN)✅ 智能依赖管理$(NC)"
	@echo ""
	@echo "$(CYAN)使用方法:$(NC)"
	@echo "$(YELLOW)1. 在tests目录下添加.c或.cpp测试文件$(NC)"
	@echo "$(YELLOW)2. 运行 'make' 自动编译所有测试程序$(NC)"
	@echo "$(YELLOW)3. 测试程序生成在 ../bin/Tests/ 目录下$(NC)"

.PHONY: show_results
