#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include "aht20.h"

int main() {
    printf("=== AHT20 简单测试程序 ===\n");
    
    // 初始化传感器
    if (aht20_init() != AHT20_OK) {
        printf("❌ AHT20初始化失败\n");
        printf("请确保:\n");
        printf("1. AHT20传感器连接到I2C-2总线\n");
        printf("2. I2C地址为0x38\n");
        printf("3. 以sudo权限运行此程序\n");
        return 1;
    }
    
    printf("✅ AHT20初始化成功\n\n");
    
    // 读取10次数据
    for (int i = 0; i < 10; i++) {
        aht20_data_t data;
        
        if (aht20_read_data(&data) == AHT20_OK) {
            printf("第%2d次读取: 温度=%.2f°C, 湿度=%.2f%%\n", 
                   i + 1, data.temperature, data.humidity);
        } else {
            printf("第%2d次读取失败\n", i + 1);
        }
        
        sleep(1); // 等待1秒
    }
    
    // 清理资源
    aht20_deinit();
    printf("\n✅ 测试完成\n");
    
    return 0;
}
