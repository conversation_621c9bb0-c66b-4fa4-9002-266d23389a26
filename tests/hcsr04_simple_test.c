/**
 * @file hcsr04_simple_test.c
 * @brief HC-SR04 超声波传感器简单测试程序
 * <AUTHOR> Team
 * @date 2024
 * 
 * 简单的 HC-SR04 功能测试程序
 * 
 * 编译命令:
 * gcc -o hcsr04_simple_test hcsr04_simple_test.c -I../src/modules/hcsr04/include -L../lib -lhcsr04 -lm
 * 
 * 运行命令:
 * sudo ./hcsr04_simple_test
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include "hcsr04.h"

// 全局变量
static volatile int running = 1;

/**
 * @brief 信号处理函数
 */
void signal_handler(int sig) {
    printf("\n收到信号 %d，正在退出...\n", sig);
    running = 0;
}

/**
 * @brief 测试基础功能
 */
void test_basic_functions(void) {
    printf("=== 基础功能测试 ===\n");
    
    // 测试初始化
    printf("1. 测试初始化...\n");
    int result = hcsr04_init();
    if (result == HCSR04_OK) {
        printf("   ✅ 初始化成功\n");
    } else {
        printf("   ❌ 初始化失败: %s\n", hcsr04_error_to_string(result));
        return;
    }
    
    // 测试状态检查
    printf("2. 测试状态检查...\n");
    if (hcsr04_is_initialized()) {
        printf("   ✅ 传感器已初始化\n");
    } else {
        printf("   ❌ 传感器未初始化\n");
    }
    
    // 测试配置获取
    printf("3. 测试配置获取...\n");
    printf("   水箱高度: %.1f cm\n", hcsr04_get_tank_height());
    printf("   传感器偏移: %.1f cm\n", hcsr04_get_sensor_offset());
    
    // 测试状态信息
    printf("4. 测试状态信息...\n");
    char status_info[512];
    result = hcsr04_get_status_info(status_info, sizeof(status_info));
    if (result == HCSR04_OK) {
        printf("   ✅ 状态信息获取成功\n");
        printf("%s\n", status_info);
    } else {
        printf("   ❌ 状态信息获取失败\n");
    }
    
    // 测试数据读取
    printf("5. 测试数据读取...\n");
    hcsr04_data_t data;
    result = hcsr04_read_data(&data);
    if (result == HCSR04_OK && data.valid) {
        printf("   ✅ 数据读取成功\n");
        printf("   距离: %.2f cm\n", data.distance_cm);
        printf("   水位: %.2f cm (%.1f%%)\n", data.water_level_cm, data.water_level_percent);
        printf("   回响时间: %u μs\n", data.echo_time_us);
    } else {
        printf("   ❌ 数据读取失败: %s\n", hcsr04_error_to_string(result));
    }
    
    // 测试简化接口
    printf("6. 测试简化接口...\n");
    float distance, water_level;
    
    result = hcsr04_read_distance(&distance);
    if (result == HCSR04_OK) {
        printf("   ✅ 距离读取: %.2f cm\n", distance);
    } else {
        printf("   ❌ 距离读取失败: %s\n", hcsr04_error_to_string(result));
    }
    
    result = hcsr04_read_water_level(&water_level);
    if (result == HCSR04_OK) {
        printf("   ✅ 水位读取: %.2f cm\n", water_level);
    } else {
        printf("   ❌ 水位读取失败: %s\n", hcsr04_error_to_string(result));
    }
    
    // 测试配置设置
    printf("7. 测试配置设置...\n");
    float original_height = hcsr04_get_tank_height();
    result = hcsr04_set_tank_height(120.0f);
    if (result == HCSR04_OK && hcsr04_get_tank_height() == 120.0f) {
        printf("   ✅ 水箱高度设置成功\n");
        hcsr04_set_tank_height(original_height); // 恢复原值
    } else {
        printf("   ❌ 水箱高度设置失败\n");
    }
    
    // 测试清理
    printf("8. 测试清理...\n");
    hcsr04_deinit();
    if (!hcsr04_is_initialized()) {
        printf("   ✅ 清理成功\n");
    } else {
        printf("   ❌ 清理失败\n");
    }
    
    printf("=== 基础功能测试完成 ===\n\n");
}

/**
 * @brief 测试连续读取
 */
void test_continuous_reading(void) {
    printf("=== 连续读取测试 ===\n");
    printf("将进行10次连续读取测试...\n");
    
    // 重新初始化
    int result = hcsr04_init();
    if (result != HCSR04_OK) {
        printf("初始化失败: %s\n", hcsr04_error_to_string(result));
        return;
    }
    
    int success_count = 0;
    int error_count = 0;
    
    for (int i = 1; i <= 10 && running; i++) {
        printf("第 %2d 次读取: ", i);
        
        hcsr04_data_t data;
        result = hcsr04_read_data(&data);
        
        if (result == HCSR04_OK && data.valid) {
            printf("距离=%.2f cm, 水位=%.2f cm (%.1f%%)\n",
                   data.distance_cm, data.water_level_cm, data.water_level_percent);
            success_count++;
        } else {
            printf("失败: %s\n", hcsr04_error_to_string(result));
            error_count++;
        }
        
        sleep(1);
    }
    
    printf("\n连续读取测试结果:\n");
    printf("  成功: %d 次\n", success_count);
    printf("  失败: %d 次\n", error_count);
    printf("  成功率: %.1f%%\n", (float)success_count / 10 * 100);
    
    hcsr04_deinit();
    printf("=== 连续读取测试完成 ===\n\n");
}

/**
 * @brief 测试高精度读取
 */
void test_averaged_reading(void) {
    printf("=== 高精度读取测试 ===\n");
    printf("将进行5次采样的高精度读取...\n");
    
    // 重新初始化
    int result = hcsr04_init();
    if (result != HCSR04_OK) {
        printf("初始化失败: %s\n", hcsr04_error_to_string(result));
        return;
    }
    
    hcsr04_data_t data;
    result = hcsr04_read_averaged(&data, 5);
    
    if (result == HCSR04_OK && data.valid) {
        printf("✅ 高精度读取成功:\n");
        printf("  平均距离: %.3f cm\n", data.distance_cm);
        printf("  平均水位: %.3f cm (%.2f%%)\n", data.water_level_cm, data.water_level_percent);
        printf("  平均回响时间: %u μs\n", data.echo_time_us);
    } else {
        printf("❌ 高精度读取失败: %s\n", hcsr04_error_to_string(result));
    }
    
    hcsr04_deinit();
    printf("=== 高精度读取测试完成 ===\n\n");
}

/**
 * @brief 主函数
 */
int main(void) {
    printf("HC-SR04 超声波传感器简单测试程序\n");
    printf("=====================================\n\n");
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 检查运行权限
    if (getuid() != 0) {
        printf("警告: 建议使用 sudo 运行此程序以获得 GPIO 访问权限\n\n");
    }
    
    // 执行各项测试
    test_basic_functions();
    
    if (running) {
        test_continuous_reading();
    }
    
    if (running) {
        test_averaged_reading();
    }
    
    printf("所有测试完成！\n");
    return 0;
}
