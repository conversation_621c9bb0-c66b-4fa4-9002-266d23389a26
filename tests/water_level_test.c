/**
 * @file water_level_test.c
 * @brief 水位检测专用测试程序
 * <AUTHOR> Team
 * @date 2024
 * 
 * 专门用于测试 HC-SR04 水位检测功能的测试程序
 * 包含基础测试、精度测试、报警测试等
 * 
 * 编译命令:
 * make water_level_test
 * 
 * 运行命令:
 * sudo ./bin/Tests/water_level_test
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <time.h>
#include <math.h>
#include <string.h>
#include "hcsr04.h"

// 测试配置
#define TEST_DURATION 60        // 测试持续时间(秒)
#define SAMPLE_INTERVAL 2       // 采样间隔(秒)
#define ALARM_LOW_LEVEL 15.0    // 低水位报警阈值(%)
#define ALARM_HIGH_LEVEL 85.0   // 高水位报警阈值(%)

// 全局变量
static volatile int running = 1;
static int test_count = 0;
static int success_count = 0;
static int error_count = 0;

// 统计数据
static float min_distance = 999.0;
static float max_distance = 0.0;
static float total_distance = 0.0;
static float min_water_level = 999.0;
static float max_water_level = 0.0;
static float total_water_level = 0.0;

/**
 * @brief 信号处理函数
 */
void signal_handler(int sig) {
    printf("\n收到信号 %d，正在停止测试...\n", sig);
    running = 0;
}

/**
 * @brief 显示测试配置
 */
void show_test_config(void) {
    printf("=== 水位检测测试配置 ===\n");
    printf("测试持续时间: %d 秒\n", TEST_DURATION);
    printf("采样间隔: %d 秒\n", SAMPLE_INTERVAL);
    printf("低水位报警: %.1f%%\n", ALARM_LOW_LEVEL);
    printf("高水位报警: %.1f%%\n", ALARM_HIGH_LEVEL);
    
    char status_info[512];
    if (hcsr04_get_status_info(status_info, sizeof(status_info)) == HCSR04_OK) {
        printf("\n%s\n", status_info);
    }
    printf("========================\n\n");
}

/**
 * @brief 更新统计数据
 */
void update_statistics(const hcsr04_data_t* data) {
    if (!data || !data->valid) return;
    
    // 距离统计
    if (data->distance_cm < min_distance) min_distance = data->distance_cm;
    if (data->distance_cm > max_distance) max_distance = data->distance_cm;
    total_distance += data->distance_cm;
    
    // 水位统计
    if (data->water_level_cm < min_water_level) min_water_level = data->water_level_cm;
    if (data->water_level_cm > max_water_level) max_water_level = data->water_level_cm;
    total_water_level += data->water_level_cm;
}

/**
 * @brief 检查水位报警
 */
void check_water_level_alarm(const hcsr04_data_t* data) {
    if (!data || !data->valid) return;
    
    if (data->water_level_percent < ALARM_LOW_LEVEL) {
        printf("🚨 低水位报警: %.1f%% (< %.1f%%)\n", 
               data->water_level_percent, ALARM_LOW_LEVEL);
    } else if (data->water_level_percent > ALARM_HIGH_LEVEL) {
        printf("🚨 高水位报警: %.1f%% (> %.1f%%)\n", 
               data->water_level_percent, ALARM_HIGH_LEVEL);
    }
}

/**
 * @brief 显示实时数据
 */
void display_real_time_data(const hcsr04_data_t* data) {
    if (!data) return;
    
    time_t now = time(NULL);
    struct tm* tm_info = localtime(&now);
    char time_str[32];
    strftime(time_str, sizeof(time_str), "%H:%M:%S", tm_info);
    
    if (data->valid) {
        printf("[%s] #%03d: 距离=%.2f cm, 水位=%.2f cm (%.1f%%), 回响=%u μs\n",
               time_str, test_count,
               data->distance_cm, data->water_level_cm, 
               data->water_level_percent, data->echo_time_us);
        
        // 检查报警
        check_water_level_alarm(data);
        
        // 更新统计
        update_statistics(data);
        success_count++;
    } else {
        printf("[%s] #%03d: ❌ 读取失败\n", time_str, test_count);
        error_count++;
    }
}

/**
 * @brief 显示测试统计
 */
void show_test_statistics(void) {
    printf("\n=== 测试统计结果 ===\n");
    printf("总测试次数: %d\n", test_count);
    printf("成功次数: %d\n", success_count);
    printf("失败次数: %d\n", error_count);
    printf("成功率: %.1f%%\n", success_count > 0 ? (float)success_count / test_count * 100 : 0);
    
    if (success_count > 0) {
        printf("\n距离统计:\n");
        printf("  最小距离: %.2f cm\n", min_distance);
        printf("  最大距离: %.2f cm\n", max_distance);
        printf("  平均距离: %.2f cm\n", total_distance / success_count);
        printf("  距离变化: %.2f cm\n", max_distance - min_distance);
        
        printf("\n水位统计:\n");
        printf("  最小水位: %.2f cm\n", min_water_level);
        printf("  最大水位: %.2f cm\n", max_water_level);
        printf("  平均水位: %.2f cm\n", total_water_level / success_count);
        printf("  水位变化: %.2f cm\n", max_water_level - min_water_level);
        
        float avg_water_percent = (total_water_level / success_count) / hcsr04_get_tank_height() * 100;
        printf("  平均水位百分比: %.1f%%\n", avg_water_percent);
    }
    printf("==================\n");
}

/**
 * @brief 基础功能测试
 */
int basic_function_test(void) {
    printf("=== 基础功能测试 ===\n");
    
    // 测试初始化
    printf("1. 测试传感器初始化...\n");
    int result = hcsr04_init();
    if (result != HCSR04_OK) {
        printf("   ❌ 初始化失败: %s\n", hcsr04_error_to_string(result));
        return -1;
    }
    printf("   ✅ 初始化成功\n");
    
    // 测试配置
    printf("2. 测试配置设置...\n");
    hcsr04_set_tank_height(100.0f);
    hcsr04_set_sensor_offset(5.0f);
    printf("   ✅ 水箱高度: %.1f cm\n", hcsr04_get_tank_height());
    printf("   ✅ 传感器偏移: %.1f cm\n", hcsr04_get_sensor_offset());
    
    // 测试单次读取
    printf("3. 测试单次数据读取...\n");
    hcsr04_data_t data;
    result = hcsr04_read_data(&data);
    if (result == HCSR04_OK && data.valid) {
        printf("   ✅ 读取成功: 距离=%.2f cm, 水位=%.2f cm (%.1f%%)\n",
               data.distance_cm, data.water_level_cm, data.water_level_percent);
    } else {
        printf("   ❌ 读取失败: %s\n", hcsr04_error_to_string(result));
        hcsr04_deinit();
        return -1;
    }
    
    // 测试高精度读取
    printf("4. 测试高精度读取(5次采样)...\n");
    result = hcsr04_read_averaged(&data, 5);
    if (result == HCSR04_OK && data.valid) {
        printf("   ✅ 高精度读取成功: 距离=%.3f cm, 水位=%.3f cm\n",
               data.distance_cm, data.water_level_cm);
    } else {
        printf("   ❌ 高精度读取失败: %s\n", hcsr04_error_to_string(result));
    }
    
    printf("=== 基础功能测试完成 ===\n\n");
    return 0;
}

/**
 * @brief 连续监测测试
 */
void continuous_monitoring_test(void) {
    printf("=== 连续水位监测测试 ===\n");
    printf("测试时长: %d 秒，采样间隔: %d 秒\n", TEST_DURATION, SAMPLE_INTERVAL);
    printf("按 Ctrl+C 可提前结束测试\n\n");
    
    printf("时间     | 次数 | 距离(cm) | 水位(cm) | 水位(%%) | 回响(μs) | 状态\n");
    printf("---------|------|----------|----------|---------|----------|------\n");
    
    time_t start_time = time(NULL);
    
    while (running && (time(NULL) - start_time) < TEST_DURATION) {
        test_count++;
        
        // 使用高精度读取(3次采样)
        hcsr04_data_t data;
        int result = hcsr04_read_averaged(&data, 3);
        
        if (result == HCSR04_OK) {
            display_real_time_data(&data);
        } else {
            time_t now = time(NULL);
            struct tm* tm_info = localtime(&now);
            char time_str[32];
            strftime(time_str, sizeof(time_str), "%H:%M:%S", tm_info);
            
            printf("[%s] #%03d: ❌ 读取失败: %s\n", 
                   time_str, test_count, hcsr04_error_to_string(result));
            error_count++;
        }
        
        // 等待下次采样
        sleep(SAMPLE_INTERVAL);
    }
    
    printf("\n=== 连续监测测试完成 ===\n");
}

/**
 * @brief 主函数
 */
int main(void) {
    printf("🌊 HC-SR04 水位检测专用测试程序\n");
    printf("================================\n\n");
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 检查权限
    if (getuid() != 0) {
        printf("⚠️  警告: 建议使用 sudo 运行此程序以获得 GPIO 访问权限\n\n");
    }
    
    // 执行基础功能测试
    if (basic_function_test() != 0) {
        printf("❌ 基础功能测试失败，程序退出\n");
        return -1;
    }
    
    // 显示测试配置
    show_test_config();
    
    // 执行连续监测测试
    continuous_monitoring_test();
    
    // 显示统计结果
    show_test_statistics();
    
    // 清理资源
    hcsr04_deinit();
    
    printf("\n🎉 水位检测测试完成！\n");
    return 0;
}
