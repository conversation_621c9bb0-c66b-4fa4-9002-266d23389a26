/**
 * @file camera_enhanced_test.c
 * @brief 摄像头模块增强功能测试程序
 * <AUTHOR>
 * @date 2024
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <string.h>
#include "camera.h"
#include "logger.h"

static volatile int running = 1;
static camera_t camera = NULL;

// 信号处理函数
void signal_handler(int sig) {
    printf("\n收到退出信号，正在停止...\n");
    running = 0;
    
    if (camera) {
        camera_stop_recording(camera);
    }
}

// 测试旋转功能
void test_rotation_feature(camera_t cam) {
    printf("🔄 测试图像旋转功能...\n");
    
    camera_rotation_t rotations[] = {
        CAMERA_ROTATE_0, CAMERA_ROTATE_90, 
        CAMERA_ROTATE_180, CAMERA_ROTATE_270
    };
    
    const char* rotation_names[] = {
        "0度", "90度", "180度", "270度"
    };
    
    for (int i = 0; i < 4; i++) {
        printf("  设置旋转角度: %s\n", rotation_names[i]);
        
        int result = camera_set_rotation(cam, rotations[i]);
        if (result == CAMERA_OK) {
            printf("  ✅ 旋转角度设置成功\n");
            
            // 拍照测试旋转效果
            char filename[64];
            snprintf(filename, sizeof(filename), "rotation_test_%s", rotation_names[i]);
            
            result = camera_quick_photo(cam, filename);
            if (result == CAMERA_OK) {
                printf("  ✅ 旋转测试照片拍摄成功: %s\n", filename);
            } else {
                printf("  ❌ 旋转测试照片拍摄失败: %s\n", camera_error_to_string(result));
            }
        } else {
            printf("  ❌ 旋转角度设置失败: %s\n", camera_error_to_string(result));
        }
        
        sleep(1);
    }
    
    // 恢复到0度
    camera_set_rotation(cam, CAMERA_ROTATE_0);
    printf("  🔄 已恢复到0度旋转\n\n");
}

// 测试500万像素分辨率
void test_5mp_resolution(camera_t cam) {
    printf("📷 测试500万像素分辨率...\n");
    
    // 设置为500万像素分辨率
    int result = camera_set_resolution(cam, 2592, 1944);
    if (result == CAMERA_OK) {
        printf("  ✅ 分辨率设置为500万像素 (2592x1944)\n");
        
        // 拍摄高分辨率照片
        camera_photo_params_t params;
        strncpy(params.filename, "5mp_test", sizeof(params.filename) - 1);
        params.filename[sizeof(params.filename) - 1] = '\0';
        params.format = CAMERA_FORMAT_JPEG;
        params.quality = 100;
        params.add_timestamp = true;
        params.add_watermark = true;
        params.width = 2592;
        params.height = 1944;
        
        result = camera_take_photo(cam, &params);
        if (result == CAMERA_OK) {
            printf("  ✅ 500万像素照片拍摄成功\n");
        } else {
            printf("  ❌ 500万像素照片拍摄失败: %s\n", camera_error_to_string(result));
        }
    } else {
        printf("  ❌ 500万像素分辨率设置失败: %s\n", camera_error_to_string(result));
    }
    
    printf("\n");
}

// 测试文件夹分类功能
void test_folder_classification(camera_t cam) {
    printf("📁 测试文件夹分类功能...\n");
    
    // 获取当前配置
    camera_config_t config;
    camera_get_config(cam, &config);
    
    printf("  当前配置:\n");
    printf("    保存路径: %s\n", config.save_path);
    printf("    照片文件夹: %s\n", config.photo_folder);
    printf("    视频文件夹: %s\n", config.video_folder);
    
    // 拍照测试 - 应该保存到 Media/Photos/
    printf("  拍照测试 (应保存到 Photos 文件夹)...\n");
    int result = camera_quick_photo(cam, "folder_test_photo");
    if (result == CAMERA_OK) {
        printf("  ✅ 照片已保存到 %s/%s/ 文件夹\n", config.save_path, config.photo_folder);
    } else {
        printf("  ❌ 照片保存失败: %s\n", camera_error_to_string(result));
    }
    
    // 录像测试 - 应该保存到 Media/Videos/
    printf("  录像测试 (应保存到 Videos 文件夹)...\n");
    result = camera_quick_video(cam, "folder_test_video", 3);
    if (result == CAMERA_OK) {
        printf("  ✅ 录像开始，将保存到 %s/%s/ 文件夹\n", config.save_path, config.video_folder);
        
        // 等待录像完成
        for (int i = 3; i > 0 && running; i--) {
            printf("  录像中... %d 秒\r", i);
            fflush(stdout);
            sleep(1);
        }
        printf("\n  ✅ 录像完成\n");
    } else {
        printf("  ❌ 录像失败: %s\n", camera_error_to_string(result));
    }
    
    printf("\n");
}

// 显示增强的摄像头信息
void show_enhanced_info(camera_t cam) {
    printf("📊 增强摄像头信息:\n");
    
    camera_info_t info;
    if (camera_get_info(cam, &info) == CAMERA_OK) {
        printf("  设备ID: %d\n", info.device_id);
        printf("  分辨率: %dx%d\n", info.width, info.height);
        printf("  帧率: %d fps\n", info.fps);
        printf("  状态: %s\n", camera_state_to_string(info.state));
        printf("  连接状态: %s\n", info.is_connected ? "已连接" : "未连接");
    }
    
    // 显示当前旋转角度
    camera_rotation_t rotation = camera_get_rotation(cam);
    printf("  当前旋转角度: %s\n", camera_rotation_to_string(rotation));
    
    // 显示配置信息
    camera_config_t config;
    if (camera_get_config(cam, &config) == CAMERA_OK) {
        printf("  分辨率预设: %s\n", camera_resolution_to_string(config.resolution));
        printf("  保存路径: %s\n", config.save_path);
        printf("  照片文件夹: %s\n", config.photo_folder);
        printf("  视频文件夹: %s\n", config.video_folder);
    }
    
    printf("\n");
}

int main() {
    printf("🚀 摄像头模块增强功能测试\n");
    printf("============================\n\n");
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 创建日志记录器
    log_config_t log_config;
    logger_get_default_config(&log_config, "camera_enhanced_test");
    logger_t logger = logger_create(&log_config);
    
    if (logger) {
        camera_set_logger(logger);
        printf("✅ 日志系统初始化完成\n\n");
    }
    
    // 扫描摄像头设备
    printf("🔍 扫描摄像头设备...\n");
    int devices[8];
    int device_count = camera_scan_devices(devices, 8);
    
    if (device_count == 0) {
        printf("❌ 未找到摄像头设备\n");
        printf("请确保USB摄像头已正确连接\n");
        goto cleanup;
    }
    
    printf("✅ 找到 %d 个摄像头设备\n", device_count);
    for (int i = 0; i < device_count; i++) {
        printf("  设备 %d: /dev/video%d\n", i, devices[i]);
    }
    printf("\n");
    
    // 创建摄像头配置 (使用500万像素默认配置)
    printf("⚙️  配置摄像头...\n");
    camera_config_t config;
    camera_get_default_config(&config);
    
    // 使用第一个设备
    config.device_id = devices[0];
    
    printf("  设备ID: %d\n", config.device_id);
    printf("  默认分辨率: %s (%dx%d)\n", 
           camera_resolution_to_string(config.resolution), config.width, config.height);
    printf("  默认旋转角度: %s\n", camera_rotation_to_string(config.rotation));
    printf("  保存路径: %s\n", config.save_path);
    printf("  照片文件夹: %s\n", config.photo_folder);
    printf("  视频文件夹: %s\n", config.video_folder);
    printf("\n");
    
    // 创建和初始化摄像头
    printf("🔧 创建摄像头实例...\n");
    camera = camera_create(&config);
    if (!camera) {
        printf("❌ 创建摄像头实例失败\n");
        goto cleanup;
    }
    
    printf("🔧 初始化摄像头...\n");
    int result = camera_init(camera);
    if (result != CAMERA_OK) {
        printf("❌ 摄像头初始化失败: %s\n", camera_error_to_string(result));
        goto cleanup;
    }
    
    printf("✅ 摄像头初始化成功\n\n");
    
    // 显示增强信息
    show_enhanced_info(camera);
    
    // 测试各项增强功能
    if (running) test_5mp_resolution(camera);
    if (running) test_rotation_feature(camera);
    if (running) test_folder_classification(camera);
    
    // 显示最终统计
    printf("📊 测试完成统计:\n");
    char stats[1024];
    result = camera_get_stats(camera, stats, sizeof(stats));
    if (result == CAMERA_OK) {
        printf("%s\n", stats);
    }
    
    printf("\n✅ 所有增强功能测试完成\n");
    printf("\n💡 检查生成的文件:\n");
    printf("  - Media/Photos/ - 照片文件\n");
    printf("  - Media/Videos/ - 视频文件\n");
    printf("  - Log/ - 日志文件\n");

cleanup:
    // 清理资源
    if (camera) {
        printf("\n🧹 清理摄像头资源...\n");
        camera_destroy(camera);
        camera = NULL;
    }
    
    if (logger) {
        logger_destroy(logger);
    }
    
    printf("👋 增强功能测试程序退出\n");
    return 0;
}
