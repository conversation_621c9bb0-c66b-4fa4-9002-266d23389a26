/**
 * @file performance_test.c
 * @brief GreenLand系统性能基准测试
 * <AUTHOR> Team
 * @date 2024
 * 
 * 本文件包含GreenLand环境监控系统的性能基准测试，包括：
 * - CPU使用率测试
 * - 内存使用测试
 * - 响应时间测试
 * - 吞吐量测试
 * - 资源利用率测试
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <time.h>
#include <sys/time.h>
#include <sys/resource.h>
#include <string.h>
#include <math.h>
#include "test_framework.h"
#include "aht20.h"
#include "bh1750.h"

// 测试配置
#define PERFORMANCE_ITERATIONS 1000
#define THROUGHPUT_TEST_DURATION 60  // 秒
#define MEMORY_TEST_ITERATIONS 100
#define RESPONSE_TIME_SAMPLES 100

// 全局变量
static volatile int test_running = 1;

// 性能统计结构
typedef struct {
    double min_time;
    double max_time;
    double avg_time;
    double total_time;
    int sample_count;
} performance_stats_t;

// 内存使用统计
typedef struct {
    long initial_memory;
    long peak_memory;
    long final_memory;
} memory_stats_t;

// 信号处理函数
void signal_handler(int sig) {
    test_running = 0;
    printf("\n收到信号 %d，正在停止测试...\n", sig);
}

/**
 * @brief 获取当前内存使用量 (KB)
 */
long get_memory_usage(void) {
    FILE* file = fopen("/proc/self/status", "r");
    if (!file) return -1;
    
    char line[256];
    long memory = -1;
    
    while (fgets(line, sizeof(line), file)) {
        if (strncmp(line, "VmRSS:", 6) == 0) {
            sscanf(line, "VmRSS: %ld kB", &memory);
            break;
        }
    }
    
    fclose(file);
    return memory;
}

/**
 * @brief 获取CPU使用率
 */
double get_cpu_usage(void) {
    struct rusage usage;
    getrusage(RUSAGE_SELF, &usage);
    
    double user_time = usage.ru_utime.tv_sec + usage.ru_utime.tv_usec / 1000000.0;
    double sys_time = usage.ru_stime.tv_sec + usage.ru_stime.tv_usec / 1000000.0;
    
    return user_time + sys_time;
}

/**
 * @brief 更新性能统计
 */
void update_performance_stats(performance_stats_t* stats, double time) {
    if (stats->sample_count == 0) {
        stats->min_time = time;
        stats->max_time = time;
        stats->total_time = time;
    } else {
        if (time < stats->min_time) stats->min_time = time;
        if (time > stats->max_time) stats->max_time = time;
        stats->total_time += time;
    }
    
    stats->sample_count++;
    stats->avg_time = stats->total_time / stats->sample_count;
}

/**
 * @brief 测试AHT20响应时间
 */
void test_aht20_response_time(void) {
    TEST_CASE_START("AHT20响应时间测试");
    
    int result = aht20_init();
    if (result != AHT20_OK) {
        TEST_SKIP("AHT20初始化失败，跳过响应时间测试");
        return;
    }
    
    performance_stats_t stats = {0};
    
    printf("执行 %d 次AHT20响应时间测试...\n", RESPONSE_TIME_SAMPLES);
    
    for (int i = 0; i < RESPONSE_TIME_SAMPLES && test_running; i++) {
        aht20_data_t data;
        
        performance_test_start();
        int read_result = aht20_read_data(&data);
        double elapsed = performance_test_end();
        
        if (read_result == AHT20_OK) {
            update_performance_stats(&stats, elapsed);
        }
        
        if (i % 20 == 0) {
            printf("已完成 %d/%d 次测试\n", i, RESPONSE_TIME_SAMPLES);
        }
        
        usleep(10000); // 10ms间隔
    }
    
    printf("AHT20响应时间统计:\n");
    printf("  最小时间: %.2f ms\n", stats.min_time);
    printf("  最大时间: %.2f ms\n", stats.max_time);
    printf("  平均时间: %.2f ms\n", stats.avg_time);
    printf("  有效样本: %d/%d\n", stats.sample_count, RESPONSE_TIME_SAMPLES);
    
    TEST_ASSERT(stats.avg_time < 100.0, "AHT20平均响应时间应该小于100ms");
    TEST_ASSERT(stats.max_time < 200.0, "AHT20最大响应时间应该小于200ms");
    TEST_ASSERT(stats.sample_count > RESPONSE_TIME_SAMPLES * 0.95, "成功率应该大于95%");
    
    aht20_deinit();
    
    TEST_CASE_END();
}

/**
 * @brief 测试BH1750响应时间
 */
void test_bh1750_response_time(void) {
    TEST_CASE_START("BH1750响应时间测试");
    
    int result = bh1750_init();
    if (result != BH1750_OK) {
        TEST_SKIP("BH1750初始化失败，跳过响应时间测试");
        return;
    }
    
    bh1750_set_mode(BH1750_MODE_CONT_H_RES);
    sleep(1); // 等待稳定
    
    performance_stats_t stats = {0};
    
    printf("执行 %d 次BH1750响应时间测试...\n", RESPONSE_TIME_SAMPLES);
    
    for (int i = 0; i < RESPONSE_TIME_SAMPLES && test_running; i++) {
        float lux;
        
        performance_test_start();
        int read_result = bh1750_read_lux(&lux);
        double elapsed = performance_test_end();
        
        if (read_result == BH1750_OK) {
            update_performance_stats(&stats, elapsed);
        }
        
        if (i % 20 == 0) {
            printf("已完成 %d/%d 次测试\n", i, RESPONSE_TIME_SAMPLES);
        }
        
        usleep(10000); // 10ms间隔
    }
    
    printf("BH1750响应时间统计:\n");
    printf("  最小时间: %.2f ms\n", stats.min_time);
    printf("  最大时间: %.2f ms\n", stats.max_time);
    printf("  平均时间: %.2f ms\n", stats.avg_time);
    printf("  有效样本: %d/%d\n", stats.sample_count, RESPONSE_TIME_SAMPLES);
    
    TEST_ASSERT(stats.avg_time < 50.0, "BH1750平均响应时间应该小于50ms");
    TEST_ASSERT(stats.max_time < 100.0, "BH1750最大响应时间应该小于100ms");
    TEST_ASSERT(stats.sample_count > RESPONSE_TIME_SAMPLES * 0.95, "成功率应该大于95%");
    
    bh1750_deinit();
    
    TEST_CASE_END();
}

/**
 * @brief 测试内存使用
 */
void test_memory_usage(void) {
    TEST_CASE_START("内存使用测试");
    
    memory_stats_t mem_stats;
    mem_stats.initial_memory = get_memory_usage();
    mem_stats.peak_memory = mem_stats.initial_memory;
    
    printf("初始内存使用: %ld KB\n", mem_stats.initial_memory);
    
    // 初始化传感器
    int aht20_result = aht20_init();
    int bh1750_result = bh1750_init();
    
    if (aht20_result != AHT20_OK || bh1750_result != BH1750_OK) {
        TEST_SKIP("传感器初始化失败，跳过内存测试");
        return;
    }
    
    printf("执行 %d 次内存使用测试...\n", MEMORY_TEST_ITERATIONS);
    
    for (int i = 0; i < MEMORY_TEST_ITERATIONS && test_running; i++) {
        // 读取传感器数据
        aht20_data_t aht20_data;
        bh1750_data_t bh1750_data;
        
        aht20_read_data(&aht20_data);
        bh1750_read_data(&bh1750_data);
        
        // 检查内存使用
        long current_memory = get_memory_usage();
        if (current_memory > mem_stats.peak_memory) {
            mem_stats.peak_memory = current_memory;
        }
        
        if (i % 20 == 0) {
            printf("第 %d 次: 当前内存 %ld KB\n", i, current_memory);
        }
        
        usleep(50000); // 50ms间隔
    }
    
    mem_stats.final_memory = get_memory_usage();
    
    printf("内存使用统计:\n");
    printf("  初始内存: %ld KB\n", mem_stats.initial_memory);
    printf("  峰值内存: %ld KB\n", mem_stats.peak_memory);
    printf("  最终内存: %ld KB\n", mem_stats.final_memory);
    printf("  内存增长: %ld KB\n", mem_stats.final_memory - mem_stats.initial_memory);
    
    // 内存使用应该保持稳定
    long memory_growth = mem_stats.final_memory - mem_stats.initial_memory;
    TEST_ASSERT(memory_growth < 1024, "内存增长应该小于1MB"); // 1MB = 1024KB
    TEST_ASSERT(mem_stats.peak_memory < 51200, "峰值内存应该小于50MB"); // 50MB = 51200KB
    
    aht20_deinit();
    bh1750_deinit();
    
    TEST_CASE_END();
}

/**
 * @brief 测试系统吞吐量
 */
void test_system_throughput(void) {
    TEST_CASE_START("系统吞吐量测试");
    
    int aht20_result = aht20_init();
    int bh1750_result = bh1750_init();
    
    if (aht20_result != AHT20_OK || bh1750_result != BH1750_OK) {
        TEST_SKIP("传感器初始化失败，跳过吞吐量测试");
        return;
    }
    
    printf("运行 30 秒吞吐量测试...\n");
    
    time_t start_time = time(NULL);
    int total_operations = 0;
    int successful_operations = 0;
    
    double cpu_start = get_cpu_usage();
    
    while (test_running && (time(NULL) - start_time) < 30) {
        aht20_data_t aht20_data;
        bh1750_data_t bh1750_data;
        
        total_operations += 2; // 两个传感器操作
        
        if (aht20_read_data(&aht20_data) == AHT20_OK) {
            successful_operations++;
        }
        
        if (bh1750_read_data(&bh1750_data) == BH1750_OK) {
            successful_operations++;
        }
        
        if (total_operations % 100 == 0) {
            printf("已执行 %d 次操作\n", total_operations);
        }
        
        usleep(10000); // 10ms间隔
    }
    
    double cpu_end = get_cpu_usage();
    double cpu_usage = cpu_end - cpu_start;
    time_t elapsed_time = time(NULL) - start_time;
    
    double throughput = (double)successful_operations / elapsed_time;
    double success_rate = (double)successful_operations / total_operations * 100.0;
    
    printf("吞吐量测试结果:\n");
    printf("  总操作数: %d\n", total_operations);
    printf("  成功操作: %d\n", successful_operations);
    printf("  测试时长: %ld 秒\n", elapsed_time);
    printf("  吞吐量: %.2f 操作/秒\n", throughput);
    printf("  成功率: %.1f%%\n", success_rate);
    printf("  CPU使用: %.2f 秒\n", cpu_usage);
    
    TEST_ASSERT(throughput > 50.0, "系统吞吐量应该大于50操作/秒");
    TEST_ASSERT(success_rate > 95.0, "操作成功率应该大于95%");
    TEST_ASSERT(cpu_usage < 10.0, "CPU使用时间应该小于10秒");
    
    aht20_deinit();
    bh1750_deinit();
    
    TEST_CASE_END();
}

/**
 * @brief 主测试函数
 */
int main(void) {
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 初始化测试框架
    test_framework_init();
    
    TEST_SUITE_START("GreenLand系统性能基准测试");
    
    // 执行各项测试
    test_aht20_response_time();
    test_bh1750_response_time();
    test_memory_usage();
    test_system_throughput();
    
    TEST_SUITE_END();
    
    // 输出测试总结
    test_framework_summary();
    
    return test_framework_get_exit_code();
}
