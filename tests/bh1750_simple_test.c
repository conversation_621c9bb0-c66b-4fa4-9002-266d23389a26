#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include "bh1750.h"

int main() {
    printf("=== BH1750 简单测试程序 ===\n");
    
    // 初始化传感器
    if (bh1750_init() != BH1750_OK) {
        printf("❌ BH1750初始化失败\n");
        printf("请确保:\n");
        printf("1. BH1750传感器连接到I2C-2总线\n");
        printf("2. I2C地址为0x23 (ADDR引脚接地)\n");
        printf("3. 以sudo权限运行此程序\n");
        return 1;
    }
    
    printf("✅ BH1750初始化成功\n\n");
    
    // 读取10次数据
    for (int i = 0; i < 10; i++) {
        float lux;
        
        if (bh1750_read_lux(&lux) == BH1750_OK) {
            printf("第%2d次读取: 光照强度=%.2f lx\n", i + 1, lux);
        } else {
            printf("第%2d次读取失败\n", i + 1);
        }
        
        sleep(1); // 等待1秒
    }
    
    // 清理资源
    bh1750_deinit();
    printf("\n✅ 测试完成\n");
    
    return 0;
}
