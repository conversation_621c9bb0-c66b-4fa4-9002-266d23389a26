#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <time.h>
#include "bh1750.h"

// 全局变量用于信号处理
static volatile int running = 1;

// 信号处理函数
void signal_handler(int sig) {
    printf("\n收到信号 %d，正在退出...\n", sig);
    running = 0;
}

// 打印错误信息
void print_error(int error_code) {
    switch (error_code) {
        case BH1750_ERROR_INIT:
            printf("❌ 初始化错误\n");
            break;
        case BH1750_ERROR_I2C:
            printf("❌ I2C通信错误\n");
            break;
        case BH1750_ERROR_TIMEOUT:
            printf("❌ 超时错误\n");
            break;
        case BH1750_ERROR_MODE:
            printf("❌ 模式错误\n");
            break;
        case BH1750_ERROR_DATA:
            printf("❌ 数据错误\n");
            break;
        default:
            printf("❌ 未知错误: %d\n", error_code);
            break;
    }
}

// 模式名称映射
const char* get_mode_name(bh1750_mode_t mode) {
    switch (mode) {
        case BH1750_MODE_CONT_H_RES:
            return "连续高分辨率";
        case BH1750_MODE_CONT_H_RES2:
            return "连续高分辨率2";
        case BH1750_MODE_CONT_L_RES:
            return "连续低分辨率";
        case BH1750_MODE_ONE_H_RES:
            return "单次高分辨率";
        case BH1750_MODE_ONE_H_RES2:
            return "单次高分辨率2";
        case BH1750_MODE_ONE_L_RES:
            return "单次低分辨率";
        default:
            return "未知模式";
    }
}

// 基本功能测试
int test_basic_functions() {
    printf("\n=== 基本功能测试 ===\n");
    
    // 测试电源控制
    printf("测试电源控制...\n");
    if (bh1750_power_down() != BH1750_OK) {
        printf("❌ 断电失败\n");
        return -1;
    }
    printf("✅ 断电成功\n");
    
    usleep(100000); // 100ms
    
    if (bh1750_power_on() != BH1750_OK) {
        printf("❌ 上电失败\n");
        return -1;
    }
    printf("✅ 上电成功\n");
    
    // 测试重置
    printf("测试重置功能...\n");
    if (bh1750_reset() != BH1750_OK) {
        printf("❌ 重置失败\n");
        return -1;
    }
    printf("✅ 重置成功\n");
    
    return 0;
}

// 单次读取测试
int test_single_read() {
    printf("\n=== 单次读取测试 ===\n");
    
    bh1750_data_t data;
    int ret = bh1750_read_data(&data);
    if (ret != BH1750_OK) {
        printf("❌ 读取数据失败\n");
        print_error(ret);
        return -1;
    }
    
    printf("✅ 读取成功:\n");
    printf("   光照强度: %.2f lx\n", data.lux);
    printf("   原始数据: 0x%04X (%d)\n", data.raw_data, data.raw_data);
    printf("   测量模式: %s\n", get_mode_name(data.mode));
    
    // 数据合理性检查
    if (data.lux < 0.0 || data.lux > 65535.0) {
        printf("⚠️  光照强度值可能异常: %.2f lx\n", data.lux);
    }
    
    return 0;
}

// 模式测试
int test_all_modes() {
    printf("\n=== 所有模式测试 ===\n");
    
    bh1750_mode_t modes[] = {
        BH1750_MODE_CONT_H_RES,
        BH1750_MODE_CONT_H_RES2,
        BH1750_MODE_CONT_L_RES,
        BH1750_MODE_ONE_H_RES,
        BH1750_MODE_ONE_H_RES2,
        BH1750_MODE_ONE_L_RES
    };
    
    int num_modes = sizeof(modes) / sizeof(modes[0]);
    
    for (int i = 0; i < num_modes && running; i++) {
        printf("测试模式: %s\n", get_mode_name(modes[i]));
        
        // 设置模式
        if (bh1750_set_mode(modes[i]) != BH1750_OK) {
            printf("❌ 设置模式失败\n");
            return -1;
        }
        
        // 等待测量稳定
        sleep(1);
        
        // 读取数据
        bh1750_data_t data;
        if (bh1750_read_data(&data) != BH1750_OK) {
            printf("❌ 读取数据失败\n");
            return -1;
        }
        
        printf("  光照强度: %.2f lx (原始: %d)\n", data.lux, data.raw_data);
    }
    
    printf("✅ 所有模式测试完成\n");
    return 0;
}

// 连续读取测试
int test_continuous_read() {
    printf("\n=== 连续读取测试 ===\n");
    printf("连续读取10次，每次间隔1秒...\n");
    
    // 设置为连续高分辨率模式
    if (bh1750_set_mode(BH1750_MODE_CONT_H_RES) != BH1750_OK) {
        printf("❌ 设置连续模式失败\n");
        return -1;
    }
    
    for (int i = 0; i < 10 && running; i++) {
        float lux;
        int ret = bh1750_read_lux(&lux);
        
        if (ret != BH1750_OK) {
            printf("❌ 第%d次读取失败\n", i + 1);
            print_error(ret);
            return -1;
        }
        
        // 获取当前时间
        time_t now = time(NULL);
        struct tm* tm_info = localtime(&now);
        char time_str[64];
        strftime(time_str, sizeof(time_str), "%H:%M:%S", tm_info);
        
        printf("[%s] 第%2d次: %.2f lx\n", time_str, i + 1, lux);
        
        if (i < 9) { // 最后一次不需要等待
            sleep(1);
        }
    }
    
    printf("✅ 连续读取测试完成\n");
    return 0;
}

// 精度对比测试
int test_precision_comparison() {
    printf("\n=== 精度对比测试 ===\n");
    printf("对比不同分辨率模式的测量结果...\n");
    
    bh1750_mode_t modes[] = {
        BH1750_MODE_CONT_H_RES,   // 1 lx 分辨率
        BH1750_MODE_CONT_H_RES2,  // 0.5 lx 分辨率
        BH1750_MODE_CONT_L_RES    // 4 lx 分辨率
    };
    
    const char* mode_names[] = {
        "高分辨率 (1lx)",
        "高分辨率2 (0.5lx)",
        "低分辨率 (4lx)"
    };
    
    for (int i = 0; i < 3 && running; i++) {
        printf("测试 %s 模式:\n", mode_names[i]);
        
        if (bh1750_set_mode(modes[i]) != BH1750_OK) {
            printf("❌ 设置模式失败\n");
            continue;
        }
        
        sleep(1); // 等待稳定
        
        // 连续读取3次求平均值
        float total_lux = 0.0f;
        int valid_reads = 0;
        
        for (int j = 0; j < 3; j++) {
            float lux;
            if (bh1750_read_lux(&lux) == BH1750_OK) {
                total_lux += lux;
                valid_reads++;
            }
            usleep(500000); // 500ms
        }
        
        if (valid_reads > 0) {
            float avg_lux = total_lux / valid_reads;
            printf("  平均光照强度: %.2f lx\n", avg_lux);
        } else {
            printf("  ❌ 无有效读取\n");
        }
    }
    
    printf("✅ 精度对比测试完成\n");
    return 0;
}

// 错误处理测试
int test_error_handling() {
    printf("\n=== 错误处理测试 ===\n");
    
    // 测试空指针
    printf("测试空指针处理...\n");
    int ret = bh1750_read_data(NULL);
    if (ret == BH1750_ERROR_DATA) {
        printf("✅ 空指针检查正常\n");
    } else {
        printf("❌ 空指针检查失败\n");
        return -1;
    }
    
    // 测试无效模式
    printf("测试无效模式处理...\n");
    ret = bh1750_set_mode((bh1750_mode_t)99);
    if (ret == BH1750_ERROR_MODE) {
        printf("✅ 无效模式检查正常\n");
    } else {
        printf("❌ 无效模式检查失败\n");
        return -1;
    }
    
    return 0;
}

int main() {
    printf("=== BH1750 光照传感器测试程序 ===\n");
    printf("I2C总线: %s\n", BH1750_I2C_BUS);
    printf("I2C地址: 0x%02X\n", BH1750_I2C_ADDR);
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 初始化传感器
    printf("\n正在初始化BH1750传感器...\n");
    int ret = bh1750_init();
    if (ret != BH1750_OK) {
        printf("❌ BH1750初始化失败\n");
        print_error(ret);
        printf("\n请检查:\n");
        printf("1. 传感器是否正确连接到I2C-2总线\n");
        printf("2. I2C地址是否为0x23 (ADDR引脚接地)\n");
        printf("3. 是否有足够的权限访问I2C设备\n");
        printf("4. 可以运行 'sudo i2cdetect -y 2' 检查设备\n");
        return 1;
    }
    
    // 运行测试
    int test_failed = 0;
    
    if (test_basic_functions() != 0) test_failed = 1;
    if (test_single_read() != 0) test_failed = 1;
    if (test_error_handling() != 0) test_failed = 1;
    
    // 只有在没有错误的情况下才运行耗时的测试
    if (!test_failed && running) {
        if (test_all_modes() != 0) test_failed = 1;
        if (test_continuous_read() != 0) test_failed = 1;
        if (test_precision_comparison() != 0) test_failed = 1;
    }
    
    // 清理资源
    bh1750_deinit();
    
    // 输出结果
    printf("\n=== 测试结果 ===\n");
    if (test_failed) {
        printf("❌ BH1750测试失败\n");
        return 1;
    } else {
        printf("🎉 BH1750测试全部通过!\n");
        return 0;
    }
}
