/**
 * @file test_hcsr04.cpp
 * @brief HC-SR04传感器模块单元测试
 * <AUTHOR>
 * @date 2024
 */

#include <iostream>
#include <cassert>
#include <thread>
#include <chrono>
#include "hcsr04.hpp"
#include "logger.hpp"

using namespace greenland;

class HCSR04Test {
public:
    void runAllTests() {
        std::cout << "🧪 开始HC-SR04传感器模块测试..." << std::endl;
        
        testConfiguration();
        testInitialization();
        testMeasurement();
        testWaterLevelCalculation();
        testCalibration();
        testErrorHandling();
        testUtilityFunctions();
        
        std::cout << "✅ HC-SR04传感器模块所有测试通过!" << std::endl;
    }

private:
    void testConfiguration() {
        std::cout << "⚙️ 测试配置功能..." << std::endl;
        
        // 测试默认配置
        HCSR04Config default_config = createDefaultHCSR04Config();
        assert(default_config.trig_pin == 22);
        assert(default_config.echo_pin == 23);
        assert(default_config.max_distance == 400.0f);
        assert(default_config.max_water_level == 35.0f);
        
        // 测试配置验证
        assert(validateHCSR04Config(default_config) == true);
        
        // 测试无效配置
        HCSR04Config invalid_config = default_config;
        invalid_config.trig_pin = invalid_config.echo_pin;  // 相同引脚
        assert(validateHCSR04Config(invalid_config) == false);
        
        invalid_config = default_config;
        invalid_config.max_distance = -1.0f;  // 负距离
        assert(validateHCSR04Config(invalid_config) == false);
        
        std::cout << "✅ 配置功能测试通过" << std::endl;
    }
    
    void testInitialization() {
        std::cout << "🔧 测试初始化功能..." << std::endl;
        
        // 创建日志器
        LogConfig log_config = createDefaultConfig("HCSR04Test");
        log_config.enable_file = false;
        auto logger = std::make_shared<Logger>(log_config);
        
        // 创建传感器配置
        HCSR04Config config = createDefaultHCSR04Config();
        config.enable_logging = true;
        
        // 创建传感器实例
        HCSR04Sensor sensor(config, logger);
        
        // 测试初始状态
        assert(sensor.isInitialized() == false);
        
        // 注意：在没有实际硬件的情况下，初始化可能会失败
        // 这里我们主要测试接口的正确性
        std::cout << "传感器配置: Trig=" << config.trig_pin 
                  << ", Echo=" << config.echo_pin << std::endl;
        
        // 测试配置获取
        const HCSR04Config& retrieved_config = sensor.getConfig();
        assert(retrieved_config.trig_pin == config.trig_pin);
        assert(retrieved_config.echo_pin == config.echo_pin);
        
        std::cout << "✅ 初始化功能测试通过" << std::endl;
    }
    
    void testMeasurement() {
        std::cout << "📏 测试测量功能..." << std::endl;
        
        // 创建日志器
        LogConfig log_config = createDefaultConfig("HCSR04Measurement");
        log_config.enable_file = false;
        auto logger = std::make_shared<Logger>(log_config);
        
        // 创建传感器
        HCSR04Config config = createDefaultHCSR04Config();
        HCSR04Sensor sensor(config, logger);
        
        std::cout << "注意：由于没有实际硬件，测量可能会失败，这是正常的。" << std::endl;
        
        // 尝试测量距离
        HCSR04Result distance_result = sensor.measureDistance();
        std::cout << "距离测量结果: " << (distance_result.success ? "成功" : "失败") << std::endl;
        if (!distance_result.success) {
            std::cout << "错误信息: " << distance_result.error_message << std::endl;
        } else {
            std::cout << "测量距离: " << distance_result.distance_cm << " cm" << std::endl;
        }
        
        // 尝试测量水位
        HCSR04Result water_result = sensor.measureWaterLevel();
        std::cout << "水位测量结果: " << (water_result.success ? "成功" : "失败") << std::endl;
        if (!water_result.success) {
            std::cout << "错误信息: " << water_result.error_message << std::endl;
        } else {
            std::cout << "水位高度: " << water_result.water_level_cm << " cm ("
                      << water_result.water_level_percent << "%)" << std::endl;
        }
        
        std::cout << "✅ 测量功能接口测试通过" << std::endl;
    }
    
    void testWaterLevelCalculation() {
        std::cout << "💧 测试水位计算功能..." << std::endl;
        
        HCSR04Config config = createDefaultHCSR04Config();
        config.max_water_level = 35.0f;  // 35cm最大水位
        
        // 模拟测试水位计算逻辑
        // 如果传感器距离水面10cm，那么水位应该是25cm
        float sensor_distance = 10.0f;
        float expected_water_level = config.max_water_level - sensor_distance;
        
        std::cout << "模拟计算:" << std::endl;
        std::cout << "  最大水位: " << config.max_water_level << " cm" << std::endl;
        std::cout << "  传感器距离: " << sensor_distance << " cm" << std::endl;
        std::cout << "  计算水位: " << expected_water_level << " cm" << std::endl;
        std::cout << "  水位百分比: " << (expected_water_level / config.max_water_level * 100.0f) << "%" << std::endl;
        
        assert(expected_water_level == 25.0f);
        
        // 测试边界情况
        float zero_distance = config.max_water_level;  // 传感器在水位最高点
        float zero_water_level = config.max_water_level - zero_distance;
        assert(zero_water_level == 0.0f);
        
        std::cout << "✅ 水位计算功能测试通过" << std::endl;
    }
    
    void testCalibration() {
        std::cout << "🎯 测试校准功能..." << std::endl;
        
        LogConfig log_config = createDefaultConfig("HCSR04Calibration");
        log_config.enable_file = false;
        auto logger = std::make_shared<Logger>(log_config);
        
        HCSR04Sensor sensor(createDefaultHCSR04Config(), logger);
        
        // 测试校准接口（在没有硬件的情况下会失败，但测试接口）
        std::cout << "尝试校准传感器..." << std::endl;
        HCSR04Error calibration_result = sensor.calibrate(20.0f);
        
        std::cout << "校准结果: " << hcsr04ErrorToString(calibration_result) << std::endl;
        
        std::cout << "✅ 校准功能接口测试通过" << std::endl;
    }
    
    void testErrorHandling() {
        std::cout << "❌ 测试错误处理..." << std::endl;
        
        LogConfig log_config = createDefaultConfig("HCSR04Error");
        log_config.enable_file = false;
        auto logger = std::make_shared<Logger>(log_config);
        
        // 测试无效配置
        HCSR04Config invalid_config;
        invalid_config.trig_pin = -1;  // 无效引脚
        invalid_config.echo_pin = -1;
        
        HCSR04Sensor sensor(invalid_config, logger);
        
        // 尝试初始化应该失败
        HCSR04Error init_result = sensor.initialize();
        std::cout << "无效配置初始化结果: " << hcsr04ErrorToString(init_result) << std::endl;
        assert(init_result != HCSR04Error::OK);
        
        // 测试未初始化状态下的测量
        HCSR04Result measurement_result = sensor.measureDistance();
        assert(measurement_result.success == false);
        std::cout << "未初始化测量错误: " << measurement_result.error_message << std::endl;
        
        std::cout << "✅ 错误处理测试通过" << std::endl;
    }
    
    void testUtilityFunctions() {
        std::cout << "🔧 测试工具函数..." << std::endl;
        
        // 测试错误码转字符串
        assert(hcsr04ErrorToString(HCSR04Error::OK) == "成功");
        assert(hcsr04ErrorToString(HCSR04Error::INIT_ERROR) == "初始化错误");
        assert(hcsr04ErrorToString(HCSR04Error::GPIO_ERROR) == "GPIO错误");
        assert(hcsr04ErrorToString(HCSR04Error::TIMEOUT_ERROR) == "超时错误");
        assert(hcsr04ErrorToString(HCSR04Error::PARAM_ERROR) == "参数错误");
        assert(hcsr04ErrorToString(HCSR04Error::NOT_INITIALIZED) == "未初始化");
        
        std::cout << "✅ 工具函数测试通过" << std::endl;
    }
};

int main() {
    try {
        HCSR04Test test;
        test.runAllTests();
        
        std::cout << std::endl;
        std::cout << "🎉 HC-SR04传感器模块测试完成!" << std::endl;
        std::cout << "注意：部分功能需要实际硬件才能完全测试。" << std::endl;
        std::cout << "当前测试主要验证了接口的正确性和错误处理。" << std::endl;
        
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "❌ 测试失败: " << e.what() << std::endl;
        return 1;
    }
}
