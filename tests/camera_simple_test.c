/**
 * @file camera_simple_test.c
 * @brief 摄像头模块简单测试程序
 * <AUTHOR>
 * @date 2024
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include "camera.h"

static volatile int running = 1;
static camera_t camera = NULL;

// 信号处理函数
void signal_handler(int sig) {
    printf("\n收到退出信号，正在停止...\n");
    running = 0;

    if (camera) {
        camera_stop_recording(camera);
    }
}

int main() {
    printf("📷 摄像头简单测试\n");
    printf("==================\n\n");

    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 扫描摄像头设备
    printf("🔍 扫描摄像头设备...\n");
    int devices[4];
    int count = camera_scan_devices(devices, 4);

    if (count == 0) {
        printf("❌ 未找到摄像头设备\n");
        printf("请检查:\n");
        printf("  1. USB摄像头是否已连接\n");
        printf("  2. 设备权限是否正确\n");
        printf("  3. 驱动是否已安装\n");
        return 1;
    }

    printf("✅ 找到 %d 个摄像头设备\n", count);
    for (int i = 0; i < count; i++) {
        printf("  设备 %d: /dev/video%d\n", i, devices[i]);
    }
    printf("\n");

    // 创建摄像头配置
    camera_config_t config;
    camera_get_default_config(&config);
    config.device_id = devices[0];  // 使用第一个设备
    config.resolution = CAMERA_RES_HD;  // 使用HD分辨率
    strcpy(config.save_path, "Media");

    printf("📷 摄像头配置:\n");
    printf("  设备ID: %d\n", config.device_id);
    printf("  分辨率: %s\n", camera_resolution_to_string(config.resolution));
    printf("  帧率: %d fps\n", config.fps);
    printf("  保存路径: %s\n", config.save_path);
    printf("\n");

    // 创建摄像头实例
    printf("🔧 创建摄像头实例...\n");
    camera = camera_create(&config);
    if (!camera) {
        printf("❌ 创建摄像头实例失败\n");
        return 1;
    }

    // 初始化摄像头
    printf("🔧 初始化摄像头...\n");
    int result = camera_init(camera);
    if (result != CAMERA_OK) {
        printf("❌ 摄像头初始化失败: %s\n", camera_error_to_string(result));
        camera_destroy(camera);
        return 1;
    }

    printf("✅ 摄像头初始化成功\n\n");

    // 获取摄像头信息
    camera_info_t info;
    camera_get_info(camera, &info);
    printf("📊 摄像头信息:\n");
    printf("  实际分辨率: %dx%d\n", info.width, info.height);
    printf("  实际帧率: %d fps\n", info.fps);
    printf("  连接状态: %s\n", info.is_connected ? "已连接" : "未连接");
    printf("\n");

    // 测试拍照
    printf("📸 测试拍照功能...\n");
    result = camera_quick_photo(camera, "simple_test_photo");
    if (result == CAMERA_OK) {
        printf("✅ 拍照成功: Media/simple_test_photo.jpg\n");
    } else {
        printf("❌ 拍照失败: %s\n", camera_error_to_string(result));
    }
    printf("\n");

    // 测试录像
    printf("🎥 测试录像功能 (3秒)...\n");
    result = camera_quick_video(camera, "simple_test_video", 3);
    if (result == CAMERA_OK) {
        printf("✅ 录像开始\n");

        // 显示倒计时
        for (int i = 3; i > 0 && running; i--) {
            printf("录像中... %d 秒\r", i);
            fflush(stdout);
            sleep(1);
        }
        printf("\n");

        // 等待录像完成
        while (camera_get_state(camera) == CAMERA_STATE_RECORDING && running) {
            usleep(100000);  // 100ms
        }

        printf("✅ 录像完成: Media/simple_test_video.mp4\n");
    } else {
        printf("❌ 录像失败: %s\n", camera_error_to_string(result));
    }
    printf("\n");

    // 显示最终统计
    camera_get_info(camera, &info);
    printf("📊 测试结果:\n");
    printf("  拍照数量: %lu 张\n", info.photos_taken);
    printf("  录像数量: %lu 个\n", info.videos_recorded);
    printf("  捕获帧数: %lu 帧\n", info.frames_captured);
    printf("  文件大小: %.2f MB\n", info.total_file_size / (1024.0 * 1024.0));
    printf("\n");

    // 清理资源
    printf("🧹 清理资源...\n");
    camera_destroy(camera);

    printf("✅ 简单测试完成\n");
    printf("\n💡 提示:\n");
    printf("  - 检查 Media/ 目录中的生成文件\n");
    printf("  - 如需更多功能，请运行 camera_test\n");

    return 0;
}
