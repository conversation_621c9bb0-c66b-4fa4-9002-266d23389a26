# GreenLand 项目 Git 忽略文件

# 编译生成的文件
bin/
build/
lib/
*.o
*.so
*.a
*.d

# 可执行文件
GreenLand
*_test
*_example

# 日志文件
Log/
*.log

# 临时文件
*.tmp
*.temp
*~
.DS_Store

# IDE 和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 系统文件
Thumbs.db
.directory

# 测试报告
test_reports/
*.tar.gz

# 备份文件
*.bak
*.backup

# 调试文件
core
*.core
vgcore.*

# 配置文件（如果包含敏感信息）
config.local
*.secret

# 外部依赖（如果使用包管理器）
external/downloads/
external/build/

# 文档生成文件
doc/html/
doc/latex/

# 压缩文件
*.zip
*.tar
*.gz
*.bz2
*.xz

# Python 缓存（如果有 Python 脚本）
__pycache__/
*.pyc
*.pyo

# Node.js（如果有前端组件）
node_modules/
npm-debug.log*

# 特定于项目的忽略
# 添加任何特定于 GreenLand 项目的文件或目录
